{
  "ConnectionStrings": {
    "DefaultConnection": "",
    "MultiEmpresaConnection": ""
  },
  "StorageSettings": {
    "Connection": "DefaultEndpointsProtocol=https;AccountName=sti3gestao;AccountKey=****************************************************************************************;BlobEndpoint=https://sti3gestao.blob.core.windows.net/;QueueEndpoint=https://sti3gestao.queue.core.windows.net/;TableEndpoint=https://sti3gestao.table.core.windows.net/;FileEndpoint=https://sti3gestao.file.core.windows.net/;"
  },
  "JwtSettings": {
    "Chave": "5P2Py9hgQPPVwCmfDmgkrSHmUkTT8LgKeFCnmLSSBgALbURm7JYBbgg53qpKXLKe678nUxwJLtBHysUVdASxA5PC3cq2f4r58yDuNRH4K8tZUfv9jBCWuYA87ZqY5MLNdrAe2e7kXHGkGBkhe6MLh5EZjvKMJd6SVHxMrNs5v6hhP8mXYbT4a8fuS2SLvVkxkpwrHGgTt3Uen23PEmUaPZb57ca7e7YZAhPHMLDynFKvPgjN5mraNkRD97hKdRFY",
    "ExpiracaoMinutos": 20,
    "Emissor": "Zendar-Test",
    "ValidoEm": "https://localhost:44320"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
      //"Microsoft.EntityFrameworkCore.Database.Command": "None", // Debug ou None - Log query do banco
      //"Hangfire": "Trace"
    }
  },
  "AllowedHosts": "*",
  "RedisSettings": {
    "Endpoint": "",
    "Port": "",
    "Password": "",
    "Configuration": "",
    "InstanceName": ""
  }
}