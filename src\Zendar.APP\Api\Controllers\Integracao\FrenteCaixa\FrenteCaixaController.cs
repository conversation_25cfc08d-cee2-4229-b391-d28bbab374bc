﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.APP.Constants;
using Zendar.APP.Extensions;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.V1.IntegracaoServices.FrenteCaixa.Interfaces;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Fiscal;
using Zendar.Business.ViewModels.Integracao;
using Zendar.Business.ViewModels.Integracao.FrenteCaixa;
using Zendar.Business.ViewModels.V1.Fiscal;
using Zendar.Business.ViewModels.V1.Integracao.AutoAtendimento;
using Zendar.Business.ViewModels.V1.Integracao.FrenteCaixa;
using Zendar.Business.ViewModels.V2.CategoriaProdutoViewModels;
using Zendar.Business.ViewModels.V2.CategoriaProdutoViewModels.CategoriaProdutoComplementoViewModels;
using Zendar.Business.ViewModels.V2.Departamentos;
using Zendar.Business.ViewModels.V2.Entregadores;
using Zendar.Business.ViewModels.V2.FichaTecnica;
using Zendar.Business.ViewModels.V2.GerenciadorDeImpressao;
using Zendar.Business.ViewModels.V2.ProdutoPorEtapa;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;
using Zendar.Business.ViewModels.V2.Promocao;
using Zendar.Business.ViewModels.V2.SetorEntrega;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces;
using Zendar.Data.ViewModels.Dispositivo;
using Zendar.Data.ViewModels.Integracao.FrenteCaixa;
using Zendar.Integracao.ViewModel;

namespace Zendar.APP.Api.Controllers.Integracao.FrenteCaixa
{
    [ApiController]
    [Route("api/[controller]")]
    public class FrenteCaixaController : MainController
    {
        private readonly IDatabaseTransaction _databaseTransaction;
        private readonly IIntegracaoFrenteCaixaService _integracaoFrenteCaixaService;
        private readonly IIntegracaoCaixaFrenteCaixaService _integracaoCaixaFrenteCaixaService;

        public FrenteCaixaController(
            INotificador notificador,
            IDatabaseTransaction databaseTransaction,
            IIntegracaoFrenteCaixaService integracaoFrenteCaixaService,
            IIntegracaoCaixaFrenteCaixaService integracaoCaixaFrenteCaixaService) : base(notificador)
        {
            _databaseTransaction = databaseTransaction;
            _integracaoFrenteCaixaService = integracaoFrenteCaixaService;
            _integracaoCaixaFrenteCaixaService = integracaoCaixaFrenteCaixaService;
        }

        #region Integracao

        [HttpGet(Endpoints.FrenteCaixaObterIntegracao)]
        [ClaimsAuthorize]
        public async Task<ActionResult<IntegracaoObterViewModel>> ObterIntegracao()
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterIntegracao();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaCadastrarIntegracao)]
        [ClaimsAuthorize]
        public async Task<ActionResult<Guid?>> CadastrarIntegracao(
            [FromBody] IntegracaoViewModel integracaoViewModel)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.CadastrarIntegracao(integracaoViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterConfiguracaoParaAlterar)]
        [ClaimsAuthorize]
        public async Task<ActionResult<FrenteCaixaConfiguracaoAlterarViewModel>> ObterConfiguracaoParaAlterar()
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterConfiguracaoParaAlterar();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaAlterarConfiguracao)]
        [ClaimsAuthorize]
        public async Task<ActionResult<Guid?>> AlterarConfiguracao(
            [FromBody] FrenteCaixaConfiguracaoAlterarViewModel configuracaoAlterarViewModel)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.AlterarConfiguracao(configuracaoAlterarViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterConfiguracaoTefParaAlterar)]
        [ClaimsAuthorize]
        public async Task<ActionResult<FrenteCaixaConfiguracaoTefAlterarViewModel>> ObterConfiguracaoTefParaAlterar()
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterConfiguracaoTefParaAlterar();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaAlterarConfiguracaoTef)]
        [ClaimsAuthorize]
        public async Task<ActionResult<Guid?>> AlterarConfiguracaoTef(
            [FromBody] FrenteCaixaConfiguracaoTefAlterarViewModel configuracaoTefAlterarViewModel)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.AlterarConfiguracaoTef(configuracaoTefAlterarViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterConfiguracaoTabelaPrecoParaAlterar)]
        [ClaimsAuthorize]
        public async Task<ActionResult<FrenteCaixaConfiguracaoTabelaPrecoAlterarViewModel>> ObterConfiguracaoTabelaPrecoParaAlterar()
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterConfiguracaoTabelaPrecoParaAlterar();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaAlterarConfiguracaoTabelaPreco)]
        [ClaimsAuthorize]
        public async Task<ActionResult<Guid?>> AlterarConfiguracaoTabelaPreco(
            [FromBody] FrenteCaixaConfiguracaoTabelaPrecoAlterarViewModel configuracaoTabelaPrecoAlterarViewModel)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.AlterarConfiguracaoTabelaPreco(configuracaoTabelaPrecoAlterarViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaDesistir)]
        [ClaimsAuthorize]
        public async Task<ActionResult> Desistir()
        {
            try
            {
                await _integracaoFrenteCaixaService.Desistir();
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }

            return CustomResponse();
        }

        [HttpGet(Endpoints.FrenteCaixaClienteFornecedorRegra)]
        public async Task<ActionResult<FrenteCaixaClienteFornecedorRegraViewModel>> ObterClienteFornecedorRegra(
            [FromRoute] Guid clienteFornecedorId)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterClienteFornecedorRegra(clienteFornecedorId);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaOperacaoTef)]
        public async Task<ActionResult<List<FrenteCaixaTefViewModel>>> ObterListaOperacaoTef()
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaOperacaoTef();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaParcelamentoTef)]
        public async Task<ActionResult<List<FrenteCaixaTefViewModel>>> ObterListaParcelamentoTef()
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaParcelamentoTef();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaTipoCartaoTef)]
        public async Task<ActionResult<List<FrenteCaixaTefViewModel>>> ObterListaTipoCartaoTef()
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaTipoCartaoTef();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaGerarPin)]
        [ClaimsAuthorize]
        public async Task<ActionResult<string>> GerarPin()
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.GerarPinDispositivo();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterQuantidadeAtivaDispositivo)]
        [ClaimsAuthorize]
        public async Task<ActionResult<FrenteCaixaQuantidadeAtivaDispositivoViewModel>> ObterQuantidadeAtivaDispositivo()
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterQuantidadeAtivaDispositivo();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaDispositivo)]
        [ClaimsAuthorize]
        public ActionResult<GridPaginadaRetorno<DispositivoPaginadoViewModel>> ObterListaDispositivo(
            [FromQuery] GridPaginadaConsulta gridPaginada,
            string nome,
            bool? ativo = null)
        {
            try
            {
                var result =
                    _integracaoFrenteCaixaService.ObterListaDispositivo(gridPaginada, nome, ativo);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPut(Endpoints.FrenteCaixaInativarDispositivo)]
        [ClaimsAuthorize]
        public async Task<ActionResult> InativarDispositivo(
            [FromQuery] Guid id)
        {
            try
            {
                await _integracaoFrenteCaixaService.InativarDispositivo(id);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, id);
            }

            return CustomResponse();
        }

        [HttpPut(Endpoints.FrenteCaixaAtivarDispositivo)]
        [ClaimsAuthorize]
        public async Task<ActionResult> AtivarDispositivo(
            [FromQuery] Guid id)
        {
            try
            {
                await _integracaoFrenteCaixaService.AtivarDispositivo(id);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, id);
            }

            return CustomResponse();
        }

        #endregion

        #region Sincronizacao

        [HttpGet(Endpoints.FrenteCaixaObterDispositivo)]
        public async Task<ActionResult<FrenteCaixaDispositivoViewModel>> ObterDispositivo(
            [FromQuery] string pin,
            [FromQuery] string identificador,
            [FromQuery] string apelido)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterDispositivo(pin, identificador, apelido);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, pin, identificador, apelido);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaAtivarLicenca)]
        public async Task<ActionResult<FrenteCaixaDispositivoViewModel>> AtivarLicenca(
            [FromBody] FrenteCaixaAtivarLicencaViewModel ativarLicencaViewModel)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.AtivarLicenca(ativarLicencaViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPut(Endpoints.FrenteCaixaMarcarDispositivoComDataHoraUltimaSincronizacao)]
        public async Task<ActionResult<FrenteCaixaDispositivoViewModel>> MarcarDispositivoComDataHoraUltimaSincronizacao(
        [FromQuery] Guid dispositivoId)
        {
            try
            {
                await _integracaoFrenteCaixaService.MarcarDispositivoComDataHoraUltimaSincronizacao(dispositivoId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }

            return CustomResponse();
        }

        [HttpGet(Endpoints.FrenteCaixaObterDadosPin)]
        public async Task<ActionResult<FrenteCaixaDispositivoViewModel>> ObterDadosPin(
            [FromRoute] string pin)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterDadosPin(pin);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaAbrirCaixaDispositivo)]
        public async Task<ActionResult<Guid?>> AbrirPorDispositivo(
            [FromBody] AbrirFecharCaixaDispositivoViewModel abrirFecharCaixaDispositivoViewModel)
        {
            try
            {
                var result =
                    await _integracaoCaixaFrenteCaixaService.AbrirPorDispositivo(abrirFecharCaixaDispositivoViewModel, ReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaReabrirPorDispositivo)]
        public async Task<ActionResult<Guid?>> ReabrirPorDispositivo(
            [FromBody] AbrirFecharCaixaDispositivoViewModel abrirFecharCaixaDispositivoViewModel)
        {
            try
            {
                var result =
                    await _integracaoCaixaFrenteCaixaService.ReabrirPorDispositivo(abrirFecharCaixaDispositivoViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaFecharCaixaDispositivo)]
        public async Task<ActionResult<Guid?>> FecharPorDispositivo(
            [FromBody] AbrirFecharCaixaDispositivoViewModel abrirFecharCaixaDispositivoViewModel)
        {
            try
            {
                var result =
                    await _integracaoCaixaFrenteCaixaService.FecharPorDispositivo(abrirFecharCaixaDispositivoViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaTransferencia)]
        public async Task<ActionResult<Guid?>> Transferencia(
         [FromBody] TransferenciaViewModel transferenciaViewModel)
        {
            try
            {
                var result =
                    await _integracaoCaixaFrenteCaixaService.Transferencia(transferenciaViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaVincularAgrupamento)]
        public async Task<ActionResult<Guid?>> VincularAgrupamentoIntegracaoCaixaMovimentacao(
            [FromBody] VincularAgrupamentoIntegracaoCaixaMovimentacaoViewModel vincularAgrupamentoIntegracaoCaixaMovimentacaoViewModel)
        {
            try
            {
                var result =
                    await _integracaoCaixaFrenteCaixaService.VincularAgrupamento(vincularAgrupamentoIntegracaoCaixaMovimentacaoViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaEnviarVenda)]
        public async Task<ActionResult<Guid?>> EnviarVenda(
            OperacaoIntegracaoViewModel operacaoIntegracaoViewModel)
        {
            try
            {
                _databaseTransaction.BeginTransaction();

                var result =
                    await _integracaoFrenteCaixaService.EnviarVenda(operacaoIntegracaoViewModel);

                if (!PossuiAvisos() && !PossuiErros())
                    _databaseTransaction.Commit();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                _databaseTransaction.Rollback();

                NotificarErro(ex, enviarEmail: false, operacaoIntegracaoViewModel);
                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaEnviarNotaFiscal)]
        public async Task<ActionResult<Guid?>> EnviarNotaFiscal(
            NotaFiscalViewModel notaFiscalViewModel)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.EnviarNotaFiscal(notaFiscalViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, notaFiscalViewModel);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaEnviarCupomSat)]
        public async Task<ActionResult<Guid?>> EnviarCupomSat(
            CupomSatViewModel cupomSatViewModel)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.EnviarCupomSat(cupomSatViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, cupomSatViewModel);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.FrenteCaixaEnviarInutilizacao)]
        public async Task<ActionResult<bool>> EnviarInutilizacao(
            EnviarInutilizacaoViewModel enviarInutilizacaoViewModel)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.EnviarInutilizacao(enviarInutilizacaoViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, enviarInutilizacaoViewModel);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterConfiguracao)]
        public async Task<ActionResult<FrenteCaixaConfiguracaoViewModel>> ObterConfiguracao()
        {
            try
            {
                var result = await _integracaoFrenteCaixaService.ObterConfiguracao();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaLoja)]
        public async Task<ActionResult<List<LojaViewModel>>> ObterListaLoja(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaLoja(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaMarca)]
        public async Task<ActionResult<List<MarcaViewModel>>> ObterListaMarca(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaMarca(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaCor)]
        public async Task<ActionResult<List<CorViewModel>>> ObterListaCor(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaCor(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaTamanho)]
        public async Task<ActionResult<List<TamanhoViewModel>>> ObterListaTamanho(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaTamanho(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaCategoria)]
        public async Task<ActionResult<List<CategoriaProdutoViewModel>>> ObterListaCategoria(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaCategoria(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaObservacao)]
        public async Task<ActionResult<List<ProdutoObservacaoV2ViewModel>>> ObterListaObservacao(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaObservacao(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaCategoriaObservacao)]
        public async Task<ActionResult<List<CategoriaProdutoObservacaoViewModel>>> ObterListaCategoriaObservacao(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaCategoriaObservacao(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaCategoriaComplemento)]
        public async Task<ActionResult<List<CategoriaProdutoComplementoViewModel>>> ObterListaCategoriaComplemento(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaCategoriaComplemento(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaUnidade)]
        public async Task<ActionResult<List<UnidadeMedidaViewModel>>> ObterListaUnidade(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaUnidade(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaFatorConversao)]
        public async Task<ActionResult<List<FrenteCaixaFatorConversaoViewModel>>> ObterListaFatorConversao(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaFatorConversao(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaAliquotaIcms)]
        public async Task<ActionResult<List<FrenteCaixaAliquotaIcmsViewModel>>> ObterListaAliquotaIcms(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaAliquotaIcms(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaVendedor)]
        public async Task<ActionResult<List<VendedorViewModel>>> ObterListaVendedor(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaVendedor(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaUsuario)]
        public async Task<ActionResult<List<FrenteCaixaUsuarioViewModel>>> ObterListaUsuario(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaUsuario(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaUsuarioPermissao)]
        public async Task<ActionResult<List<FrenteCaixaUsuarioPermissaoViewModel>>> ObterListaUsuarioPermissao(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaUsuarioPermissao(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaFormaPagamentoRecebimento)]
        public async Task<ActionResult<List<FormaPagamentoRecebimentoViewModel>>> ObterListaFormaPagamentoRecebimento(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaFormaPagamentoRecebimento(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaRegraFiscal)]
        public async Task<ActionResult<List<RegraFiscalViewModel>>> ObterListaRegraFiscal(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaRegraFiscal(null, dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaCliente)]
        public async Task<ActionResult<List<ClienteFornecedorViewModel>>> ObterListaCliente(
            [FromQuery] GridPaginadaConsulta gridPaginada,
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaCliente(gridPaginada, dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaProduto)]
        public async Task<ActionResult<List<ProdutoV2ViewModel>>> ObterListaProduto(
            [FromQuery] GridPaginadaConsulta gridPaginada,
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaProduto(gridPaginada, dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaSetorEntrega)]
        public async Task<ActionResult<List<SetorEntregaViewModel>>> ObterListaSetorEntrega(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaSetorEntrega(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaPromocao)]
        public async Task<ActionResult<List<PromocaoViewModel>>> ObterListaPromocao(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaPromocao(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaDepartamento)]
        public async Task<ActionResult<List<DepartamentoViewModel>>> ObterListaDepartamento(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaDepartamento(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaEntregador)]
        public async Task<ActionResult<List<EntregadorObterViewModel>>> ObterListaEntregador(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaEntregador(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaProdutoFichaTecnica)]
        public async Task<ActionResult<List<ProdutoFichaTecnicaViewModel>>> ObterListaProdutoFichaTecnica(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaProdutoFichaTecnica(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaEtapaProduto)]
        public async Task<ActionResult<List<EtapaProdutoObterViewModel>>> ObterListaEtapaProduto(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaEtapaProduto(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaLojaServico)]
        public async Task<ActionResult<List<LojaServicoViewModel>>> ObterListaLojaServico(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaLojaServico(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaConfiguracaoGerenciadorImpressao)]
        public async Task<ActionResult<List<ConfiguracaoGerenciadorImpressaoObterViewModel>>> ObterListaConfiguracaoGerenciadorImpressao(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaConfiguracaoGerenciadorImpressao(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaGerenciadorImpressao)]
        public async Task<ActionResult<List<GerenciadorImpressaoObterViewModel>>> ObterListaGerenciadorImpressao(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaGerenciadorImpressao(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaDepartamentoImpressora)]
        public async Task<ActionResult<List<DepartamentoImpressoraObterViewModel>>> ObterListaDepartamentoImpressora(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaDepartamentoImpressora(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaImpressora)]
        public async Task<ActionResult<List<ImpressoraObterViewModel>>> ObterListaImpressora(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaImpressora(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaMarketplace)]
        public async Task<ActionResult<List<MarketplaceViewModel>>> ObterListaMarketplace(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaMarketplace(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaCredenciadoraCartao)]
        public async Task<ActionResult<List<CredenciadoraCartaoViewModel>>> ObterListaCredenciadoraCartao(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaCredenciadoraCartao(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaPeriodoCaixa)]
        public async Task<ActionResult<List<PeriodoCaixaViewModel>>> ObterListaPeriodoCaixa(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaPeriodoCaixa(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaRelatorioPersonalizado)]
        public async Task<ActionResult<List<RelatorioPersonalizadoViewModel>>> ObterListaRelatorioPersonalizado(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaRelatorioPersonalizado(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaAutoAtendimento)]
        public async Task<ActionResult<List<AutoAtendimentoArquivoViewModel>>> ObterListaAutoAtendimento()
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaAutoAtendimento();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaNotificacaoPorDispositivo)]
        public async Task<ActionResult<List<SincronizarNotificacaoViewModel>>> ObterListaNotificacaoPorDispositivo(
            [FromQuery] Guid dispositivoId)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaNotificacaoPorDispositivo(dispositivoId);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPut(Endpoints.FrenteCaixaMarcarNotificacaoComoSincronizada)]
        public async Task<ActionResult> MarcarNotificacaoComoSincronizada(
            [FromQuery] Guid notificacaoId)
        {
            try
            {
                await _integracaoFrenteCaixaService.MarcarNotificacaoComoSincronizada(notificacaoId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, notificacaoId);
            }

            return CustomResponse();
        }

        [HttpGet(Endpoints.FrenteCaixaObterListaNotificacaoExclusaoPorDispositivo)]
        public async Task<ActionResult<List<SincronizarNotificacaoExclusaoViewModel>>> ObterListaNotificacaoExclusaoPorDispositivo(
            [FromQuery] Guid dispositivoId)
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterListaNotificacaoExclusaoPorDispositivo(dispositivoId);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPut(Endpoints.FrenteCaixaMarcarNotificacaoExclusaoComoSincronizada)]
        public async Task<ActionResult> MarcarNotificacaoExclusaoComoSincronizada(
            [FromQuery] Guid notificacaoId)
        {
            try
            {
                await _integracaoFrenteCaixaService.MarcarNotificacaoExclusaoComoSincronizada(notificacaoId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, notificacaoId);
            }

            return CustomResponse();
        }

        [HttpPost(Endpoints.FrenteCaixaCadastrarItemRemovido)]
        public async Task<ActionResult<Guid?>> CadastrarItemRemovido([FromBody] FrenteCaixaOperacaoItemRemovido item)
        {
            try
            {
                var result = await _integracaoFrenteCaixaService.CadastrarItemRemovido(item);
                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, item);
                return CustomResponse();
            }
        }

        #endregion

        #region Remover

        [HttpPost(Endpoints.FrenteCaixaNotificarExportacao)]
        public async Task<ActionResult> NotificarExportacao(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                await _integracaoFrenteCaixaService.NotificarExportacao(notificacaoViewModel);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, notificacaoViewModel);
            }

            return CustomResponse();
        }

        [HttpPost(Endpoints.FrenteCaixaNotificarScript)]
        public async Task<ActionResult> NotificarScript(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                await _integracaoFrenteCaixaService.NotificarScript(notificacaoViewModel);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, notificacaoViewModel);
            }

            return CustomResponse();
        }

        [HttpGet(Endpoints.FrenteCaixaObterUrlScript)]
        [ClaimsAuthorize]
        public async Task<ActionResult<string>> ObterUrlScript()
        {
            try
            {
                var result =
                    await _integracaoFrenteCaixaService.ObterUrlScript();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        #endregion
    }
}