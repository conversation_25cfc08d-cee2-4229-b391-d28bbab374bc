using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;
using System;
using Zendar.Business.Consts;

namespace zendar_trigger;

public static class RedisConfig
{
	public static IServiceCollection AddRedisConfig(this IServiceCollection services)
	{
		var endpoint = Environment.GetEnvironmentVariable(SystemConst.REDIS_ENDPOINT);
		var port = Environment.GetEnvironmentVariable(SystemConst.REDIS_PORT);
		var password = Environment.GetEnvironmentVariable(SystemConst.REDIS_PASSWORD);

		services.AddStackExchangeRedisCache(options =>
		{
			options.ConfigurationOptions = new ConfigurationOptions
			{
				EndPoints = { { endpoint, int.Parse(port) } },
				//User = "default",
				Password = password
			};
		});

		return services;
	}
}
