import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import OptionType from 'types/optionType';

import StatusConsultaEnum from 'constants/enum/statusConsulta';
import TipoFiltroProdutoEstoqueEnum from 'constants/enum/tipoFiltroProdutoEstoque';
import ConstanteMensagemValidacao from 'constants/mensagensValidacoes';

export type CamposPersonalizados = {
  campoPersonalizadoId: string;
  valor: string;
};

export type FormData = {
  categoriasProduto?: string[];
  cores?: string[];
  marcas?: string[];
  tamanhos?: string[];
  tags?: string[];
  produtoId?: OptionType<string>;
  localEstoqueIds?: string[];
  statusConsulta: number;
  tipoEstoque: number;
  tipoRelatorio: number;
  camposPersonalizados: CamposPersonalizados[];
};

export const valoresPadraoRelatorioEstoque = {
  statusConsulta: StatusConsultaEnum.TODOS,
  tipoEstoque: TipoFiltroProdutoEstoqueEnum.TODOS,
  localEstoqueIds: undefined,
};

const schema = yup.object().shape({
  tipoRelatorio: yup
    .number()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
});

export const yupResolver = yupResolverInstance(schema);
