import { Button, Flex } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';

import { CustomFormStandardComponent } from 'components/update/CustomFormStandard';

import { useRelatorioEstoque } from './hooks';
import {
  FormData,
  yupResolver,
  valoresPadraoRelatorioEstoque,
} from './validationForms';

export const RelatorioEstoque = () => {
  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: valoresPadraoRelatorioEstoque,
  });

  const {
    isLoading,
    listaRelatoriosEstoque,
    obterOpcoesLocaisDeEstoque,
    handleGerarRelatorio,
  } = useRelatorioEstoque(formMethods);

  return (
    <FormProvider {...formMethods}>
      <CustomFormStandardComponent
        type="Estoque"
        isLoading={isLoading}
        optionsRelatorio={listaRelatoriosEstoque}
        obterOpcoesLocaisDeEstoque={obterOpcoesLocaisDeEstoque}
      />
      <Flex
        flexDirection={{ base: 'column', md: 'row', lg: 'row' }}
        justifyContent="end"
      >
        <Button
          variant="outline"
          w={{ base: 'full', sm: '250px' }}
          h="10"
          borderRadius="md"
          color="gray.900"
          mr={{ base: '0', md: '6', lg: '10' }}
          mt={{ base: '6', md: '6', lg: '10' }}
          onClick={() => handleGerarRelatorio('CSV')}
        >
          Exportar arquivo CSV
        </Button>
        <Button
          variant="unstyled"
          w={{ base: 'full', sm: '250px' }}
          h="10"
          borderRadius="md"
          color="gray.900"
          bg="secondary.400"
          mt={{ base: '6', md: '6', lg: '10' }}
          onClick={() => handleGerarRelatorio('PDF')}
        >
          Gerar Relatório
        </Button>
      </Flex>
    </FormProvider>
  );
};
