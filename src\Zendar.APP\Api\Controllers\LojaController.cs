﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.APP.Constants;
using Zendar.APP.Extensions;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.LojaServices.CadastrarLojaService;
using Zendar.Business.Services.V1.LojaServices.AtualizarServicosService;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Integracao.STi3Dashboard;
using Zendar.Business.ViewModels.Loja;
using Zendar.Data.Helpers;
using Zendar.Data.ViewModels;
using ZendarPackage.NotaFiscal.Enums;

namespace Zendar.APP.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LojaController : MainController
    {
        private readonly ILojaService _lojaService;
        private readonly ICadastrarLojaService _cadastrarLojaService;
        private readonly IAtualizarServicosService _atualizarServicosService;

        public LojaController(ILojaService lojaService,
                             INotificador notificador,
                             ICadastrarLojaService cadastrarLojaService,
                             IAtualizarServicosService atualizarServicosService) : base(notificador)
        {
            _lojaService = lojaService;
            _cadastrarLojaService = cadastrarLojaService;
            _atualizarServicosService = atualizarServicosService;
        }

        [HttpGet(Endpoints.ListarLojaComEnderecoELocalEstoque)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<List<LojaEnderecoListaViewModel>>> ListarLojaComEndereco()
        {
            try
            {
                return CustomResponse(await _lojaService.ListarLojaComEnderecoELocalEstoque());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ListarLojaTransferenciaEstoque)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<List<LojaLocalEstoqueListaViewModel>>> ListarLojaTransferenciaEstoque()
        {
            try
            {
                return CustomResponse(await _lojaService.ListarLojaTransferenciaEstoque());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ListarLojaUsuario)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<List<LojaVendedorListaViewModel>>> ListarLojaUsuario(Guid usuarioId)
        {
            try
            {
                return CustomResponse(await _lojaService.ListarLojaUsuario(usuarioId));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, usuarioId);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ListarLojaVendedor)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<List<LojaVendedorListaViewModel>>> ListarLojaVendedor(Guid? vendedorId = null)
        {
            try
            {
                return CustomResponse(await _lojaService.ListarLojaVendedor(vendedorId));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, vendedorId);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ListarLojaTabelaPreco)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<List<LojaTabelaPrecoViewModel>>> ListarLojaTabelaPreco(Guid? tabelaPrecoId = null)
        {
            try
            {
                return CustomResponse(await _lojaService.ListarLojaTabelaPreco(tabelaPrecoId));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, tabelaPrecoId);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ListarTodosAtivos)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<List<LojaAtivaListaViewModel>>> ListarTodasLojasAtivas()
        {
            try
            {
                return CustomResponse(await _lojaService.ListarTodasLojasAtivas());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ObterNumerosSerie)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<LojaNumeroSerieViewModel>> ObterNumerosSerie()
        {
            try
            {
                return CustomResponse(await _lojaService.ObterNumerosSerie());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ObterCidade)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<LojaCidadeViewModel>> ObterCidade()
        {
            try
            {
                return CustomResponse(await _lojaService.ObterCidade());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ObterInformacoesFiscais)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<LojaInformacoesFiscaisViewModel>> ObterInformacoesFiscais()
        {
            try
            {
                return CustomResponse(await _lojaService.ObterInformacoesFiscais());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ObterRegraFiscalLoja)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<LojaRegraFiscalViewModel>> ObterRegraFiscalLoja(Guid lojaId)
        {
            try
            {
                return CustomResponse(await _lojaService.ObterRegraFiscalLoja(lojaId));
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ListarPaginado)]
        [ClaimsAuthorize(Permissoes.LojaListar)]
        public ActionResult<GridPaginadaRetorno<LojaPaginadaViewModel>> ListarPaginado([FromQuery] GridPaginadaConsulta gridPaginada, string fantasia)
        {
            try
            {
                return CustomResponse(_lojaService.ListarPaginado(gridPaginada, fantasia));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, gridPaginada, fantasia);
                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.Cadastrar)]
        //[ClaimsAuthorize()]
        public async Task<ActionResult> Cadastrar(LojaCadastroViewModel lojaViewModel)
        {
            try
            {
                await _cadastrarLojaService.Cadastrar(lojaViewModel);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }

            return CustomResponse();
        }

        [HttpPost(Endpoints.AtualizarServicos)]
        //[ClaimsAuthorize()]
        public async Task<ActionResult> AtualizarServicos(AtualizarServicosViewModel atualizarServicosViewModel)
        {
            try
            {
                await _atualizarServicosService.Atualizar(atualizarServicosViewModel);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }

            return CustomResponse();
        }

        [HttpPost(Endpoints.Alterar)]
        [ClaimsAuthorize(Permissoes.LojaAlterar)]
        public async Task<ActionResult> Alterar(LojaViewModel lojaViewModel)
        {
            try
            {
                await _lojaService.Alterar(lojaViewModel);
                return CustomResponse();
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }

            return CustomResponse();
        }

        [HttpGet(Endpoints.Obter)]
        [ClaimsAuthorize(Permissoes.LojaVisualizar, Permissoes.LojaAlterar)]
        public async Task<ActionResult<LojaViewModel>> Obter(Guid id)
        {
            try
            {
                return CustomResponse(await _lojaService.Obter(id));
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }

            return CustomResponse();
        }

		[HttpGet(Endpoints.ObterIdentificadorUrlDelivery)]
		[ClaimsAuthorize(Permissoes.LojaVisualizar, Permissoes.LojaAlterar)]
		public async Task<ActionResult<string>> ObterIdentificadorUrlDelivery(
            [FromRoute] Guid lojaId)
		{
			try
			{
				return CustomResponse(await _lojaService.ObterIdentificadorUrlDelivery(lojaId));
			}
			catch (Exception ex)
			{
				NotificarErro(ex);
			}

			return CustomResponse();
		}

		[HttpDelete(Endpoints.Excluir)]
        [ClaimsAuthorize(Permissoes.LojaExcluir)]
        public async Task<ActionResult> Excluir(Guid id)
        {
            try
            {
                await _lojaService.Excluir(id);
                return CustomResponse();
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ObterEmailContabilista)]
        [ClaimsAuthorize()]
        public async Task<ActionResult> ObterEmailContabilista()
        {
            try
            {
                return CustomResponse(await _lojaService.ObterEmailContabilista());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }

            return CustomResponse();
        }

        [HttpGet(Endpoints.ObterInformacoesFinalizarVenda)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<LojaInformacoesFinalizarVendaViewModel>> ObterInformacoesFinalizarVenda()
        {
            try
            {
                return CustomResponse(await _lojaService.ObterInformacoesFinalizarVenda());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }

            return CustomResponse();
        }

        [HttpGet(Endpoints.ObterTipoAmbienteFiscal)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<AmbienteFiscal>> ObterAmbienteFiscal()
        {
            try
            {
                return CustomResponse(await _lojaService.ObterAmbienteFiscal());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ObterConfigEmitirNFe)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<bool>> ObterConfigEmitirNFe()
        {
            try
            {
                return CustomResponse(await _lojaService.ObterConfigEmitirNFe());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }

            return CustomResponse();
        }

        [HttpPost(Endpoints.GerarPinA3)]
        [ClaimsAuthorize(Permissoes.LojaVisualizar, Permissoes.LojaAlterar)]
        public async Task<ActionResult<string>> GerarPinA3([FromRoute] Guid lojaId)
        {
            try
            {
                return CustomResponse(await _lojaService.GerarPinA3(lojaId));
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ObterInformacoesPinA3)]
        public async Task<ActionResult<string>> ObterInformacoesPinA3([FromRoute] string pin)
        {
            try
            {
                return CustomResponse(await _lojaService.ObterInformacoesPinA3(pin));
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ListarLojasComContasFinanceiras)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<List<LojaContaFinanceiraViewModel>>> ListarLojasComContasFinanceiras()
        {
            try
            {
                return CustomResponse(await _lojaService.ListarLojasComContasFinanceiras());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet("listar")]
        public async Task<ActionResult<List<ListaLojaViewModel>>> ListarLojasSti3Dashboard()
        {
            try
            {
                return CustomResponse(await _lojaService.ListarLojasParaSti3Dashboard());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ObterVencimentos)]
        [ClaimsAuthorize]
        public async Task<ActionResult<DateTime?>> ObterVencimentos()
        {
            try
            {
                return CustomResponse(await _lojaService.ObterVencimentos());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }
    }
}
