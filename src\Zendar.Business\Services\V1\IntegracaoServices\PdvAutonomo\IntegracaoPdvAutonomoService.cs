﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Multiempresa.Data.Context;
using Multiempresa.Data.Repositories.AnpRepositories;
using Multiempresa.Shared.Constants;
using Multiempresa.Shared.Enums;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.API.ZendarSync.Handlers;
using Zendar.Business.Application.Events.FrenteCaixaEvents;
using Zendar.Business.AutoMappers.ProdutoMapper;
using Zendar.Business.Helpers;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Interfaces.Services.Fiscal;
using Zendar.Business.Services.DispositivoServices;
using Zendar.Business.Services.Financeiro.ContaFinanceiraServices;
using Zendar.Business.Services.IntegracaoServices.IntegracaoOrigemPedidoService;
using Zendar.Business.Services.IntegracaoServices.IntegracaoPedidoService.Cadastrar;
using Zendar.Business.Services.IntegracaoServices.IntegracaoPendenciaPedidoService;
using Zendar.Business.Services.IntegracaoServices.IntegracaoService;
using Zendar.Business.Services.IntegracaoServices.IntegracaoSituacaoPedidoService;
using Zendar.Business.Services.V1.IntegracaoServices.PdvAutonomo.Interfaces;
using Zendar.Business.Services.V1.IntegracaoServices.SincronizacaoServices.Sincronizacao;
using Zendar.Business.Services.V1.IntegracaoServices.SincronizacaoServices.Trigger;
using Zendar.Business.Services.V2.TabelaPrecoV2Services.TabelaPrecoV2Service;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Fiscal;
using Zendar.Business.ViewModels.Integracao;
using Zendar.Business.ViewModels.Integracao.IntegracaoPagamentos.Zoop;
using Zendar.Business.ViewModels.Integracao.IntegracaoSituacaoPedido;
using Zendar.Business.ViewModels.Integracao.PdvAutonomo;
using Zendar.Business.ViewModels.V1.Fiscal;
using Zendar.Business.ViewModels.V1.Integracao.PdvAutonomo;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;
using Zendar.Data.DomainServices.PrecoServices;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Models.DTO.Padronizacao;
using Zendar.Data.Models.DTO.Preco;
using Zendar.Data.Models.DTO.TabelaPreco;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoOrigemPedidoRepository;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoPedidoRepository;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoPendenciaPedidoRepository;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoRepository;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoSituacaoPedidoRepository;
using Zendar.Data.Repository.Aplicacao.LojaServicosRepository;
using Zendar.Data.ViewModels.Dispositivo;
using Zendar.Data.ViewModels.Integracao.FrenteCaixa;
using Zendar.Integracao.ViewModel;
using ZendarPackage.NotaFiscal.ClassesXml.InutilizarXml.Retorno;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.ManipuladorXml;

namespace Zendar.Business.Services.V1.IntegracaoServices.PdvAutonomo
{
    public class IntegracaoPdvAutonomoService : BaseService, IIntegracaoPdvAutonomoService
    {
        private readonly IMapper _mapper;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly INotificationHubService _notificationHubService;
        private readonly INotificador _notificador;
        private readonly ICacheService _cacheService;
        private readonly IZendarTriggerService _zendarTriggerService;
        private MultiEmpresaContexto _multiEmpresaContexto;

        private readonly IIntegracaoRepository _integracaoRepository;
        private readonly IIntegracaoPedidoRepository _integracaoPedidoRepository;
        private readonly IIntegracaoSituacaoPedidoRepository _integracaoSituacaoPedidoRepository;
        private readonly IIntegracaoOrigemPedidoRepository _integracaoOrigemPedidoRepository;
        private readonly IIntegracaoPendenciaPedidoRepository _integracaoPendenciaPedidoRepository;
        private readonly IContaFinanceiraRepository _contaFinanceiraRepository;
        private readonly IMovimentacaoFinanceiraRepository _movimentacaoFinanceiraRepository;
        private readonly IDocumentoFiscalRepository _documentoFiscalRepository;
        private readonly IDispositivoRepository _dispositivoRepository;

        private readonly ILojaRepository _lojaRepository;
        private readonly IVendedorRepository _vendedorRepository;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IPermissaoRepository _permissaoRepository;
        private readonly IUsuarioPermissaoRepository _usuarioPermissaoRepository;
        private readonly ICorRepository _corRepository;
        private readonly IUnidadeMedidaRepository _unidadeMedidaRepository;
        private readonly ITamanhoRepository _tamanhoRepository;
        private readonly IMarcaRepository _marcaRepository;
        private readonly ICategoriaProdutoRepository _categoriaProdutoRepository;
        private readonly IFormaPagamentoRecebimentoRepository _formaPagamentoRecebimentoRepository;
        private readonly IProdutoRepository _produtoRepository;
        private readonly IClienteFornecedorRepository _clienteFornecedorRepository;
        private readonly ILojaServicosRepository _lojaServicosRepository;
        private readonly ILojaFiscalRepository _lojaFiscalRepository;
        private readonly IPadronizacaoRepository _padronizacaoRepository;
        private readonly ICupomSatRepository _cupomSatRepository;
        private readonly IRegraFiscalRepository _regraFiscalRepository;
        private readonly IAnpRepository _anpRepository;

        private readonly IIntegracaoService _integracaoService;
        private readonly IIntegracaoPedidoService _integracaoPedidoService;
        private readonly IIntegracaoSituacaoPedidoService _integracaoSituacaoPedidoService;
        private readonly IIntegracaoOrigemPedidoService _integracaoOrigemPedidoService;
        private readonly IIntegracaoPendenciaPedidoService _integracaoPendenciaPedidoService;
        private readonly IIntegracaoPedidoPdvAutonomoService _integracaoPedidoPdvAutonomoService;

        private readonly ITabelaPrecoService _tabelaPrecoService;
        private readonly ITabelaPrecoV2Service _tabelaPrecoV2Service;
        private readonly IEnvioEmailService _envioEmailService;
        private readonly IContaFinanceiraService _contaFinanceiraService;
        private readonly ILocalEstoqueService _localEstoqueService;
        private readonly IDispositivoService _dispositivoService;
        private readonly INotaFiscalService _notaFiscalService;
        private readonly IPadronizacaoService _padronizacaoService;
        private readonly ICupomSatService _cupomSatService;
        private readonly IInutilizacaoService _inutilizacaoService;
        private readonly INotificacaoService _notificacaoService;
        private readonly INotificacaoExclusaoService _notificacaoExclusaoService;
        private readonly IStorageService _storageService;
        private readonly ILogErroService _logErroService;
        private readonly ISincronizacaoIntegracaoService _sincronizacaoIntegracaoService;

        private readonly IPeriodoCaixaRepository _periodoCaixaRepository;
        private readonly ICredenciadoraCartaoRepository _credenciadoraCartaoRepository;

        public IntegracaoPdvAutonomoService(
            IConfiguration configuration,
            IMapper mapper,
            INotificador notificador,
            IAspNetUserInfo aspNetUserInfo,
            INotificationHubService notificationHubService,
            ICacheService cacheService,
            IZendarTriggerService zendarTriggerService,
            MultiEmpresaContexto multiEmpresaContexto,

            IIntegracaoRepository integracaoRepository,
            IIntegracaoPedidoRepository integracaoPedidoRepository,
            IIntegracaoSituacaoPedidoRepository integracaoSituacaoPedidoRepository,
            IIntegracaoOrigemPedidoRepository integracaoOrigemPedidoRepository,
            IIntegracaoPendenciaPedidoRepository integracaoPendenciaPedidoRepository,
            IContaFinanceiraRepository contaFinanceiraRepository,
            IMovimentacaoFinanceiraRepository movimentacaoFinanceiraRepository,
            IDocumentoFiscalRepository documentoFiscalRepository,
            IDispositivoRepository dispositivoRepository,

            ILojaRepository lojaRepository,
            IVendedorRepository vendedorRepository,
            IUsuarioRepository usuarioRepository,
            IPermissaoRepository permissaoRepository,
            IUsuarioPermissaoRepository usuarioPermissaoRepository,
            ICorRepository corRepository,
            IUnidadeMedidaRepository unidadeMedidaRepository,
            ITamanhoRepository tamanhoRepository,
            IMarcaRepository marcaRepository,
            ICategoriaProdutoRepository categoriaProdutoRepository,
            IFormaPagamentoRecebimentoRepository formaPagamentoRecebimentoRepository,
            IProdutoRepository produtoRepository,
            IClienteFornecedorRepository clienteFornecedorRepository,
            ILojaServicosRepository lojaServicosRepository,
            ILojaFiscalRepository lojaFiscalRepository,
            IPadronizacaoRepository padronizacaoRepository,
            ICupomSatRepository cupomSatRepository,
            IRegraFiscalRepository regraFiscalRepository,
            IAnpRepository anpRepository,

            IIntegracaoService integracaoService,
            IIntegracaoPedidoService integracaoPedidoService,
            IIntegracaoSituacaoPedidoService integracaoSituacaoPedidoService,
            IIntegracaoOrigemPedidoService integracaoOrigemPedidoService,
            IIntegracaoPendenciaPedidoService integracaoPendenciaPedidoService,
            IIntegracaoPedidoPdvAutonomoService integracaoPedidoPdvAutonomoService,
            IPeriodoCaixaRepository periodoCaixaRepository,
            ICredenciadoraCartaoRepository credenciadoraCartaoRepository,

            ITabelaPrecoService tabelaPrecoService,
            ITabelaPrecoV2Service tabelaPrecoV2Service,
            IEnvioEmailService envioEmailService,
            IContaFinanceiraService contaFinanceiraService,
            ILocalEstoqueService localEstoqueService,
            IDispositivoService dispositivoService,
            INotaFiscalService notaFiscalService,
            IPadronizacaoService padronizacaoService,
            ICupomSatService cupomSatService,
            IInutilizacaoService inutilizacaoService,
            INotificacaoService notificacaoService,
            INotificacaoExclusaoService notificacaoExclusaoService,
            IStorageService storageService,
            ILogErroService logErroService,
            ISincronizacaoIntegracaoService sincronizacaoIntegracaoService) : base(notificador)
        {
            _mapper = mapper;
            _notificador = notificador;
            _aspNetUserInfo = aspNetUserInfo;
            _notificationHubService = notificationHubService;
            _cacheService = cacheService;
            _zendarTriggerService = zendarTriggerService;
            _multiEmpresaContexto = multiEmpresaContexto;

            _integracaoRepository = integracaoRepository;
            _integracaoPedidoRepository = integracaoPedidoRepository;
            _integracaoSituacaoPedidoRepository = integracaoSituacaoPedidoRepository;
            _integracaoOrigemPedidoRepository = integracaoOrigemPedidoRepository;
            _integracaoPendenciaPedidoRepository = integracaoPendenciaPedidoRepository;
            _contaFinanceiraRepository = contaFinanceiraRepository;
            _movimentacaoFinanceiraRepository = movimentacaoFinanceiraRepository;
            _documentoFiscalRepository = documentoFiscalRepository;
            _dispositivoRepository = dispositivoRepository;

            _lojaRepository = lojaRepository;
            _vendedorRepository = vendedorRepository;
            _usuarioRepository = usuarioRepository;
            _permissaoRepository = permissaoRepository;
            _usuarioPermissaoRepository = usuarioPermissaoRepository;
            _corRepository = corRepository;
            _unidadeMedidaRepository = unidadeMedidaRepository;
            _tamanhoRepository = tamanhoRepository;
            _marcaRepository = marcaRepository;
            _categoriaProdutoRepository = categoriaProdutoRepository;
            _formaPagamentoRecebimentoRepository = formaPagamentoRecebimentoRepository;
            _produtoRepository = produtoRepository;
            _clienteFornecedorRepository = clienteFornecedorRepository;
            _lojaServicosRepository = lojaServicosRepository;
            _lojaFiscalRepository = lojaFiscalRepository;
            _padronizacaoRepository = padronizacaoRepository;
            _cupomSatRepository = cupomSatRepository;
            _regraFiscalRepository = regraFiscalRepository;
            _anpRepository = anpRepository;

            _integracaoService = integracaoService;
            _integracaoPedidoService = integracaoPedidoService;
            _integracaoSituacaoPedidoService = integracaoSituacaoPedidoService;
            _integracaoOrigemPedidoService = integracaoOrigemPedidoService;
            _integracaoPendenciaPedidoService = integracaoPendenciaPedidoService;
            _integracaoPedidoPdvAutonomoService = integracaoPedidoPdvAutonomoService;
            _tabelaPrecoService = tabelaPrecoService;
            _tabelaPrecoV2Service = tabelaPrecoV2Service;
            _envioEmailService = envioEmailService;
            _contaFinanceiraService = contaFinanceiraService;
            _localEstoqueService = localEstoqueService;
            _dispositivoService = dispositivoService;
            _notaFiscalService = notaFiscalService;
            _padronizacaoService = padronizacaoService;
            _cupomSatService = cupomSatService;
            _inutilizacaoService = inutilizacaoService;
            _notificacaoService = notificacaoService;
            _notificacaoExclusaoService = notificacaoExclusaoService;
            _storageService = storageService;
            _logErroService = logErroService;
            _sincronizacaoIntegracaoService = sincronizacaoIntegracaoService;
            _periodoCaixaRepository = periodoCaixaRepository;
            _credenciadoraCartaoRepository = credenciadoraCartaoRepository;
        }

        #region Public Methods

        #region Integracao

        public async Task<IntegracaoObterViewModel> ObterIntegracao(
            Guid? lojaId = null)
        {
            try
            {
                _notificador.Limpar();

                var result =
                    await _integracaoService.Obter(IdentificacaoIntegracao.CAIXA_PDV_AUTONOMO, lojaId);

                return result;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<Guid?> CadastrarIntegracao(
            IntegracaoViewModel integracaoViewModel)
        {
            try
            {
                var integracao =
                    await _integracaoRepository.FirstOrDefaultAsNoTracking(i => i.IdentificacaoIntegracao == integracaoViewModel.IdentificacaoIntegracao &&
                                                                                i.LojaId == _aspNetUserInfo.LojaId.Value);

                if (integracao != null)
                {
                    NotificarAviso("Já existe uma integração para o PDV Offline");

                    return null;
                }

                integracaoViewModel.UsuarioId = Guid.Parse(_aspNetUserInfo.Id);

                var localEstoqueId =
                    await _localEstoqueService.ObterPadraoSistema(_aspNetUserInfo.LojaId.Value);

                if (localEstoqueId != null)
                    integracaoViewModel.LocalEstoqueId = localEstoqueId.Value;

                if (PossuiAvisos() || PossuiErros())
                    return null;

                #region StarGate

                int? parceiroSti3 = 0;
                string versaoTabelaIbpt = "TabelaIBPTaxSP23.2.B";
                string versaoTabelaIbptVigenciaFinal = "";
                string identificacaoParceiro = "";
                string cnpjResponsavelTecnico = "07.482.867/0001-70";
                string nomeResponsavelTecnico = "CRISTIAN RAVAGNOLLI";
                string emailResponsavelTecnico = "<EMAIL>";
                string foneResponsavelTecnico = "(14) 3624-3709";

                var contaCliente =
                    _multiEmpresaContexto.ContaCliente?.IgnoreQueryFilters()
                                                      ?.Include(c => c.Revenda)
                                                      ?.Where(c => c.Dominios.Any(x => x.Dominio.ToLower() == _aspNetUserInfo.HostUrl))
                                                      ?.Select(s => s)
                                                      ?.FirstOrDefault();

                if (contaCliente == null)
                {
                    NotificarAviso("Conta cliente não encontrada.");

                    return null;
                }

                if (contaCliente.Revenda != null)
                {
                    if (!contaCliente.Revenda.PadraoSistema)
                        parceiroSti3 = 1;

                    identificacaoParceiro = contaCliente.Revenda.NomeFantasia;
                    cnpjResponsavelTecnico = contaCliente.Revenda.Cnpj;
                    nomeResponsavelTecnico = contaCliente.Revenda.NomeResponsavel;
                    emailResponsavelTecnico = contaCliente.Revenda.EmailContato;
                    foneResponsavelTecnico = contaCliente.Revenda.Telefone;
                }

                var loja = await _lojaRepository.ObterInformacoesFiscais(_aspNetUserInfo.LojaId.Value);

                if (loja != null)
                {
                    var importacaoNcm =
                        _multiEmpresaContexto.ImportacaoNcm?.IgnoreQueryFilters()
                                                           ?.Where(x => x.CodigoEstado == Convert.ToInt32(loja.Cidade.Estado.Codigo))
                                                           ?.Select(s => s)
                                                           ?.OrderByDescending(c => c.DataHora)
                                                           ?.FirstOrDefault();

                    if (importacaoNcm != null)
                    {
                        versaoTabelaIbpt = importacaoNcm.CaminhoArquivo.Replace(".csv", "");

                        var ncmEstado =
                            _multiEmpresaContexto.NcmEstado?.IgnoreQueryFilters()
                                                           ?.Where(x => x.EstadoId == Convert.ToInt32(loja.Cidade.Estado.Codigo) &&
                                                                        x.Versao == importacaoNcm.Versao)
                                                           ?.Select(s => s)
                                                           ?.FirstOrDefault();

                        if (ncmEstado != null)
                            versaoTabelaIbptVigenciaFinal = ncmEstado.VigenciaFim.ToString("yyyy-MM-dd");
                    }
                }

                #endregion

                #region Zendar

                var tamanho = _tamanhoRepository.FirstOrDefaultAsNoTracking(c => !c.PadraoSistema);

                bool controleTam = tamanho != null ? true : false;

                var cor = _corRepository.FirstOrDefaultAsNoTracking(c => !c.PadraoSistema);

                bool controleCores = cor != null ? true : false;

                var lojaFiscal =
                    await _lojaFiscalRepository.FirstOrDefaultAsNoTracking(c => c.LojaId == _aspNetUserInfo.LojaId.Value);

                int nfeTipoAmbiente = (int)lojaFiscal.TipoAmbienteFiscal;

                var padronizacao = await _padronizacaoRepository.FirstOrDefaultAsNoTracking();

                int casasDecimais = padronizacao.CasasDecimaisValor;

                int casasDecimaisQtde = padronizacao.CasasDecimaisQuantidade;

                #endregion

                var tabelaPrecoId = await _tabelaPrecoService.ObterTabelaPrecoPadrao();

                var configuracaoTabelaPrecoAlterarViewModel = new PdvAutonomoConfiguracaoTabelaPrecoAlterarViewModel();
                configuracaoTabelaPrecoAlterarViewModel.TabelaPrecoIdAVista = tabelaPrecoId;
                configuracaoTabelaPrecoAlterarViewModel.TabelaPrecoIdAPrazo = tabelaPrecoId;

                var integracaoId =
                    await _integracaoService.Cadastrar(integracaoViewModel,
                                                       JsonConvert.SerializeObject(
                                                           new PdvAutonomoConfiguracaoViewModel(contaCliente.BancoDados.ToString(),
                                                                                                parceiroSti3,
                                                                                                versaoTabelaIbpt,
                                                                                                versaoTabelaIbptVigenciaFinal,
                                                                                                identificacaoParceiro,
                                                                                                cnpjResponsavelTecnico,
                                                                                                nomeResponsavelTecnico,
                                                                                                emailResponsavelTecnico,
                                                                                                foneResponsavelTecnico,
                                                                                                controleTam,
                                                                                                controleCores,
                                                                                                nfeTipoAmbiente,
                                                                                                casasDecimais,
                                                                                                casasDecimaisQtde,
                                                                                                configuracaoTabelaPrecoAlterarViewModel)));


                if (integracaoId != Guid.Empty)
                    await _integracaoSituacaoPedidoService.VincularSituacaoIntegracaoPadrao(integracaoId, ObterListaSituacaoPadrao());

                return integracaoId;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task AtivarInativarIntegracao(
            Guid lojaId,
            bool ativar)
        {
            try
            {
                var integracao =
                    await _integracaoRepository.FirstOrDefaultAsNoTracking(i => i.IdentificacaoIntegracao == IdentificacaoIntegracao.CAIXA_PDV_AUTONOMO &&
                                                                                 i.LojaId == lojaId);

                if (PossuiAvisos() || PossuiErros() || integracao == null)
                    return;

                var configuracoes = JsonConvert.DeserializeObject<PdvAutonomoConfiguracaoViewModel>(integracao.Configuracoes);
                integracao.SincronizacaoHabilitada = ativar;

                await _integracaoService.Alterar(
                    new IntegracaoViewModel
                    {
                        Id = integracao.Id,
                        SincronizacaoHabilitada = integracao.SincronizacaoHabilitada,
                        LocalEstoqueId = integracao.LocalEstoqueId,
                        Ativo = integracao.Ativo
                    },
                    JsonConvert.SerializeObject(configuracoes));

                await SetZendarTriggerCache(lojaId);
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new()
                {
                    Erro = ex.Message,
                    Dados = JsonConvert.SerializeObject(new
                    {
                        lojaId,
                        ativar,
                        ex.StackTrace
                    })
                });

                return;
            }
        }

        public async Task<PdvAutonomoConfiguracaoAlterarViewModel> ObterConfiguracaoParaAlterar()
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var configuracao =
                    JsonConvert.DeserializeObject<PdvAutonomoConfiguracaoAlterarViewModel>(integracao.Configuracoes);

                return configuracao;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<Guid?> AlterarConfiguracao(
            PdvAutonomoConfiguracaoAlterarViewModel configuracaoAlterarViewModel)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var configuracoes = JsonConvert.DeserializeObject<PdvAutonomoConfiguracaoViewModel>(integracao.Configuracoes);
                configuracoes.CabecalhoCupom = configuracaoAlterarViewModel.CabecalhoCupom;
                configuracoes.RodapeCupom = configuracaoAlterarViewModel.RodapeCupom;
                configuracoes.QtdDigCodProdBalanca = configuracaoAlterarViewModel.QtdDigCodProdBalanca;
                configuracoes.UtilizarVendPDV = configuracaoAlterarViewModel.UtilizarVendPDV;
                configuracoes.NFeFormaEmissao = configuracaoAlterarViewModel.NFeFormaEmissao;
                configuracoes.DiasBloqVenda = configuracaoAlterarViewModel.DiasBloqVenda;
                configuracoes.MostrarLimitesRelVendas = configuracaoAlterarViewModel.MostrarLimitesRelVendas;
                configuracoes.BloquearCliAtraso = configuracaoAlterarViewModel.BloquearCliAtraso;
                configuracoes.ImpressaoPorItensPDV = configuracaoAlterarViewModel.ImpressaoPorItensPDV;
                configuracoes.VendaSolicitarCpf = configuracaoAlterarViewModel.VendaSolicitarCpf;
                configuracoes.RodapeCupomParceiro = configuracaoAlterarViewModel.RodapeCupomParceiro;
                configuracoes.LimiteQuantidadeVenda = configuracaoAlterarViewModel.LimiteQuantidadeVenda;
                configuracoes.ImprimirDadosCompletosCliente = configuracaoAlterarViewModel.ImprimirDadosCompletosCliente;
                configuracoes.AceitarCodigoInternoPDV = configuracaoAlterarViewModel.AceitarCodigoInternoPDV;
                configuracoes.ImprimirViaCupomNaoFiscal = configuracaoAlterarViewModel.ImprimirViaCupomNaoFiscal;
                configuracoes.AgruparItensImpressaoVenda = configuracaoAlterarViewModel.AgruparItensImpressaoVenda;
                configuracoes.VendaObrigatorioCPF = configuracaoAlterarViewModel.VendaObrigatorioCPF;
                configuracoes.ExibirDescansoDeTelaNoPDV = configuracaoAlterarViewModel.ExibirDescansoDeTelaNoPDV;
                configuracoes.SolicitarImpressaoEcologica = configuracaoAlterarViewModel.SolicitarImpressaoEcologica;
                configuracoes.SangriaAposDinheiroPDV = configuracaoAlterarViewModel.SangriaAposDinheiroPDV;
                configuracoes.DecimaisDireitaPDV = configuracaoAlterarViewModel.DecimaisDireitaPDV;
                configuracoes.PdvManterFocoNaTela = configuracaoAlterarViewModel.PdvManterFocoNaTela;
                configuracoes.ImpressaoDescontoVenda = configuracaoAlterarViewModel.ImpressaoDescontoVenda;
                configuracoes.TimerAposVendaPDV = configuracaoAlterarViewModel.TimerAposVendaPDV;
                configuracoes.IdProdutoGenerico = configuracaoAlterarViewModel.IdProdutoGenerico;
                configuracoes.ConsiderarCodigoBarraBalanca = configuracaoAlterarViewModel.ConsiderarCodigoBarraBalanca;
                configuracoes.NfePLNotaFiscal = configuracaoAlterarViewModel.NfePLNotaFiscal;
                configuracoes.NfePLEPEC = configuracaoAlterarViewModel.NfePLEPEC;
                configuracoes.NfePLEvento = configuracaoAlterarViewModel.NfePLEvento;
                configuracoes.VersaoDadosSAT = configuracaoAlterarViewModel.VersaoDadosSAT;

                if (!string.IsNullOrEmpty(configuracaoAlterarViewModel.SerieCertificado))
                    configuracoes.SerieCertificado = configuracaoAlterarViewModel.SerieCertificado;

                if (!string.IsNullOrEmpty(configuracaoAlterarViewModel.AssinaturaSatSTi3))
                    configuracoes.AssinaturaSatSTi3 = configuracaoAlterarViewModel.AssinaturaSatSTi3;

                if (!string.IsNullOrEmpty(configuracaoAlterarViewModel.AssinaturaSatSTi3))
                    configuracoes.IdEmpresaSti3Pay = configuracaoAlterarViewModel.IdEmpresaSti3Pay;

                #region StarGate

                var contaCliente =
                    _multiEmpresaContexto.ContaCliente?.IgnoreQueryFilters()
                                                      ?.Include(c => c.Revenda)
                                                      ?.Where(c => c.Dominios.Any(x => x.Dominio.ToLower() == _aspNetUserInfo.HostUrl))
                                                      ?.Select(s => s)
                                                      ?.FirstOrDefault();

                if (contaCliente != null &&
                    contaCliente.Revenda != null)
                {
                    if (!contaCliente.Revenda.PadraoSistema)
                        configuracoes.ParceiroSTi3 = 1;

                    configuracoes.IdentificacaoParceiro = contaCliente.Revenda.NomeFantasia;
                    configuracoes.CNPJResponsavelTecnico = contaCliente.Revenda.Cnpj;
                    configuracoes.NomeResponsavelTecnico = contaCliente.Revenda.NomeResponsavel;
                    configuracoes.EmailResponsavelTecnico = contaCliente.Revenda.EmailContato;
                    configuracoes.FoneResponsavelTecnico = contaCliente.Revenda.Telefone;
                }

                #endregion

                #region Loja Fiscal

                var lojaFiscal =
                    await _lojaFiscalRepository.FirstOrDefaultAsNoTracking(c => c.LojaId == _aspNetUserInfo.LojaId.Value);

                if (lojaFiscal != null)
                {
                    int nfeTipoAmbiente = (int)lojaFiscal.TipoAmbienteFiscal;

                    configuracoes.NFeTipoAmbiente = nfeTipoAmbiente;
                }
                #endregion

                var integracaoViewModel =
                    _mapper.Map<IntegracaoViewModel>(integracao);

                await _integracaoService.Alterar(integracaoViewModel,
                                                 JsonConvert.SerializeObject(configuracoes));

                #region Trigger

                await _zendarTriggerService.ExecuteGuid(integracao.Id,
                                                    TabelaTrigger.CONFIGURACAO,
                                                    OperacaoTrigger.ALTERAR);

                #endregion

                return integracao.Id;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<PdvAutonomoConfiguracaoTefAlterarViewModel> ObterConfiguracaoTefParaAlterar()
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var configuracoes =
                    JsonConvert.DeserializeObject<PdvAutonomoConfiguracaoViewModel>(integracao.Configuracoes);

                var configuracaoAlterarTefViewModel = configuracoes.Tef;

                return configuracaoAlterarTefViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<Guid?> AlterarConfiguracaoTef(
            PdvAutonomoConfiguracaoTefAlterarViewModel configuracaoAlterarTefViewModel)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var configuracoes =
                    JsonConvert.DeserializeObject<PdvAutonomoConfiguracaoViewModel>(integracao.Configuracoes);

                configuracoes.Tef = configuracaoAlterarTefViewModel;

                var integracaoViewModel =
                    _mapper.Map<IntegracaoViewModel>(integracao);

                await _integracaoService.Alterar(integracaoViewModel,
                                                 JsonConvert.SerializeObject(configuracoes));

                #region Trigger

                await _zendarTriggerService.ExecuteGuid(integracaoViewModel.Id,
                                                    TabelaTrigger.FORMA_PAGAMENTO_RECEBIMENTO,
                                                    OperacaoTrigger.ALTERAR);
                #endregion

                return integracao.Id;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<PdvAutonomoConfiguracaoTabelaPrecoAlterarViewModel> ObterConfiguracaoTabelaPrecoParaAlterar()
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var configuracoes =
                    JsonConvert.DeserializeObject<PdvAutonomoConfiguracaoViewModel>(integracao.Configuracoes);

                var configuracaoTabelaPrecoAlterarViewModel = configuracoes.TabelaPreco;

                return configuracaoTabelaPrecoAlterarViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<Guid?> AlterarConfiguracaoTabelaPreco(
            PdvAutonomoConfiguracaoTabelaPrecoAlterarViewModel configuracaoTabelaPrecoAlterarViewModel)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var configuracoes =
                    JsonConvert.DeserializeObject<PdvAutonomoConfiguracaoViewModel>(integracao.Configuracoes);

                configuracoes.TabelaPreco = configuracaoTabelaPrecoAlterarViewModel;

                var integracaoViewModel =
                    _mapper.Map<IntegracaoViewModel>(integracao);

                await _integracaoService.Alterar(integracaoViewModel,
                                                 JsonConvert.SerializeObject(configuracoes));

                #region Trigger

                await _zendarTriggerService.ExecuteGuid(integracaoViewModel.Id,
                                                    TabelaTrigger.TABELA_PRECO,
                                                    OperacaoTrigger.ALTERAR);

                #endregion

                return integracao.Id;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task Desistir()
        {
            string mensagem = "Falha ao desistir da configuração.";

            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return;

                #region Integracao Pendencia Pedido

                var listaIntegracaoPendenciaPedido =
                    await _integracaoPendenciaPedidoService.ObterListaPorIdentificacaoIntegracao(IdentificacaoIntegracao.CAIXA_PDV_AUTONOMO);

                foreach (var item in listaIntegracaoPendenciaPedido)
                    await _integracaoPendenciaPedidoRepository.Delete(item.Id);

                #endregion

                #region Integracao Pedido

                var listaIntegracaoPedido =
                    _integracaoPedidoService.ObterListaPedidoPorIntegracaoId(integracao.Id);

                foreach (var item in listaIntegracaoPedido)
                    await _integracaoPedidoRepository.Delete(item.Id);

                #endregion

                #region Integracao Situacao Pedido

                var listaSituacaoPedido =
                    await _integracaoSituacaoPedidoService.ListarSelect(IdentificacaoIntegracao.CAIXA_PDV_AUTONOMO);

                foreach (var item in listaSituacaoPedido)
                    await _integracaoSituacaoPedidoRepository.Delete(item.Id);

                #endregion

                #region Integracao Origem Pedido

                var listaOrigemPedido =
                    await _integracaoOrigemPedidoService.ListarSelect(IdentificacaoIntegracao.CAIXA_PDV_AUTONOMO);

                foreach (var item in listaOrigemPedido)
                    await _integracaoOrigemPedidoRepository.Delete(item.Id);

                #endregion

                #region Integracao

                await _integracaoRepository.Delete(integracao.Id);

                #endregion
            }
            catch (Exception ex)
            {
                NotificarErro(mensagem);

                return;
            }
        }

        public async Task<PdvAutonomoClienteFornecedorRegraViewModel> ObterClienteFornecedorRegra(
            Guid clienteFornecedorId)
        {
            var clienteFornecedorRegraViewModel = new PdvAutonomoClienteFornecedorRegraViewModel();
            clienteFornecedorRegraViewModel.ValoresEmAtraso = _movimentacaoFinanceiraRepository.ObterValorContasEmAtraso(clienteFornecedorId);
            clienteFornecedorRegraViewModel.SaldoDevedor = _movimentacaoFinanceiraRepository.ObterValorContasEmAberto(clienteFornecedorId);

            var clienteFornecedor =
                await _clienteFornecedorRepository.FirstOrDefaultAsNoTracking(c => c.Id == clienteFornecedorId);

            if (clienteFornecedor != null)
                clienteFornecedorRegraViewModel.LimiteCredito = clienteFornecedor.LimiteCredito;

            var vencimentoContaEmAberto =
                await _movimentacaoFinanceiraRepository.ConsultarVencimentoContaEmAbertoMaisAntiga(clienteFornecedorId);

            if (vencimentoContaEmAberto != new DateTime())
            {
                var diasEmAtraso = DateTime.UtcNow.Date - vencimentoContaEmAberto.Date;

                clienteFornecedorRegraViewModel.DiasAtraso = diasEmAtraso.Days;
            }

            return clienteFornecedorRegraViewModel;
        }

        public async Task<List<PdvAutonomoTefViewModel>> ObterListaOperacaoTef()
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaTefViewModel =
                    new List<PdvAutonomoTefViewModel>();

                var tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "0";
                tefViewModel.Descricao = "Genérico SOFTWARE EXPRESS";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "2";
                tefViewModel.Descricao = "Débito SOFTWARE EXPRESS";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "3";
                tefViewModel.Descricao = "Crédito SOFTWARE EXPRESS";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "012";
                tefViewModel.Descricao = "Reimpressão do Último Comprovante";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "101";
                tefViewModel.Descricao = "Débito";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "112";
                tefViewModel.Descricao = "Crédito";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "113";
                tefViewModel.Descricao = "Crédito Parcelado Sem Juros";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "114";
                tefViewModel.Descricao = "Crédito Parcelado Com Juros";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "128";
                tefViewModel.Descricao = "Cancelamento Genérico";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "158";
                tefViewModel.Descricao = "Cancelamento de Crédito";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "159";
                tefViewModel.Descricao = "Cancelamento de Débito";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "229";
                tefViewModel.Descricao = "Reimpressão de Outro Comprovante";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "902";
                tefViewModel.Descricao = "Operação Genérica";
                listaTefViewModel.Add(tefViewModel);

                return listaTefViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<PdvAutonomoTefViewModel>> ObterListaParcelamentoTef()
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaTefViewModel =
                    new List<PdvAutonomoTefViewModel>();

                var tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "0";
                tefViewModel.Descricao = "Escolher no menu";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "2";
                tefViewModel.Descricao = "Pelo Estabelecimento";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "3";
                tefViewModel.Descricao = "Pela Administradora";
                listaTefViewModel.Add(tefViewModel);

                return listaTefViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<PdvAutonomoTefViewModel>> ObterListaTipoCartaoTef()
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaTefViewModel =
                    new List<PdvAutonomoTefViewModel>();

                var tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "0";
                tefViewModel.Descricao = "Escolher no menu";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "1";
                tefViewModel.Descricao = "Magnético / Chip";
                listaTefViewModel.Add(tefViewModel);

                tefViewModel = new PdvAutonomoTefViewModel();
                tefViewModel.Id = "2";
                tefViewModel.Descricao = "Não Magnético";
                listaTefViewModel.Add(tefViewModel);

                return listaTefViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<string> GerarPinDispositivo()
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var pin = PINGenerator.Generate();

                var chave = string.Format(CacheKeys.Pdv, pin);

                while (string.IsNullOrEmpty(pin) ||
                       pin.Length < 6 ||
                       await _cacheService.Existe(chave))
                {
                    pin = PINGenerator.Generate();
                    chave = string.Format(CacheKeys.Pdv, pin);
                }

                await _cacheService.AdicionarAsync(
                    new CacheModel
                    {
                        Chave = chave,
                        ExpirarAposInatividade = TimeSpan.FromDays(30 * 3),
                        ExpirarEm = DateTime.UtcNow.AddMonths(3),
                        Valor = new PdvAutonomoDispositivoViewModel
                        {
                            Dominio = _aspNetUserInfo.HostUrl,
                            LojaId = _aspNetUserInfo.LojaId.Value,
                            CodigoContaEmpresa = _aspNetUserInfo.CodigoContaEmpresa
                        }
                    },
                    false);

                return pin;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<PdvAutonomoQuantidadeAtivaDispositivoViewModel> ObterQuantidadeAtivaDispositivo()
        {
            var ativoTotal =
                new PdvAutonomoQuantidadeAtivaDispositivoViewModel
                {
                    Ativo =
                    await _dispositivoService.ObterDispositivosAtivos(_aspNetUserInfo.LojaId.Value,
                                                                      ReferenciaServicoStargate.DISPOSITIVO_PDV),
                    Total =
                    await _lojaServicosRepository.ObterQuantidadeServicoDisponivel(ReferenciaServicoStargate.DISPOSITIVO_PDV,
                                                                                   _aspNetUserInfo.LojaId.Value)
                };

            return ativoTotal;
        }

        public GridPaginadaRetorno<DispositivoPaginadoViewModel> ObterListaDispositivo(
            GridPaginadaConsulta gridPaginada,
            string nome,
            bool? ativo = null)
        {
            return _dispositivoRepository.ListarPaginado(gridPaginada,
                                                         nome,
                                                         _aspNetUserInfo.LojaId.Value,
                                                         ReferenciaServicoStargate.DISPOSITIVO_PDV,
                                                         ativo);
        }

        public async Task InativarDispositivo(
          params Guid[] listaDispositivoId)
        {
            var dispositivos =
                await _dispositivoRepository.ObterComContaFinanceiraParaAlteracao(listaDispositivoId);

            if (dispositivos == null ||
                !dispositivos.Any())
            {
                NotificarAvisoRegistroNaoEncontrado("dispositivo");

                return;
            }

            foreach (var dispositivo in dispositivos)
            {
                dispositivo.Desabilitar();

                _dispositivoRepository.AdicionarEvento(new ContaFinanceiraFrenteCaixaInativadaEvent(dispositivo.Id,
                                                                                                    dispositivo.Identificador,
                                                                                                    _aspNetUserInfo.LojaId.Value,
                                                                                                    _aspNetUserInfo.Id));
            }

            await _dispositivoRepository.SaveChanges();
        }

        public async Task AtivarDispositivo(
            params Guid[] listaDispositivoId)
        {
            var quantidadeDispositivoAtivo =
                await _dispositivoRepository.ObterDispositivosAtivos(_aspNetUserInfo.LojaId.Value,
                                                                     ReferenciaServicoStargate.DISPOSITIVO_PDV);

            var quantidadeContratada =
                await _lojaServicosRepository.ObterQuantidadeServicoDisponivel(ReferenciaServicoStargate.DISPOSITIVO_PDV,
                                                                               _aspNetUserInfo.LojaId.Value);

            var dispositivos =
                await _dispositivoRepository.ObterComContaFinanceiraParaAlteracao(listaDispositivoId);

            if (quantidadeContratada <= quantidadeDispositivoAtivo)
            {
                var dispositivo =
                    dispositivos?.FirstOrDefault();

                if (!dispositivo.Ativo)
                {
                    NotificarAviso("Não há licenças disponíveis par ativar o dispositivo.");

                    return;
                }
            }

            if (dispositivos == null ||
                !dispositivos.Any())
            {
                NotificarAvisoRegistroNaoEncontrado("dispositivo");

                return;
            }

            foreach (var dispositivo in dispositivos)
                dispositivo.Habilitar();

            await _dispositivoRepository.SaveChanges();
        }

        #endregion

        #region Sincronizacao

        public async Task<PdvAutonomoDispositivoViewModel> ObterDispositivo(
            string pin,
            string identificador,
            string apelido)
        {
            try
            {
                var dadosPin = await ObterDadosPin(pin);

                var dispositivo = await _dispositivoRepository.ObterDispositivo(
                    dadosPin.LojaId,
                    identificador,
                    apelido,
                    ReferenciaServicoStargate.DISPOSITIVO_PDV);

                if (dispositivo == null)
                    return null;

                var result = new PdvAutonomoDispositivoViewModel
                {
                    Id = dispositivo.Id,
                    Dominio = dadosPin.Dominio,
                    Apelido = dispositivo.Apelido,
                    Identificador = dispositivo.Identificador,
                    Pin = pin,
                    CodigoContaEmpresa = dadosPin.CodigoContaEmpresa,
                    LojaId = dadosPin.LojaId,
                    Origem = dispositivo.Origem,
                    Codigo = dispositivo.Codigo,
                    Ativo = dispositivo.Ativo,
                    DataHoraUltimaAlteracao = dispositivo.DataHoraUltimaAlteracao,
                };

                return result;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);
                return null;
            }
        }

        public async Task<PdvAutonomoDispositivoViewModel> AtivarLicenca(
           PdvAutonomoAtivarLicencaViewModel ativarLicencaViewModel)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var dados =
                    await ObterDadosPin(ativarLicencaViewModel.Pin);

                var quantidadeDispositivoAtivo =
                    await _dispositivoRepository.ObterDispositivosAtivos(dados.LojaId, ReferenciaServicoStargate.DISPOSITIVO_PDV);

                var quantidadeContratada =
                    await _lojaServicosRepository.ObterQuantidadeServicoDisponivel(ReferenciaServicoStargate.DISPOSITIVO_PDV, dados.LojaId);

                var dispositivo =
                    await _dispositivoRepository.FirstOrDefault(x => (x.Identificador == ativarLicencaViewModel.Identificador || x.Apelido == ativarLicencaViewModel.Apelido) &&
                                                                     x.LojaId == dados.LojaId &&
                                                                     x.ReferenciaServico == ReferenciaServicoStargate.DISPOSITIVO_PDV);
                if (dispositivo != null &&
                    quantidadeContratada == 0)
                {
                    await InativarDispositivo(dispositivo.Id);

                    await SetZendarTriggerCache();

                    return null;
                }

                if (quantidadeContratada <= quantidadeDispositivoAtivo)
                {
                    if (dispositivo == null || !dispositivo.Ativo)
                        return null;
                }

                ContaFinanceiraViewModel contaFinanceiraViewModel = null;

                if (dispositivo == null)
                {
                    dados.Ativo = true;
                    dados.Apelido = ativarLicencaViewModel.Apelido;
                    dados.Identificador = ativarLicencaViewModel.Identificador;

                    var dispositivoId =
                        await CadastrarNovoDispositivo(dados);

                    if (dispositivoId != null)
                    {
                        dispositivo =
                            await _dispositivoRepository.FirstOrDefault(x => x.Id == dispositivoId);
                    }
                }
                else
                {
                    if (dispositivo.ContaFinanceiraId != null)
                    {
                        contaFinanceiraViewModel =
                            await _contaFinanceiraService.Obter(dispositivo.ContaFinanceiraId.Value);

                        contaFinanceiraViewModel.Nome = ativarLicencaViewModel.Apelido;
                        contaFinanceiraViewModel.Ativo = true;

                        await _contaFinanceiraService.Alterar(contaFinanceiraViewModel);
                    }

                    dispositivo.Ativo = true;
                    dispositivo.Apelido = ativarLicencaViewModel.Apelido;
                    dispositivo.Identificador = ativarLicencaViewModel.Identificador;
                    dispositivo.DataHoraUltimaAlteracao = DateTime.UtcNow;

                    await _dispositivoRepository.Update(dispositivo);

                    await _dispositivoRepository.SaveChanges();
                }

                dados.Id = dispositivo.Id;
                dados.Identificador = dispositivo.Identificador;
                dados.Apelido = dispositivo.Apelido;
                dados.Origem = dispositivo.Origem;
                dados.Codigo = dispositivo.Codigo;
                dados.Ativo = dispositivo.Ativo;
                dados.Pin = ativarLicencaViewModel.Pin;
                dados.DataHoraUltimaAlteracao = dispositivo.DataHoraUltimaAlteracao;

                //if (contaFinanceiraViewModel != null)
                //    dados.SerialPos = contaFinanceiraViewModel.SerialPOS;

                await _cacheService.RemoverAsync(ativarLicencaViewModel.Pin,
                                                 false);

                await _cacheService.AdicionarAsync(
                    new CacheModel
                    {
                        Chave = string.Format(CacheKeys.Pdv, ativarLicencaViewModel.Pin),
                        ExpirarAposInatividade = TimeSpan.FromDays(30 * 3),
                        ExpirarEm = DateTime.UtcNow.AddMonths(3),
                        Valor = dados
                    },
                    false);

                _aspNetUserInfo.PreencherCodigoContaEmpresa(dados.CodigoContaEmpresa);

                return dados;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task MarcarDispositivoComDataHoraUltimaSincronizacao(
            Guid dispositivoId)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var dispositivo =
                    await _dispositivoRepository.FirstOrDefault(x => x.Id == dispositivoId);

                if (dispositivo != null)
                {
                    dispositivo.DataHoraUltimaSincronizacao = DateTime.UtcNow;

                    await _dispositivoRepository.Update(dispositivo);

                    await _dispositivoRepository.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task<PdvAutonomoDispositivoViewModel> ObterDadosPin(
            string pin)
        {
            try
            {
                var pdvAutonomoDispositivoViewModel =
                    await _cacheService.ObterAsync<PdvAutonomoDispositivoViewModel>(string.Format(CacheKeys.Pdv, pin), false);

                return pdvAutonomoDispositivoViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<Guid?> EnviarVenda(
            OperacaoIntegracaoViewModel operacaoIntegracaoViewModel)
        {
            var result =
                 await _integracaoPedidoPdvAutonomoService.GerarOperacaoPorIntegracao(operacaoIntegracaoViewModel);

            if (PossuiAvisos() || PossuiErros())
            {
                NotificarAviso("Houve um erro para gravar a venda.");

                return null;
            }

            return result;
        }

        public async Task<Guid?> EnviarNotaFiscal(
            NotaFiscalViewModel notaFiscalViewModel)
        {
            var notaFiscal =
                await _documentoFiscalRepository.FirstOrDefault(c => c.ChaveAcesso == notaFiscalViewModel.ChaveAcesso);

            if (notaFiscal != null)
            {
                notaFiscalViewModel.Id = notaFiscal.Id;

                await _notaFiscalService.AlterarPorIntegracao(notaFiscalViewModel);

                return notaFiscal.Id;
            }

            var result =
                 await _notaFiscalService.CadastrarPorIntegracao(notaFiscalViewModel);

            if (PossuiAvisos() || PossuiErros())
            {
                NotificarAviso("Houve um erro para gravar a nota fiscal.");

                return null;
            }

            return result;
        }

        public async Task<Guid?> EnviarCupomSat(
            CupomSatViewModel cupomSatViewModel)
        {
            var loja =
                await _lojaRepository.FirstOrDefaultAsNoTracking(c => c.Id == cupomSatViewModel.LojaId);

            if (loja == null)
                return null;

            if (string.IsNullOrEmpty(cupomSatViewModel.ChaveAcessoAutorizado))
                return null;

            var cupomSat =
                await _cupomSatRepository.FirstOrDefault(c => c.ChaveAcessoAutorizado == cupomSatViewModel.ChaveAcessoAutorizado.Replace("CFe", ""));

            if (cupomSat != null)
            {
                cupomSatViewModel.Id = cupomSat.Id;

                await _cupomSatService.AlterarPorIntegracao(loja.CpfCnpj, cupomSatViewModel);

                return cupomSat.Id;
            }

            var result =
                 await _cupomSatService.CadastrarPorIntegracao(loja.CpfCnpj, cupomSatViewModel);

            if (PossuiAvisos() || PossuiErros())
            {
                NotificarAviso("Houve um erro para gravar a cupom sat.");

                return null;
            }

            return result;
        }

        public async Task<bool> EnviarInutilizacao(
            EnviarInutilizacaoViewModel enviarInutilizacaoViewModel)
        {
            var loja =
                await _lojaRepository.FirstOrDefaultAsNoTracking(c => c.Id == enviarInutilizacaoViewModel.LojaId);

            if (loja == null)
                return false;

            var inutilizacao =
                await _documentoFiscalRepository.FirstOrDefault(c => c.ChaveAcesso == enviarInutilizacaoViewModel.ChaveAcesso);

            if (inutilizacao != null)
                return true;

            var retornoInutilizarXml =
                (RetornoInutilizarXml)ConverterXml.ConverterStringXmlParaObjeto(enviarInutilizacaoViewModel.ArquivoXml, typeof(RetornoInutilizarXml));

            var result =
                 await _inutilizacaoService.CadastrarPorIntegracao(loja, retornoInutilizarXml, enviarInutilizacaoViewModel.ChaveAcesso);

            if (PossuiAvisos() || PossuiErros())
            {
                NotificarAviso("Houve um erro para gravar a inutilização.");

                return false;
            }

            return result;
        }

        public async Task<PdvAutonomoConfiguracaoViewModel> ObterConfiguracao()
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var configuracao = JsonConvert.DeserializeObject<PdvAutonomoConfiguracaoViewModel>(integracao.Configuracoes);

                var integracaoZoop = await _integracaoService.ObterIntegracaoSTI3PAY();

                if (integracaoZoop is not null)
                {
                    var configuracaoZoop = ZoopConfigViewModel.GetConfigZoop(integracaoZoop.Configuracoes);
                    configuracao.ZoopSellerId = configuracaoZoop?.Credenciamento?.id;
                }
                return configuracao;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<LojaViewModel>> ObterListaLoja(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaLoja =
                    await _lojaRepository.ObterListaCompleta(dataAtualizacao, _aspNetUserInfo.LojaId.Value);

                var listaLojaViewModel =
                    _mapper.Map<List<LojaViewModel>>(listaLoja);

                foreach (var lojaViewModel in listaLojaViewModel)
                {
                    var loja =
                        listaLoja?.Where(c => c.Id == lojaViewModel.Id)
                                 ?.FirstOrDefault();

                    if (loja != null)
                    {
                        if (loja.LojaFiscal != null)
                        {
                            #region NFCe

                            var ultimoNumeroNFCe =
                                _documentoFiscalRepository.ConsultarUltimoNumeroNotaFiscal(ModeloFiscal.NFCe,
                                                                                           loja.Id,
                                                                                           loja.LojaFiscal.NFCeNumeroSerie);

                            var numeroNFCe =
                                Convert.ToInt32(ultimoNumeroNFCe);

                            if (loja.LojaFiscal.NFCeNumeracaoInicial > numeroNFCe)
                                numeroNFCe = loja.LojaFiscal.NFCeNumeracaoInicial;
                            else
                                ++numeroNFCe;

                            loja.LojaFiscal.NFCeNumeracaoInicial = numeroNFCe;

                            #endregion

                            #region NFe

                            var ultimoNumeroNFe =
                                _documentoFiscalRepository.ConsultarUltimoNumeroNotaFiscal(ModeloFiscal.NFe,
                                                                                           loja.Id,
                                                                                           loja.LojaFiscal.NFeNumeroSerie);

                            var numeroNFe = Convert.ToInt32(ultimoNumeroNFe);

                            if (loja.LojaFiscal.NFeNumeracaoInicial > numeroNFe)
                                numeroNFe = loja.LojaFiscal.NFeNumeracaoInicial;
                            else
                                ++numeroNFe;

                            loja.LojaFiscal.NFeNumeracaoInicial = numeroNFe;

                            #endregion
                        }
                    }
                }

                return listaLojaViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<MarcaViewModel>> ObterListaMarca(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaMarca =
                    await _marcaRepository.ObterListaCompleta(dataAtualizacao);

                var listaMarcaViewModel =
                    _mapper.Map<List<MarcaViewModel>>(listaMarca);

                return listaMarcaViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<CorViewModel>> ObterListaCor(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaCor =
                    await _corRepository.ObterListaCompleta(dataAtualizacao);

                var listaCorViewModel =
                    _mapper.Map<List<CorViewModel>>(listaCor);

                return listaCorViewModel?.OrderByDescending(c => c.PadraoSistema)
                                        ?.ToList();
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<TamanhoViewModel>> ObterListaTamanho(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaTamanho =
                    await _tamanhoRepository.ObterListaCompleta(dataAtualizacao);

                var listaTamanhoViewModel =
                    _mapper.Map<List<TamanhoViewModel>>(listaTamanho);

                return listaTamanhoViewModel?.OrderByDescending(c => c.PadraoSistema)
                                            ?.ToList();
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<CategoriaProdutoViewModel>> ObterListaCategoria(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaCategoria =
                    await _categoriaProdutoRepository.ObterListaCompleta(dataAtualizacao);

                var listaCategoriaViewModel =
                    _mapper.Map<List<CategoriaProdutoViewModel>>(listaCategoria);

                return listaCategoriaViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<UnidadeMedidaViewModel>> ObterListaUnidade(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaUnidadeMedida =
                    await _unidadeMedidaRepository.ObterListaCompleta(dataAtualizacao);

                var listaUnidadeMedidaViewModel =
                    _mapper.Map<List<UnidadeMedidaViewModel>>(listaUnidadeMedida);

                return listaUnidadeMedidaViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<PdvAutonomoFatorConversaoViewModel>> ObterListaFatorConversao(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaFatorConversao =
                    await _produtoRepository.ObterListaFatorConversaoCompleto(dataAtualizacao);

                var listaFatorConversaoViewModel =
                    _mapper.Map<List<PdvAutonomoFatorConversaoViewModel>>(listaFatorConversao);

                return listaFatorConversaoViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<PdvAutonomoAliquotaIcmsViewModel>> ObterListaAliquotaIcms(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaAliquotaIcms =
                    await _produtoRepository.ObterListaAliquotaIcmsCompleto(dataAtualizacao);

                var listaAliquotaIcmsViewModel =
                    _mapper.Map<List<PdvAutonomoAliquotaIcmsViewModel>>(listaAliquotaIcms);

                return listaAliquotaIcmsViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<VendedorViewModel>> ObterListaVendedor(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaVendedor =
                    await _vendedorRepository.ObterListaCompleto(dataAtualizacao, _aspNetUserInfo.LojaId.Value);

                var listaVendedorViewModel =
                    _mapper.Map<List<VendedorViewModel>>(listaVendedor);

                return listaVendedorViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<PdvAutonomoUsuarioViewModel>> ObterListaUsuario(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaUsuario =
                    await _usuarioRepository.ObterListaCompleto(dataAtualizacao, _aspNetUserInfo.LojaId.Value);

                var listaUsuarioViewModel =
                    _mapper.Map<List<PdvAutonomoUsuarioViewModel>>(listaUsuario);

                return listaUsuarioViewModel?.OrderByDescending(c => c.Administrador)
                                            ?.ToList();
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<PdvAutonomoUsuarioPermissaoViewModel>> ObterListaUsuarioPermissao(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao = await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaUsuarioPermissao =
                    await _usuarioPermissaoRepository.ObterListaCompleta(dataAtualizacao, _aspNetUserInfo.LojaId.Value);

                var listaUsuarioPermissaoViewModel =
                    _mapper.Map<List<PdvAutonomoUsuarioPermissaoViewModel>>(listaUsuarioPermissao);

                return listaUsuarioPermissaoViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<FormaPagamentoRecebimentoViewModel>> ObterListaFormaPagamentoRecebimento(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaFormaPagamentoRecebimento =
                    await _formaPagamentoRecebimentoRepository.ObterListaCompleta(dataAtualizacao);

                var listaFormaPagamentoRecebimentoViewModel =
                    new List<FormaPagamentoRecebimentoViewModel>();

                foreach (var formaPagamento in listaFormaPagamentoRecebimento)
                {
                    var formaPagamentoRecebimentoViewModel =
                        _mapper.Map<FormaPagamentoRecebimentoViewModel>(formaPagamento);

                    formaPagamentoRecebimentoViewModel.RegraMeioPagamento = formaPagamento.MeioPagamento.RegraMeioPagamento;
                    formaPagamentoRecebimentoViewModel.Parcelas =
                        _mapper.Map<List<FormaPagamentoParcelaViewModel>>(formaPagamento.FormaPagamentoParcelas);

                    if (!string.IsNullOrEmpty(formaPagamentoRecebimentoViewModel.Icone))
                    {
                        formaPagamentoRecebimentoViewModel.Icone =
                            _storageService.ObterCaminhoIconeBancoDados(StorageContaArmazenamento.App,
                                                                        CaminhoArquivosStorage.IconeFinanceiros,
                                                                        CaminhoArquivosStorage.Bancos,
                                                                        formaPagamentoRecebimentoViewModel.Icone);
                    }

                    listaFormaPagamentoRecebimentoViewModel.Add(formaPagamentoRecebimentoViewModel);
                }

                return listaFormaPagamentoRecebimentoViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<RegraFiscalViewModel>> ObterListaRegraFiscal(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaCstOrigem =
                    await _produtoRepository.ObterListaCstOrigem();

                var listaRegraFiscal =
                    await _regraFiscalRepository.ObterListaCompleta(dataAtualizacao);

                var listaRegraFiscalCstOrigemViewModel =
                    new List<RegraFiscalViewModel>();

                foreach (var cstOrigem in listaCstOrigem)
                {
                    var listaRegraFiscalViewModel =
                        _mapper.Map<List<RegraFiscalViewModel>>(listaRegraFiscal);

                    foreach (var regraFiscalViewModel in listaRegraFiscalViewModel)
                    {
                        var regraFiscalItens =
                            regraFiscalViewModel.RegraFiscalItens?.Where(c => c.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA)
                                                                 ?.ToList();

                        var listaRegimeTributario =
                            regraFiscalItens?.Select(c => c.RegimeTributario)
                                            ?.Distinct()
                                            ?.ToList();

                        foreach (var regimeTributario in listaRegimeTributario)
                        {
                            var regraFiscalDistinctViewModel = new RegraFiscalViewModel();
                            regraFiscalDistinctViewModel.Id = regraFiscalViewModel.Id;
                            regraFiscalDistinctViewModel.IdCustom = $"{regraFiscalViewModel.Id};{Enum.GetName(regimeTributario)}";
                            regraFiscalDistinctViewModel.Nome = regraFiscalViewModel.Nome;
                            regraFiscalDistinctViewModel.Ativo = regraFiscalViewModel.Ativo;
                            regraFiscalDistinctViewModel.CstOrigem = Convert.ToString((int)cstOrigem);
                            regraFiscalDistinctViewModel.DataHoraCadastro = regraFiscalViewModel.DataHoraCadastro;
                            regraFiscalDistinctViewModel.DataHoraUltimaAlteracao = regraFiscalViewModel.DataHoraUltimaAlteracao;
                            regraFiscalDistinctViewModel.RegraFiscalItens = regraFiscalItens?.Where(c => c.RegimeTributario == regimeTributario)
                                                                                            ?.ToList();

                            listaRegraFiscalCstOrigemViewModel.Add(regraFiscalDistinctViewModel);
                        }
                    }
                }

                return listaRegraFiscalCstOrigemViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<ClienteFornecedorViewModel>> ObterListaCliente(
            GridPaginadaConsulta gridPaginada,
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaClienteFornecedor =
                     _clienteFornecedorRepository.ObterListaPaginadaCompleta(gridPaginada, dataAtualizacao);

                var listaClienteFornecedorViewModel =
                    _mapper.Map<List<ClienteFornecedorViewModel>>(listaClienteFornecedor.Registros);

                Loja loja = await _lojaRepository.ObterInformacoesEndereco(_aspNetUserInfo.LojaId.Value);
                var enderecoVm = EnderecoViewModel.LojaEnderecoParaEnderecoViewModel(loja);

                foreach (var clienteFornecedorPadraoSistema in listaClienteFornecedorViewModel.Where(c => c.PadraoSistema))
                {
                    clienteFornecedorPadraoSistema.AlterarEnderecoClienteParaEnderecoLoja(enderecoVm);
                }

                return listaClienteFornecedorViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<ProdutoV2ViewModel>> ObterListaProduto(
            GridPaginadaConsulta gridPaginada,
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao = await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var configuracoes =
                    JsonConvert.DeserializeObject<PdvAutonomoConfiguracaoViewModel>(integracao.Configuracoes);

                var gridPaginadaRetornoProduto =
                     _produtoRepository.ObterListaPaginadaCompleta(_aspNetUserInfo.LojaId.Value, gridPaginada, dataAtualizacao);

                var listaProdutoV2ViewModel =
                    gridPaginadaRetornoProduto?.Registros
                                              ?.Select(c => c.ToViewModel())
                                              ?.ToList();

                var configuracaoTabelaPrecoViewModel =
                    configuracoes.TabelaPreco;

                var tabelaPrecoAVistaDTO =
                    await _tabelaPrecoV2Service.Obter(configuracaoTabelaPrecoViewModel.TabelaPrecoIdAVista);

                var tabelaPrecoAPrazoDTO =
                    await _tabelaPrecoV2Service.Obter(configuracaoTabelaPrecoViewModel.TabelaPrecoIdAPrazo);

                TabelaPrecoDTO tabelaPrecoAlternativoDTO = null;

                if (configuracaoTabelaPrecoViewModel.TabelaPrecoIdAlternativo != null &&
                    configuracaoTabelaPrecoViewModel.TabelaPrecoIdAlternativo != Guid.Empty)
                {
                    tabelaPrecoAlternativoDTO =
                        await _tabelaPrecoV2Service.Obter(configuracaoTabelaPrecoViewModel.TabelaPrecoIdAlternativo.Value);
                }

                var padronizacao =
                    await ObterPadronizacaoCasasDecimais();

                foreach (var produtoV2ViewModel in listaProdutoV2ViewModel)
                {
                    var precoDTO = produtoV2ViewModel?.Precos
                                                     ?.ProdutoPrecoLojas
                                                     ?.Where(y => y.LojaId == _aspNetUserInfo.LojaId.Value)
                                                     ?.Select(y => new PrecoDTO(y.PrecoVenda.PrecoVenda, y.PrecoCusto))
                                                     ?.FirstOrDefault();

                    if (tabelaPrecoAVistaDTO != null)
                        produtoV2ViewModel.PrecoAVista = PrecoProdutoCalculador.Calcular(tabelaPrecoAVistaDTO, new ProdutoVariacaoPrecoDTO(Guid.Empty, produtoV2ViewModel.Id, precoDTO), padronizacao);

                    if (tabelaPrecoAPrazoDTO != null)
                        produtoV2ViewModel.PrecoAPrazo = PrecoProdutoCalculador.Calcular(tabelaPrecoAPrazoDTO, new ProdutoVariacaoPrecoDTO(Guid.Empty, produtoV2ViewModel.Id, precoDTO), padronizacao);

                    if (tabelaPrecoAlternativoDTO != null)
                        produtoV2ViewModel.PrecoAlternativo = PrecoProdutoCalculador.Calcular(tabelaPrecoAlternativoDTO, new ProdutoVariacaoPrecoDTO(Guid.Empty, produtoV2ViewModel.Id, precoDTO), padronizacao);

                    if (produtoV2ViewModel.InformacoesFiscais != null)
                    {
                        if (!string.IsNullOrEmpty(produtoV2ViewModel.InformacoesFiscais.CodigoAnp))
                        {
                            produtoV2ViewModel.InformacoesFiscais.DescricaoProdutoANP =
                                await _anpRepository.ObterDescricaoPorCodigo(produtoV2ViewModel.InformacoesFiscais.CodigoAnp);
                        }
                    }

                    foreach (var produtoCorV2ViewModel in produtoV2ViewModel.ProdutoCores)
                    {
                        foreach (var produtoCorTamanhoViewModel in produtoCorV2ViewModel.ProdutoCorTamanhos)
                        {
                            if (precoDTO != null)
                            {
                                var precoVariacao =
                                    new ProdutoVariacaoPrecoDTO(produtoCorTamanhoViewModel.Id, produtoV2ViewModel.Id, precoDTO);

                                if (precoVariacao != null)
                                {
                                    produtoCorTamanhoViewModel.PrecoAVista =
                                        PrecoProdutoCalculador.Calcular(tabelaPrecoAVistaDTO, precoVariacao, padronizacao);

                                    produtoCorTamanhoViewModel.PrecoAPrazo =
                                        PrecoProdutoCalculador.Calcular(tabelaPrecoAPrazoDTO, precoVariacao, padronizacao);

                                    if (tabelaPrecoAlternativoDTO != null)
                                    {
                                        produtoCorTamanhoViewModel.PrecoAlternativo =
                                            PrecoProdutoCalculador.Calcular(tabelaPrecoAlternativoDTO, precoVariacao, padronizacao);
                                    }
                                }
                            }
                        }
                    }
                }

                return listaProdutoV2ViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<CredenciadoraCartaoViewModel>> ObterListaCredenciadoraCartao(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaCredenciadoraCartao =
                    await _credenciadoraCartaoRepository.ObterListaCompleta(dataAtualizacao);


                var listaCredenciadoraCartaoViewModel =
                    _mapper.Map<List<CredenciadoraCartaoViewModel>>(listaCredenciadoraCartao);

                return listaCredenciadoraCartaoViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<SincronizarNotificacaoViewModel>> ObterListaNotificacaoPorDispositivo(
            Guid dispositivoId)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaSincronizarNotificacaoViewModel =
                    await _notificacaoService.ObterListaCompletaPorDispositivoId(dispositivoId);

                return listaSincronizarNotificacaoViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task MarcarNotificacaoComoSincronizada(
            Guid notificacaoId)
        {
            await _notificacaoService.AlterarParaSincronizado(notificacaoId);
        }

        public async Task<List<SincronizarNotificacaoExclusaoViewModel>> ObterListaNotificacaoExclusaoPorDispositivo(
            Guid dispositivoId)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaSincronizarNotificacaoExclusaoViewModel =
                    await _notificacaoExclusaoService.ObterListaCompletaPorDispositivoId(dispositivoId);

                return listaSincronizarNotificacaoExclusaoViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task MarcarNotificacaoExclusaoComoSincronizada(
            Guid notificacaoId)
        {
            await _notificacaoExclusaoService.AlterarParaSincronizado(notificacaoId);
        }

        public async Task<List<PeriodoCaixaViewModel>> ObterListaPeriodoCaixa(
            DateTime? dataAtualizacao)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaPeriodoCaixa =
                    await _periodoCaixaRepository.ObterListaCompleta(dataAtualizacao);

                var listaPeriodoCaixaViewModel =
                    _mapper.Map<List<PeriodoCaixaViewModel>>(listaPeriodoCaixa);

                return listaPeriodoCaixaViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        #endregion

        public void Dispose()
        {
        }

        #endregion

        #region Private Methods

        private static List<IntegracaoSituacaoPedidoViewModel> ObterListaSituacaoPadrao()
        {
            return new List<IntegracaoSituacaoPedidoViewModel>()
            {
                new IntegracaoSituacaoPedidoViewModel("Concluída", "Concluída", IdentificacaoSituacao.FINALIZADO),
                new IntegracaoSituacaoPedidoViewModel("Cancelada", "Cancelada", IdentificacaoSituacao.CANCELADO)
            };
        }

        private async Task<Guid?> CadastrarNovoDispositivo(
            PdvAutonomoDispositivoViewModel dados)
        {
            var ultimoCodigoCadastrado =
                await _dispositivoRepository.ObterUltimoCodigoCadastrado(dados.LojaId);

            var dispositivo = new Dispositivo
            {
                Identificador = dados.Identificador,
                LojaId = dados.LojaId,
                Ativo = dados.Ativo,
                DataHoraCadastro = DateTime.UtcNow,
                DataHoraUltimaAlteracao = DateTime.UtcNow,
                Apelido = dados.Apelido,
                Origem = "",
                Codigo = ultimoCodigoCadastrado + 1,
                ReferenciaServico = ReferenciaServicoStargate.DISPOSITIVO_PDV
            };

            await _dispositivoRepository.Insert(dispositivo);

            var contaFinanceiraExistente =
                await _contaFinanceiraRepository.FirstOrDefaultAsNoTracking(c => c.Nome == dispositivo.Apelido);

            Guid? contaFinanceiraId = null;

            if (contaFinanceiraExistente == null)
            {
                contaFinanceiraId =
                    await _contaFinanceiraService.Cadastrar(
                        new ContaFinanceiraViewModel
                        {
                            LojaId = dados.LojaId,
                            Nome = dispositivo.Apelido,
                            TipoContaFinanceira = TipoContaFinanceira.CAIXA,
                            Ativo = true,
                            PadraoSistema = false,
                        });
            }
            else
                contaFinanceiraId = contaFinanceiraExistente.Id;

            dispositivo.ContaFinanceiraId = contaFinanceiraId;

            await _dispositivoRepository.Update(dispositivo);

            await _dispositivoRepository.SaveChanges();

            return dispositivo.Id;
        }

        private async Task<PadronizacaoCasaDecimalDTO> ObterPadronizacaoCasasDecimais()
        {
            var padronizacao =
                await _padronizacaoService.ObterCasasDecimais();

            return padronizacao ?? PadronizacaoCasaDecimalDTO.CriarPadrao();
        }

        protected async Task SetZendarTriggerCache(
            Guid? lojaId = null)
        {
            bool possuiIntegracao = false;

            var integracaoPdvAutonomo =
                await ObterIntegracao(lojaId);

            if (integracaoPdvAutonomo != null)
            {
                if (integracaoPdvAutonomo.SincronizacaoHabilitada)
                    possuiIntegracao = true;
            }

            await _sincronizacaoIntegracaoService.HabilitarTodas();
        }

        #endregion

        #region Remover

        public async Task NotificarExportacao(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<PdvAutonomoConfiguracaoViewModel>(integracao.Configuracoes);

                await _integracaoService.Alterar(
                    new IntegracaoViewModel
                    {
                        Id = integracao.Id,
                        SincronizacaoHabilitada = true,
                        LocalEstoqueId = integracao.LocalEstoqueId,
                        Ativo = integracao.Ativo
                    },
                    JsonConvert.SerializeObject(configuracoes));

                await SetZendarTriggerCache();

                var usuario =
                    await _usuarioRepository.BuscarPorId(notificacaoViewModel.UsuarioId);

                if (usuario != null)
                {
                    var listaEmail = new List<string>();
                    listaEmail.Add(usuario.Email);

                    await _envioEmailService.EnviarEmailZendarSync("Exportação da base de dados foi concluída",
                                                                   listaEmail);

                    await _notificationHubService.Notificar(new Guid(_aspNetUserInfo.LojaId?.ToString()),
                                                            notificacaoViewModel.UsuarioId,
                                                            "pdv-autonomo",
                                                            "exportacao-base-pdv");
                }
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task NotificarScript(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes =
                    JsonConvert.DeserializeObject<PdvAutonomoConfiguracaoViewModel>(integracao.Configuracoes);

                configuracoes.UrlScript = notificacaoViewModel.Mensagem;
                configuracoes.DataScript = DateTime.UtcNow;

                await _integracaoService.Alterar(
                    new IntegracaoViewModel
                    {
                        Id = integracao.Id,
                        SincronizacaoHabilitada = integracao.SincronizacaoHabilitada,
                        LocalEstoqueId = integracao.LocalEstoqueId,
                        Ativo = integracao.Ativo
                    },
                    JsonConvert.SerializeObject(configuracoes));

                var usuario =
                    await _usuarioRepository.BuscarPorId(notificacaoViewModel.UsuarioId);

                if (usuario != null)
                {
                    var listaEmail = new List<string>();
                    listaEmail.Add(usuario.Email);

                    await _envioEmailService.EnviarEmailZendarSync("Geração do script foi concluída",
                                                                   listaEmail);

                    await _notificationHubService.Notificar(new Guid(_aspNetUserInfo.LojaId?.ToString()),
                                                            notificacaoViewModel.UsuarioId,
                                                            "pdv-autonomo",
                                                            "gerar-script-pdv");
                }
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task<string> ObterUrlScript()
        {
            try
            {
                var integracao =
                    await ObterIntegracao();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var configuracoes =
                    JsonConvert.DeserializeObject<PdvAutonomoConfiguracaoViewModel>(integracao.Configuracoes);

                return configuracoes.UrlScript;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        #endregion
    }
}