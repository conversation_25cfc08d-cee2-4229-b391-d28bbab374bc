﻿using System;
using System.Collections.Generic;
using System.Linq;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Models.Aplicacao;

namespace Zendar.Data.DomainServices.UsuarioServices
{
	public static class LicencaUsuarioService
	{
		public static int CalcularQuantidadeLicenca(
			List<Loja> lojas,
			ReferenciaServicoStargate servico,
			DateTime dataBloqueio)
		{
			if (lojas?.Any() != true) return 0;

			var quantidadeLicencasUsuario = lojas.Sum(l => l.ObterQuantidadeServicoAtivos(servico, dataBloqueio));

			if (servico == ReferenciaServicoStargate.USUARIO_LICENCA)
				quantidadeLicencasUsuario += lojas.Count(l => !l.<PERSON>(dataBloqueio));

			return quantidadeLicencasUsuario;
		}

        public static int CalcularQuantidadeLicencaTotais(
                List<Loja> lojas,
                ReferenciaServicoStargate servico)
        {
            if (lojas?.Any() != true) return 0;

            int quantidadeLicencasUsuario = lojas.Sum(l => l.ObterQuantidadeServicoTotais(servico));

            if (servico == ReferenciaServicoStargate.USUARIO_LICENCA)
                quantidadeLicencasUsuario += lojas.Count;

            return quantidadeLicencasUsuario;
        }

        public static int CalcularQuantidadeExcedentePorTipoUsuario(
			List<Loja> lojas,
			int tipoUsuarioQuantidadeAtiva)
		{
			return tipoUsuarioQuantidadeAtiva - CalcularQuantidadeLicencaTotais(lojas, ReferenciaServicoStargate.USUARIO_LICENCA);
		}
	}
}
