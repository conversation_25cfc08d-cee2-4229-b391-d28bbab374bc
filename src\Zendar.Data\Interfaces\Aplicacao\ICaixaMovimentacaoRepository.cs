﻿using Multiempresa.Shared.Helpers;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.Data.Helpers;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.ViewModels;

namespace Zendar.Data.Interfaces.Aplicacao
{
    public interface ICaixaMovimentacaoRepository : IRepository<CaixaMovimentacao>
    {
        GridPaginadaRetorno<ControleCaixaPaginadaViewModel> ListarPaginado(GridPaginadaConsulta gridPaginada, ControleCaixaFiltrosPaginadaViewModel filtros, Guid lojaId, Guid usuarioId, bool listarTodosCaixas);
        GridPaginadaRetorno<ControleCaixaPaginadaViewModel> ListarCaixaMovelPaginado(GridPaginadaConsulta gridPaginada, ControleCaixaFiltrosPaginadaViewModel filtros, Guid lojaId, Guid usuarioId, bool listarTodosCaixas);
        Task<CaixaViewModel> ObterFechamentoCaixaSimples(Guid idCaixaMovimentacao);
        Task<CaixaMovimentacao> ObterParaFecharCaixa(Guid idCaixaMovimentacao);
        Task<Guid?> ObterIdUltimaMovimentacaoCaixa(Guid contaFinanceira);
        Task<CaixaMovimentacao> ObterInformacoesCaixa(Guid id);
        Task<CaixaMovimentacao> ObterMovimentacaoDispositivo(string identificador, Guid lojaId, Guid usuarioId, Guid? caixaMovimentacaoId = null, Guid? agrupamentoIntegracaoId = null);
        Task<CaixaMovimentacao> ObterUltimoCaixaMovimentacaoAberto(Guid contaFinanceiraId, Guid lojaId);
        Task<Guid> ObterIdContaFinanceiraPeloCaixaMovimentacao(Guid caixaMovimentacaoId);
        Task<CaixaMovimentacao> ObterConferenciaDinheiroCheque(Guid caixaMovimentacao);
        Task<List<Guid>> ObterListaIdCaixaAbertoUsuario(Guid usuarioId, Guid lojaId);
        Task<CaixaMovimentacao> ObterComOperacoesParaVincularAgrupamnetoIntegracaoId(Guid caixaMovimentacaoId);
        Task<CaixaMovimentacao> ObterComOperacoesParaVincularAgrupamnetoIntegracaoId(Guid lojaId, string identificadorDispositivo);
        Task<List<IdValorViewModel>> ObterMovimentacoes(Guid caixaMovimentacao);
        Task<bool> UltimoCaixaFechado(Guid lojaId, DateTime dataHoraFechamento);
        Task<CaixaMovimentacao> ObterUltimoCaixaPorContaFinanceira(Guid contaFinanceiraId);
        Task<CaixaMovimentacao> ObterComConferencias(Guid caixaMovimentacaoId);
        Task<CaixaMovimentacao> ObterPorAgrupamentoIntegracaoIdELojaId(
            Guid agrupamentoIntegracaoId,
            Guid lojaId);
        Task<CaixaMovimentacao> ObterPorAgrupamentoIntegracaoIdELojaIdEUsuarioId(
            Guid agrupamentoIntegracaoId, 
            Guid lojaId,
            Guid usuarioId);
    }
}
