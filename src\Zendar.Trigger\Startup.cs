using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Hosting;
using Microsoft.Extensions.DependencyInjection;
using System;
using Zendar.Business.Consts;
using Zendar.Business.DI;
using Zendar.Data.DI;
using zendar_trigger;

[assembly: FunctionsStartup(typeof(Startup))]

namespace zendar_trigger;

public class Startup : IWebJobsStartup
{
	void IWebJobsStartup.Configure(
		IWebJobsBuilder builder)
	{
		var defaultConnection = Environment.GetEnvironmentVariable(SystemConst.DEFAULT_CONNECTION);

		var multiEmpresaConnection = Environment.GetEnvironmentVariable(SystemConst.MULTI_EMPRESA_CONNECTION);

		builder.Services.AddRedisConfig();
		builder.Services.AddMediatR(typeof(Startup));
		builder.Services.RegistrarAutoMapper();
		builder.Services.RegistrarContextoFunction(defaultConnection, multiEmpresaConnection);
		builder.Services.RegistrarComuns(true);
		builder.Services.RegistrarFacade();
		builder.Services.RegistrarServicos();
		builder.Services.RegistrarRepositorios();
		builder.Services.RegistrarEventos();
		builder.Services.RegistrarComandos();
		builder.Services.AddHttpClient();
		builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
		builder.Services.AddMemoryCache();
	}
}
