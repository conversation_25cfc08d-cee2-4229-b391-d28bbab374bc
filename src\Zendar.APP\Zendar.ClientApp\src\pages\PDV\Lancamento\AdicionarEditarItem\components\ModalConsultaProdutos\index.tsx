import {
  ModalContent,
  ModalBody,
  useDisclosure,
  ModalHeader,
  Button,
  GridItem,
  Flex,
  Icon,
  Text,
  ModalProps,
  useMediaQuery,
} from '@chakra-ui/react';
import { useCallback, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';

import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';

import api, { ResponseApi } from 'services/api';

import ConsultaProdutosPdvProvider, {
  ConsultaProdutoPdvContext,
  Filtros,
  Produto,
} from 'store/PDV/ConsultaProdutoPdv';

import { GridPaginadaRetorno } from 'components/Grid/Paginacao';
import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import ModalListarSaldoVariacoes from 'components/Modal/ModalListarSaldoVariacoes';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import { PaginationData } from 'components/update/Pagination';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import TipoProdutoEnum from 'constants/enum/tipoProduto';
import { BuscaIcon } from 'icons';

import { DrawerFiltrosAvancados } from '../DrawerFiltrosAvancados';
import { VariacoesResponseProps } from '../VariacaoItem';

import { ConsultaProdutos } from './components/ConsultaProdutos';
import { ConsultaVariacoes } from './components/ConsultaVariacoes';
import { ModalFooterConsulta } from './components/ModalFooter';

type ModalConsultaProdutosPdvProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> &
  InstanceProps<{
    label: string;
    value: string;
    tipoProduto: number;
    codigoBarrasInterno?: string;
  }> & {
    casasDecimais: {
      casasDecimaisQuantidade: number;
    };
    exibirBotaoAdicionarProduto?: boolean;
  };

export enum EnumTelaConsultaProdutosPdv {
  PRODUTOS = 1,
  VARIACOES = 2,
}

export const ModalConsultaProdutos = create<
  ModalConsultaProdutosPdvProps,
  {
    label: string;
    value: string;
    tipoProduto: number;
    codigoBarrasInterno?: string;
  }
>(
  ({
    onResolve,
    onReject,
    casasDecimais,
    exibirBotaoAdicionarProduto = true,
    ...rest
  }) => {
    const [
      modalVisualizarEstoqueEstaAberto,
      setModalVisualizarEstoqueEstaAberto,
    ] = useState(false);
    const [produtoSelecionado, setProdutoSelecionado] =
      useState<Produto | null>(null);
    const [variacaoSelecionada, setVariacaoSelecionada] =
      useState<VariacoesResponseProps | null>(null);
    const [contextFilters, setContextFilters] = useState<Filtros | undefined>();

    const [tela, setTela] = useState(EnumTelaConsultaProdutosPdv.PRODUTOS);
    const [totalRegistrosGrade, setTotalRegistrosGrade] = useState(0);
    const [isLoadingGradeVariacoes, setIsLoadingGradeVariacoes] =
      useState(false);
    const [listarVariacoes, setListarVariacoes] = useState<
      VariacoesResponseProps[]
    >([]);

    const [isLargerThan1200] = useMediaQuery('(min-width: 1200px)');
    const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const {
      isOpen: isOpenDrawer,
      onOpen: onOpenDrawer,
      onClose: onCloseDrawer,
    } = useDisclosure({
      defaultIsOpen: true,
    });

    const lancarProdutoDesabilitado = produtoSelecionado === null;

    const formMethods = useForm();

    const fecharModal = () => {
      onReject();
      onClose();
    };

    const paginationHandleGrade = useCallback(
      async (paginationData: PaginationData, produtoId?: string) => {
        setIsLoadingGradeVariacoes(true);
        const idProduto = produtoId || produtoSelecionado?.id || '';
        const response = await api.get<
          void,
          ResponseApi<GridPaginadaRetorno<VariacoesResponseProps>>
        >(
          formatQueryPagegTable(
            `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${idProduto}/produtoCorTamanhos/grade`,
            paginationData,
            {
              id: idProduto,
              status: 1,
            }
          )
        );

        if (response?.sucesso) {
          setTotalRegistrosGrade(response.dados.total);
          setListarVariacoes(
            response.dados.registros.map((itemVariacao) => ({
              ...itemVariacao,
              isChecked: false,
            }))
          );
          setTela(EnumTelaConsultaProdutosPdv.VARIACOES);
          setIsLoadingGradeVariacoes(false);
        }

        setIsLoadingGradeVariacoes(false);
      },
      [produtoSelecionado]
    );

    const escolherVariacao = async (produtoId: string) => {
      const gridPaginadaConsulta = {
        currentPage: 1,
        orderColumn: 'Nome',
        orderDirection: 'asc',
        pageSize: 10,
      } as PaginationData;
      await paginationHandleGrade(gridPaginadaConsulta, produtoId);
    };

    const lancarProdutoSimples = (produto: {
      id: string;
      nome: string;
      precoVenda: number;
      estoque: number;
    }) => {
      onResolve({
        label: produto.nome,
        value: produto.id,
        tipoProduto: TipoProdutoEnum.PRODUTO_SIMPLES,
      });
      onClose();
    };

    const lancarProdutoVariacao = (variacao: VariacoesResponseProps) => {
      onResolve({
        label: variacao.produtoCorTamanho.cor,
        value: produtoSelecionado?.id || '',
        tipoProduto: TipoProdutoEnum.PRODUTO_VARIACAO,
        codigoBarrasInterno: variacao.identificadores?.codigoBarrasInterno,
      });
      onClose();
    };

    const toggleSelect = useCallback((produto: Produto) => {
      setProdutoSelecionado(produto);
    }, []);

    const toggleSelectVariacao = useCallback(
      (variacao: VariacoesResponseProps) => {
        setVariacaoSelecionada(variacao);
      },
      []
    );

    const handleDoubleClickVariacao = useCallback(
      (variacao: VariacoesResponseProps) => {
        if (!exibirBotaoAdicionarProduto) return;
        if (variacao) {
          lancarProdutoVariacao(variacao);
        }
      },
      []
    );

    const submitFiltros = useCallback(async (data: Filtros) => {
      setContextFilters(data);
      setProdutoSelecionado(null);
      setVariacaoSelecionada(null);
      setTela(EnumTelaConsultaProdutosPdv.PRODUTOS);
    }, []);

    return (
      <ModalPadraoChakra
        isCentered={false}
        size="full"
        {...rest}
        isOpen={isOpen}
        onClose={() => {
          fecharModal();
        }}
      >
        <ConsultaProdutosPdvProvider
          formMethods={formMethods}
          key="ConsultaProdutosPdvProvider"
          contextFiltros={contextFilters}
        >
          <ConsultaProdutoPdvContext.Consumer>
            {({ isLoading, isLoadingPesquisa }) => {
              return (
                <ModalContent
                  h="unset"
                  bg="gray.50"
                  borderRadius="0px"
                  transition="right 0.5s ease-in-out, width 0.5s ease-in-out"
                  w={
                    isLargerThan1200 && isOpenDrawer && !isLoading
                      ? 'calc(100% - 502px)'
                      : '100%'
                  } // 502px é a largura do drawer
                  right={
                    isLargerThan1200 && isOpenDrawer && !isLoading
                      ? '255px'
                      : '0'
                  }
                  autoFocus={false}
                >
                  {(isLoading ||
                    isLoadingPesquisa ||
                    isLoadingGradeVariacoes) && <LoadingPadrao />}
                  <ModalHeader
                    px="32px"
                    py="20px"
                    color="violet.500"
                    fontWeight="normal"
                    fontSize="18px"
                  >
                    <Flex align="center" w="full" height="32px">
                      <Text>Consulta de produtos</Text>
                      {(!isOpenDrawer || !isLargerThan1200) && (
                        <Button
                          margin="0 0 0 auto"
                          colorScheme="gray"
                          variant="outlineDefault"
                          borderRadius="full"
                          w="120px"
                          height="32px"
                          transition="all 300ms"
                          _hover={{
                            '& svg': {
                              color: 'white',
                            },
                            bg: 'gray.500',
                            color: 'white',
                          }}
                          borderColor="gray.200"
                          onClick={
                            isLargerThan1200 || !isOpenDrawer
                              ? onOpenDrawer
                              : onCloseDrawer
                          }
                          leftIcon={
                            <Icon
                              as={BuscaIcon}
                              fontSize="12px"
                              color="black"
                              transition="all 300ms"
                            />
                          }
                        >
                          Filtros
                        </Button>
                      )}
                    </Flex>
                  </ModalHeader>
                  <ModalBody px="32px" pt="0px">
                    <SimpleGridForm
                      gap={{ base: '10px', sm: '10px', md: 8 }}
                      mb="60px"
                    >
                      <FormProvider {...formMethods}>
                        <GridItem mt={['10px', '10px', '0px']} colSpan={12}>
                          <ConsultaProdutos
                            tela={tela}
                            exibirBotaoAdicionarProduto={
                              exibirBotaoAdicionarProduto
                            }
                            produtoSelecionado={produtoSelecionado}
                            casasDecimais={casasDecimais}
                            isOpenDrawer={isOpenDrawer}
                            setModalVisualizarEstoqueEstaAberto={
                              setModalVisualizarEstoqueEstaAberto
                            }
                            toggleSelect={toggleSelect}
                            escolherVariacao={escolherVariacao}
                            setProdutoSelecionado={setProdutoSelecionado}
                            onCloseDrawer={onCloseDrawer}
                            lancarProdutoSimples={lancarProdutoSimples}
                          />
                          <ConsultaVariacoes
                            tela={tela}
                            paginationHandleGrade={paginationHandleGrade}
                            totalRegistrosGrade={totalRegistrosGrade}
                            listarVariacoes={listarVariacoes}
                            casasDecimais={casasDecimais}
                            toggleSelectVariacao={toggleSelectVariacao}
                            variacaoSelecionada={variacaoSelecionada}
                            handleDoubleClickVariacao={
                              handleDoubleClickVariacao
                            }
                          />
                        </GridItem>
                      </FormProvider>
                    </SimpleGridForm>
                  </ModalBody>
                  <ModalFooterConsulta
                    tela={tela}
                    produtoSelecionado={produtoSelecionado}
                    exibirBotaoAdicionarProduto={exibirBotaoAdicionarProduto}
                    lancarProdutoDesabilitado={lancarProdutoDesabilitado}
                    onClose={onClose}
                    onReject={onReject}
                    escolherVariacao={escolherVariacao}
                    lancarProdutoSimples={lancarProdutoSimples}
                    lancarProdutoVariacao={lancarProdutoVariacao}
                    isLargerThan1200={isLargerThan1200}
                    isOpenDrawer={isOpenDrawer}
                    isLoading={isLoading}
                    isLargerThan900={isLargerThan900}
                    setProdutoSelecionado={setProdutoSelecionado}
                    setTela={setTela}
                    setVariacaoSelecionada={setVariacaoSelecionada}
                    variacaoSelecionada={variacaoSelecionada}
                  />

                  <ModalListarSaldoVariacoes
                    isOpen={
                      modalVisualizarEstoqueEstaAberto && !!produtoSelecionado
                    }
                    setIsOpen={setModalVisualizarEstoqueEstaAberto}
                    idProduto={produtoSelecionado?.id || ''}
                  />
                  <DrawerFiltrosAvancados
                    isOpen={isOpenDrawer}
                    onClose={onCloseDrawer}
                    filtersSubmit={async (data) => {
                      submitFiltros(data);
                    }}
                    key="drawerFiltrosConsultaProdutosPdv"
                  />
                </ModalContent>
              );
            }}
          </ConsultaProdutoPdvContext.Consumer>
        </ConsultaProdutosPdvProvider>
      </ModalPadraoChakra>
    );
  }
);
