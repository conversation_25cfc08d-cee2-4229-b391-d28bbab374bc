﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Refit;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Zendar.Business.API.ZendarSync.Clients;
using Zendar.Business.API.ZendarSync.Dtos.Export.Request;
using Zendar.Business.API.ZendarSync.Dtos.Import.Request;
using Zendar.Business.API.ZendarSync.Dtos.Link.Request;
using Zendar.Business.API.ZendarSync.Dtos.Order.Request;
using Zendar.Business.API.ZendarSync.Dtos.Product.Request;
using Zendar.Business.API.ZendarSync.Dtos.Trigger.Request;
using Zendar.Business.API.ZendarSync.Dtos.Update.Request;
using Zendar.Business.API.ZendarSync.Handlers;
using Zendar.Business.API.ZendarSync.Services;
using Zendar.Business.Consts;
using Zendar.Business.Helpers;
using Zendar.Business.Helpers.Refit;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Interfaces.Services.OperacaoServices;
using Zendar.Business.Services.IntegracaoServices.IntegracaoOrigemPedidoService;
using Zendar.Business.Services.IntegracaoServices.IntegracaoPedidoService.Cadastrar;
using Zendar.Business.Services.IntegracaoServices.IntegracaoPendenciaPedidoService;
using Zendar.Business.Services.IntegracaoServices.IntegracaoService;
using Zendar.Business.Services.IntegracaoServices.IntegracaoSituacaoPedidoService;
using Zendar.Business.Services.IntegracaoServices.ProdutoPlataformaService;
using Zendar.Business.Services.V1.IntegracaoServices.SincronizacaoServices.Sincronizacao;
using Zendar.Business.Services.V1.IntegracaoServices.Tray.Interfaces;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoEcommerceService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoVariacaoFacade;
using Zendar.Business.Services.V2.TabelaPrecoV2Services.TabelaPrecoProdutoCorTamanhoV2Service;
using Zendar.Business.Services.V2.TabelaPrecoV2Services.TabelaPrecoProdutoV2Service;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Integracao;
using Zendar.Business.ViewModels.Integracao.IntegracaoPedido;
using Zendar.Business.ViewModels.Integracao.IntegracaoSituacaoPedido;
using Zendar.Business.ViewModels.Integracao.IntegracaoSnapshot;
using Zendar.Business.ViewModels.Integracao.Tray;
using Zendar.Business.ViewModels.Operacao;
using Zendar.Business.ViewModels.V1.Integracao;
using Zendar.Business.ViewModels.V1.Integracao.Tray;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;
using Zendar.Business.ViewModels.V2.TabelaPrecoViewModels;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoOrigemPedidoRepository;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoPedidoRepository;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoPendenciaPedidoRepository;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoRepository;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoSituacaoPedidoRepository;
using Zendar.Data.Repository.Aplicacao.PromocaoRepository;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;
using Zendar.Integracao.ViewModel;
using Zendar.QueueService.Interfaces;
using ZendarPackage.NotaFiscal.Enums;

namespace Zendar.Business.Services.V1.IntegracaoServices.Tray
{
    public class IntegracaoTrayService : BaseService, IIntegracaoTrayService
    {
        private readonly IConfiguration _configuration;
        private readonly INotificador _notificador;
        private readonly IMapper _mapper;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly INotificationHubService _notificationHubService;
        private readonly ICacheService _cacheService;

        private readonly IIntegracaoRepository _integracaoRepository;
        private readonly IIntegracaoPedidoRepository _integracaoPedidoRepository;
        private readonly IIntegracaoSituacaoPedidoRepository _integracaoSituacaoPedidoRepository;
        private readonly IIntegracaoOrigemPedidoRepository _integracaoOrigemPedidoRepository;
        private readonly IIntegracaoPendenciaPedidoRepository _integracaoPendenciaPedidoRepository;
        private readonly IDocumentoFiscalRepository _documentoFiscalRepository;

        private readonly ITabelaPrecoRepository _tabelaPrecoRepository;
        private readonly ITabelaPrecoLojaRepository _tabelaPrecoLojaRepository;
        private readonly ITabelaPrecoProdutoRepository _tabelaPrecoProdutoRepository;
        private readonly ITabelaPrecoProdutoCorTamanhoRepository _tabelaPrecoProdutoCorTamanhoRepository;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IPromocaoRepository _promocaoRepository;
        private readonly IProdutoRepository _produtoRepository;
        private readonly IProdutoCorTamanhoEstoqueRepository _produtoCorTamanhoEstoqueRepository;

        private readonly IIntegracaoService _integracaoService;
        private readonly IIntegracaoPedidoService _integracaoPedidoService;
        private readonly IIntegracaoSituacaoPedidoService _integracaoSituacaoPedidoService;
        private readonly IIntegracaoOrigemPedidoService _integracaoOrigemPedidoService;
        private readonly IIntegracaoPendenciaPedidoService _integracaoPendenciaPedidoService;

        private readonly IProdutoV2Service _produtoV2Service;
        private readonly IProdutoEcommerceV2Service _produtoEcommerceV2Service;
        private readonly IEnvioEmailService _envioEmailService;
        private readonly ILocalEstoqueService _localEstoqueService;
        private readonly ITabelaPrecoService _tabelaPrecoService;
        private readonly ITabelaPrecoProdutoV2Service _tabelaPrecoProdutoV2Service;
        private readonly ITabelaPrecoProdutoCorTamanhoV2Service _tabelaPrecoProdutoCorTamanhoV2Service;
        private readonly IPedidoOrcamentoVendaService _pedidoOrcamentoVendaService;
        private readonly IProdutoPlataformaService _produtoPlataformaService;
        private readonly ILogErroService _logErroService;
        private readonly IProdutoVariacaoV2Facade _produtoVariacaoV2Facade;
        private readonly IZendarSyncEcommerceService<ITrayApi> _zendarSyncTrayApi;
        private readonly IServiceBusEnqueueMessage _serviceBusEnqueueMessage;
        private readonly ISincronizacaoIntegracaoService _sincronizacaoIntegracaoService;
		private string _trayUrl { get; set; }

		public IntegracaoTrayService(
			IConfiguration configuration,
			IMapper mapper,
			INotificador notificador,
			IAspNetUserInfo aspNetUserInfo,
			INotificationHubService notificationHubService,
			ICacheService cacheService,

			IIntegracaoRepository integracaoRepository,
			IIntegracaoPedidoRepository integracaoPedidoRepository,
			IIntegracaoSituacaoPedidoRepository integracaoSituacaoPedidoRepository,
			IIntegracaoOrigemPedidoRepository integracaoOrigemPedidoRepository,
			IIntegracaoPendenciaPedidoRepository integracaoPendenciaPedidoRepository,
			IDocumentoFiscalRepository documentoFiscalRepository,

			ITabelaPrecoRepository tabelaPrecoRepository,
			ITabelaPrecoLojaRepository tabelaPrecoLojaRepository,
			ITabelaPrecoProdutoRepository tabelaPrecoProdutoRepository,
			ITabelaPrecoProdutoCorTamanhoRepository tabelaPrecoProdutoCorTamanhoRepository,
			IUsuarioRepository usuarioRepository,
			IPromocaoRepository promocaoRepository,
			IProdutoRepository produtoRepository,
			IProdutoCorTamanhoEstoqueRepository produtoCorTamanhoEstoqueRepository,

			IIntegracaoService integracaoService,
			IIntegracaoPedidoService integracaoPedidoService,
			IIntegracaoSituacaoPedidoService integracaoSituacaoPedidoService,
			IIntegracaoOrigemPedidoService integracaoOrigemPedidoService,
			IIntegracaoPendenciaPedidoService integracaoPendenciaPedidoService,

			IProdutoV2Service produtoV2Service,
			IProdutoEcommerceV2Service produtoEcommerceV2Service,
			IEnvioEmailService envioEmailService,
			ILocalEstoqueService localEstoqueService,
			ITabelaPrecoService tabelaPrecoService,
			ITabelaPrecoProdutoV2Service tabelaPrecoProdutoV2Service,
			ITabelaPrecoProdutoCorTamanhoV2Service tabelaPrecoProdutoCorTamanhoV2Service,
			IPedidoOrcamentoVendaService pedidoOrcamentoVendaService,
			IProdutoPlataformaService produtoPlataformaService,
			ILogErroService logErroService,
			IProdutoVariacaoV2Facade produtoVariacaoV2Facade,
			SyncJwtHandler syncJwtHandler,
			IServiceBusEnqueueMessage serviceBusEnqueueMessage,
			ISincronizacaoIntegracaoService sincronizacaoIntegracaoService)
			: base(notificador)
		{
			_configuration = configuration;
			_notificador = notificador;
			_mapper = mapper;
			_aspNetUserInfo = aspNetUserInfo;
			_notificationHubService = notificationHubService;
			_cacheService = cacheService;

			_integracaoRepository = integracaoRepository;
			_integracaoPedidoRepository = integracaoPedidoRepository;
			_integracaoSituacaoPedidoRepository = integracaoSituacaoPedidoRepository;
			_integracaoOrigemPedidoRepository = integracaoOrigemPedidoRepository;
			_integracaoPendenciaPedidoRepository = integracaoPendenciaPedidoRepository;
			_documentoFiscalRepository = documentoFiscalRepository;

			_tabelaPrecoRepository = tabelaPrecoRepository;
			_tabelaPrecoLojaRepository = tabelaPrecoLojaRepository;
			_tabelaPrecoProdutoRepository = tabelaPrecoProdutoRepository;
			_tabelaPrecoProdutoCorTamanhoRepository = tabelaPrecoProdutoCorTamanhoRepository;
			_integracaoPendenciaPedidoRepository = integracaoPendenciaPedidoRepository;
			_integracaoRepository = integracaoRepository;
			_integracaoPedidoRepository = integracaoPedidoRepository;
			_integracaoSituacaoPedidoRepository = integracaoSituacaoPedidoRepository;
			_integracaoOrigemPedidoRepository = integracaoOrigemPedidoRepository;
			_usuarioRepository = usuarioRepository;
			_promocaoRepository = promocaoRepository;
			_produtoRepository = produtoRepository;
			_produtoCorTamanhoEstoqueRepository = produtoCorTamanhoEstoqueRepository;

			_integracaoService = integracaoService;
			_integracaoPedidoService = integracaoPedidoService;
			_integracaoSituacaoPedidoService = integracaoSituacaoPedidoService;
			_integracaoOrigemPedidoService = integracaoOrigemPedidoService;
			_integracaoPendenciaPedidoService = integracaoPendenciaPedidoService;

			_produtoV2Service = produtoV2Service;
			_produtoEcommerceV2Service = produtoEcommerceV2Service;
			_envioEmailService = envioEmailService;
			_localEstoqueService = localEstoqueService;
			_tabelaPrecoService = tabelaPrecoService;
			_tabelaPrecoProdutoV2Service = tabelaPrecoProdutoV2Service;
			_tabelaPrecoProdutoCorTamanhoV2Service = tabelaPrecoProdutoCorTamanhoV2Service;
			_pedidoOrcamentoVendaService = pedidoOrcamentoVendaService;
			_produtoPlataformaService = produtoPlataformaService;
			_logErroService = logErroService;
			_produtoVariacaoV2Facade = produtoVariacaoV2Facade;

			_trayUrl = _configuration.GetValue<string>("ZendarSyncApi:TrayUrl");

			if (string.IsNullOrEmpty(_trayUrl))
				_trayUrl = Environment.GetEnvironmentVariable(SystemConst.ZENDAR_SYNC_TRAY_URL);

			var httpClient = new HttpClient(syncJwtHandler) { BaseAddress = new Uri(_trayUrl) };

			_zendarSyncTrayApi = RestService.For<IZendarSyncEcommerceService<ITrayApi>>(httpClient);

			_serviceBusEnqueueMessage = serviceBusEnqueueMessage;
			_sincronizacaoIntegracaoService = sincronizacaoIntegracaoService;
		}

		#region Etapa

		public async Task<IdentificacaoEtapasTray> ObterIdentificacaoEtapa()
        {
            try
            {
                var integracao = await Obter();

                if (integracao == null)
                    return IdentificacaoEtapasTray.GUIA_INTEGRACAO;

                return JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes).IdentificacaoEtapas;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return IdentificacaoEtapasTray.GUIA_INTEGRACAO;
            }
        }

        public async Task CadastrarEtapa(
            IntegracaoViewModel integracaoViewModel)
        {
            try
            {
                integracaoViewModel.UsuarioId = Guid.Parse(_aspNetUserInfo.Id);

                var localEstoqueId = await _localEstoqueService.ObterPadraoSistema(_aspNetUserInfo.LojaId.Value);

                if (localEstoqueId != null)
                    integracaoViewModel.LocalEstoqueId = localEstoqueId.Value;

                integracaoViewModel.TabelaPrecoId = await _tabelaPrecoService.ObterTabelaPrecoPadrao();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var integracaoId =
                    await _integracaoService.Cadastrar(integracaoViewModel,
                                                       JsonConvert.SerializeObject(new ConfiguracaoIntegracaoViewModel
                                                       {
                                                           IdentificacaoEtapas = IdentificacaoEtapasTray.GUIA_INTEGRACAO
                                                       }));

                if (integracaoId != Guid.Empty)
                    await _integracaoSituacaoPedidoService.VincularSituacaoIntegracaoPadrao(integracaoId, ObterListaSituacaoPadrao());
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task AlterarEtapa(
            IntegracaoViewModel integracaoViewModel)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);

                await _integracaoService.Alterar(integracaoViewModel, JsonConvert.SerializeObject(configuracoes));
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task AlterarIdentificacaoEtapa(
            IdentificacaoEtapasTray identificacaoEtapasTray)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);
                configuracoes.IdentificacaoEtapas = identificacaoEtapasTray;

                var integracaoViewModel = _mapper.Map<IntegracaoViewModel>(integracao);

                await _integracaoService.Alterar(integracaoViewModel, JsonConvert.SerializeObject(configuracoes));
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task NotificarImportacaoEtapa(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);

                if (notificacaoViewModel.Tipo == (int)Tipo.LISTA_PRODUTO_SITE_MANUAL)
                    configuracoes.IdentificacaoEtapas = IdentificacaoEtapasTray.LISTA_PRODUTO_SITE;
                else if (notificacaoViewModel.Tipo == (int)Tipo.LISTA_PRODUTO_SITE_AUTOMATICO)
                {
                    configuracoes.IdentificacaoEtapas = IdentificacaoEtapasTray.FINALIZADO;
                    integracao.SincronizacaoHabilitada = true;
                }

                await _integracaoService.Alterar(new IntegracaoViewModel
                {
                    Id = integracao.Id,
                    SincronizacaoHabilitada = integracao.SincronizacaoHabilitada,
                    LocalEstoqueId = integracao.LocalEstoqueId,
                    Ativo = integracao.Ativo
                },
                JsonConvert.SerializeObject(configuracoes));

                await SetZendarTriggerCache();

                var usuario = await _usuarioRepository.BuscarPorId(notificacaoViewModel.UsuarioId);

                if (usuario != null)
                {
                    var listaEmail = new List<string>();
                    listaEmail.Add(usuario.Email);

                    await _envioEmailService.EnviarEmailZendarSync("Importação de produtos da Tray foi concluída.", listaEmail);

                    await _notificationHubService.Notificar(new Guid(_aspNetUserInfo.LojaId?.ToString()), notificacaoViewModel.UsuarioId, "tray", "importacao-produto");
                }
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task NotificarExportacaoEtapa(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);

                if (notificacaoViewModel.Tipo == (int)Tipo.LISTA_PRODUTO_ZENDAR)
                {
                    configuracoes.IdentificacaoEtapas = IdentificacaoEtapasTray.FINALIZADO;
                    integracao.SincronizacaoHabilitada = true;
                }

                await _integracaoService.Alterar(new IntegracaoViewModel
                {
                    Id = integracao.Id,
                    SincronizacaoHabilitada = integracao.SincronizacaoHabilitada,
                    LocalEstoqueId = integracao.LocalEstoqueId,
                    Ativo = integracao.Ativo
                },
                JsonConvert.SerializeObject(configuracoes));

                await SetZendarTriggerCache();

                var usuario = await _usuarioRepository.BuscarPorId(notificacaoViewModel.UsuarioId);

                if (usuario != null)
                {
                    var listaEmail = new List<string>();
                    listaEmail.Add(usuario.Email);

                    await _envioEmailService.EnviarEmailZendarSync("Exportação de produtos da Tray foi concluída.", listaEmail);

                    await _notificationHubService.Notificar(new Guid(_aspNetUserInfo.LojaId?.ToString()), notificacaoViewModel.UsuarioId, "tray", "exportacao-produto");
                }
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task NotificarExportacaoLimite(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);

                if (notificacaoViewModel.Tipo == (int)Tipo.LISTA_PRODUTO_ZENDAR)
                {
                    configuracoes.IdentificacaoEtapas = IdentificacaoEtapasTray.FINALIZADO;
                    integracao.SincronizacaoHabilitada = true;
                }

                await _integracaoService.Alterar(new IntegracaoViewModel
                {
                    Id = integracao.Id,
                    SincronizacaoHabilitada = integracao.SincronizacaoHabilitada,
                    LocalEstoqueId = integracao.LocalEstoqueId,
                    Ativo = integracao.Ativo
                },
                JsonConvert.SerializeObject(configuracoes));

                await SetZendarTriggerCache();

                var usuario = await _usuarioRepository.BuscarPorId(notificacaoViewModel.UsuarioId);

                if (usuario != null)
                {
                    var listaEmail = new List<string>();
                    listaEmail.Add(usuario.Email);

                    await _envioEmailService.EnviarEmailZendarSync("Exportação de produtos da Tray foi concluída com limite excedido do plano.", listaEmail);

                    await _notificationHubService.Notificar(new Guid(_aspNetUserInfo.LojaId?.ToString()), notificacaoViewModel.UsuarioId, "tray", "exportacao-produto-limite");
                }
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task NotificarAtualizacaoTabelaPreco(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);
                configuracoes.AtualizacaoTabelaPreco = false;

                var integracaoViewModel = _mapper.Map<IntegracaoViewModel>(integracao);

                await _integracaoService.Alterar(integracaoViewModel, JsonConvert.SerializeObject(configuracoes));

                await SetZendarTriggerCache();

                var usuario = await _usuarioRepository.BuscarPorId(notificacaoViewModel.UsuarioId);

                if (usuario != null)
                {
                    var listaEmail = new List<string>();
                    listaEmail.Add(usuario.Email);

                    await _envioEmailService.EnviarEmailZendarSync("Atualização da tabela de preços da Tray foi concluída.", listaEmail);

                    await _notificationHubService.Notificar(new Guid(_aspNetUserInfo.LojaId?.ToString()), notificacaoViewModel.UsuarioId, "tray", "atualizacao-tabela-preco");
                }
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task NotificarAtualizacaoPromocao(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);
                configuracoes.AtualizacaoPromocao = false;

                var integracaoViewModel = _mapper.Map<IntegracaoViewModel>(integracao);

                await _integracaoService.Alterar(integracaoViewModel, JsonConvert.SerializeObject(configuracoes));

                await SetZendarTriggerCache();

                var usuario = await _usuarioRepository.BuscarPorId(notificacaoViewModel.UsuarioId);

                if (usuario != null)
                {
                    var listaEmail = new List<string>();
                    listaEmail.Add(usuario.Email);

                    await _envioEmailService.EnviarEmailZendarSync("Atualização da promoção da Tray foi concluída.", listaEmail);

                    await _notificationHubService.Notificar(new Guid(_aspNetUserInfo.LojaId?.ToString()), notificacaoViewModel.UsuarioId, "tray", "atualizacao-promocao");
                }
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task NotificarPedido(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                await _notificationHubService.NotificarComDataEMensagem(new Guid(_aspNetUserInfo.LojaId?.ToString()),
                                                                   notificacaoViewModel.Id,
                                                                   notificacaoViewModel.DataNotificacao,
                                                                   "tray",
                                                                   "pedido",
                                                                   notificacaoViewModel.Mensagem);
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        #endregion

        #region Vincular

        public async Task VincularMarca(
            VincularCadastroRequest vincularCadastroRequest)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                await _zendarSyncTrayApi.VincularMarca(vincularCadastroRequest);
            }
            catch
            {
                NotificarErro(ResourceMensagem.IntegracaoTrayService_FalhaVincularMarca);

                return;
            }
        }

        public async Task VincularCategoria(
            VincularCadastroRequest vincularCadastroRequest)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                await _zendarSyncTrayApi.VincularCategoria(vincularCadastroRequest);
            }
            catch
            {
                NotificarErro(ResourceMensagem.IntegracaoTrayService_FalhaVincularCategoria);

                return;
            }
        }

        public async Task VincularCaracteristica(
            VincularCadastroRequest vincularCadastroRequest)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                await _zendarSyncTrayApi.VincularCaracteristica(vincularCadastroRequest);
            }
            catch
            {
                NotificarErro(ResourceMensagem.IntegracaoTrayService_FalhaVincularCaracteristica);

                return;
            }
        }

        public async Task VincularVariacao(
            VincularCadastroRequest vincularCadastroRequest)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                await _zendarSyncTrayApi.VincularVariacao(vincularCadastroRequest);
            }
            catch
            {
                NotificarErro(ResourceMensagem.IntegracaoTrayService_FalhaVincularVariacao);

                return;
            }
        }

		public async Task VincularProduto(
            VincularCadastroRequest vincularCadastroRequest)
        {
            string mensagem = "Falha ao vincular um produto.";

            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                try
                {
                    await _zendarSyncTrayApi.CadastrarProduto(vincularCadastroRequest.SiteId);
                }
                catch
                {
                    NotificarErro(mensagem);

                    return;
                }

                await _zendarSyncTrayApi.VincularProduto(vincularCadastroRequest);
            }
            catch
            {
                NotificarErro(ResourceMensagem.IntegracaoTrayService_FalhaVincularProduto);

                return;
            }
        }

        public async Task VincularCliente(
			VincularCadastroRequest vincularCadastroRequest)
        {
			try
			{
				var integracao = await Obter();

				if (PossuiAvisos() || PossuiErros())
					return;

				await _zendarSyncTrayApi.VincularCliente(vincularCadastroRequest);
			}
			catch
			{
				NotificarErro(ResourceMensagem.IntegracaoTrayService_FalhaVincularCliente);

				return;
			}
		}

		public async Task VincularFormaPagamento(
            VincularCadastroRequest vincularCadastroRequest)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                await _zendarSyncTrayApi.VincularFormaPagamento(vincularCadastroRequest);
            }
            catch
            {
                NotificarErro(ResourceMensagem.IntegracaoTrayService_FalhaVincularFormaPagamento);

                return;
            }
        }

        #endregion

        #region Ação

        public async Task AtivarInativarIntegracao(
            Guid lojaId,
            bool ativar)
        {
            try
            {
                var integracao = await _integracaoRepository.FirstOrDefaultAsNoTracking(
                    i => i.IdentificacaoIntegracao == IdentificacaoIntegracao.TRAY && i.LojaId == lojaId);

                if (PossuiAvisos() || PossuiErros() || integracao == null)
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);

                if (configuracoes == null || configuracoes.DadosAutenticacao == null)
                    return;

                integracao.SincronizacaoHabilitada = ativar;

                try
                {
                    var vincularLojaResponse =
                        await _zendarSyncTrayApi.VincularLoja(
                            new VincularLojaRequest()
                            {
                                SiteCodigo = configuracoes.DadosAutenticacao.Code,
                                SiteUrl = configuracoes.DadosAutenticacao.Url,
                                SiteDominio = _aspNetUserInfo.HostUrl,
                                SincronizacaoHabilitada = integracao.SincronizacaoHabilitada
                            });
                }
                catch
                {

                }

                await _integracaoService.Alterar(new IntegracaoViewModel
                {
                    Id = integracao.Id,
                    SincronizacaoHabilitada = integracao.SincronizacaoHabilitada,
                    LocalEstoqueId = integracao.LocalEstoqueId,
                    Ativo = integracao.Ativo
                },
                JsonConvert.SerializeObject(configuracoes));

                await SetZendarTriggerCache(lojaId);
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new()
                {
                    Erro = ex.Message,
                    Dados = JsonConvert.SerializeObject(new
                    {
                        lojaId,
                        ativar,
                        ex.StackTrace
                    })
                });

                return;
            }
        }

        public async Task PublicarProduto(
            PublicarProdutoRequest publicarProduto)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                bool existePesoEmbalagem = true;

                var produtoV2ViewModel = await _produtoV2Service.Obter(publicarProduto.ProdutoId);

                if (produtoV2ViewModel == null)
                    return;

                var listaProdutoCorTamanhoV2ViewModel = await _produtoVariacaoV2Facade.ObterListaVariacao(produtoV2ViewModel.TipoProduto, publicarProduto.ProdutoId);

                if (listaProdutoCorTamanhoV2ViewModel != null &&
                    listaProdutoCorTamanhoV2ViewModel.Count > 0)
                {
                    foreach (var variacao in listaProdutoCorTamanhoV2ViewModel.Where(c => c.Ativo))
                    {
                        if (variacao.Caracteristicas != null &&
                            variacao.Caracteristicas.PesoEmbalagem == 0)
                        {
                            existePesoEmbalagem = false;

                            break;
                        }
                    }
                }

                if (!existePesoEmbalagem)
                {
                    NotificarAviso($"Produto não possui peso da embalagem, por favor verifique nas variações ou na guia geral.");

                    return;
                }

                var produtoEcommerceV2ViewModel =
                              await _produtoEcommerceV2Service.ObterPorProdutoIdEIdentificacaoIntegracao(publicarProduto.ProdutoId,
                                                                                                         IdentificacaoIntegracao.TRAY);

                if (produtoEcommerceV2ViewModel is null) return;

                produtoEcommerceV2ViewModel.Anunciado = publicarProduto.Publicar;

                await _produtoEcommerceV2Service.Alterar(produtoEcommerceV2ViewModel);

                if (!produtoEcommerceV2ViewModel.Anunciado)
                    await _zendarSyncTrayApi.GatilhoRemoverProduto(publicarProduto.ProdutoId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task ExcluirProduto(
            ExcluirProdutoRequest produtoExcluir)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var produtoEcommerceV2ViewModel =
                              await _produtoEcommerceV2Service.ObterPorProdutoIdEIdentificacaoIntegracao(produtoExcluir.ProdutoId,
                                                                                                         IdentificacaoIntegracao.TRAY);

                produtoEcommerceV2ViewModel.Anunciado = false;

                await _produtoEcommerceV2Service.Alterar(produtoEcommerceV2ViewModel);

                await _zendarSyncTrayApi.GatilhoRemoverProduto(produtoExcluir.ProdutoId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task AlterarRastreioPedido(
            AlterarRastreioPedidoRequest alterarRastreioPedidoRequest)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var integracaoPedido = await _integracaoPedidoService.ObterPorId(alterarRastreioPedidoRequest.IntegracaoPedidoId);

                var integracaoPedidoViewModel = new IntegracaoPedidoViewModel
                {
                    Id = integracaoPedido.Id,
                    Cliente = integracaoPedido.Cliente,
                    ValorTotal = integracaoPedido.ValorTotal,
                    Observacao = integracaoPedido.Observacao,
                    CodigoRastreioEnviado = true,
                    Json = integracaoPedido.Json,
                    JsonOperacaoIntegracao = integracaoPedido.JsonOperacaoIntegracao,
                    IntegracaoSituacaoPedidoId = integracaoPedido.IntegracaoSituacaoPedidoId
                };

                await _integracaoPedidoService.Alterar(integracaoPedidoViewModel);

                var gatilhoPedidoRequest = new GatilhoPedidoRequest();
                gatilhoPedidoRequest.ZendarId = alterarRastreioPedidoRequest.IntegracaoPedidoId;
                gatilhoPedidoRequest.CodigoEnvio = alterarRastreioPedidoRequest.CodigoEnvio;
                gatilhoPedidoRequest.DataEnvio = alterarRastreioPedidoRequest.DataEnvio;
                gatilhoPedidoRequest.Status = "Enviado";

                await _zendarSyncTrayApi.GatilhoRastreioPedido(gatilhoPedidoRequest);
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task FinalizarPedido(
            FinalizarPedidoRequest finalizarPedidoRequest)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var gatilhoPedidoRequest = new GatilhoPedidoRequest();
                gatilhoPedidoRequest.ZendarId = finalizarPedidoRequest.IntegracaoPedidoId;
                gatilhoPedidoRequest.Status = "Finalizado";

                await _zendarSyncTrayApi.GatilhoStatusPedido(gatilhoPedidoRequest);
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task AutenticarDados(
            DadosAutenticacaoViewModel dadosAutenticacaoViewModel)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var vincularLojaResponse =
                    await _zendarSyncTrayApi.VincularLoja(
                        new VincularLojaRequest()
                        {
                            SiteCodigo = dadosAutenticacaoViewModel.Code,
                            SiteUrl = dadosAutenticacaoViewModel.Url,
                            SiteDominio = _aspNetUserInfo.HostUrl,
                            SincronizacaoHabilitada = true
                        });

                dadosAutenticacaoViewModel.SiteLojaId = vincularLojaResponse.SiteLojaId;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);
                configuracoes.DadosAutenticacao = dadosAutenticacaoViewModel;

                integracao.SincronizacaoHabilitada = dadosAutenticacaoViewModel.SincronizacaoHabilitada;

                await _integracaoService.Alterar(new IntegracaoViewModel
                {
                    Id = integracao.Id,
                    SincronizacaoHabilitada = integracao.SincronizacaoHabilitada,
                    LocalEstoqueId = integracao.LocalEstoqueId,
                    Ativo = integracao.Ativo
                },
                JsonConvert.SerializeObject(configuracoes));

                await SetZendarTriggerCache();
            }
            catch (ApiException ex)
            {
                var refitError = JsonConvert.DeserializeObject<RefitError>(ex.Content);

                var error = refitError?.errors?.Error?.FirstOrDefault();

                NotificarAviso(error);

                await _logErroService.Inserir(new LogErroInserirViewModel
                {
					Dados = JsonConvert.SerializeObject(new
					{
						dadosAutenticacaoViewModel,
						ex.StackTrace
					}),
                    Erro = error,
				}, false);

                return;
            }
        }

        public async Task AlterarCanalVenda(
            DadosCanalVendaViewModel dadosCanalVendaViewModel)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);
                configuracoes.DadosCanalVenda = dadosCanalVendaViewModel;

                await _integracaoService.Alterar(new IntegracaoViewModel
                {
                    Id = integracao.Id,
                    SincronizacaoHabilitada = integracao.SincronizacaoHabilitada,
                    LocalEstoqueId = integracao.LocalEstoqueId,
                    Ativo = integracao.Ativo
                },
                JsonConvert.SerializeObject(configuracoes));
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task AlterarVendedor(
            Guid vendedorId)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                await _integracaoService.AlterarVendedor(integracao.Id, vendedorId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task AlterarComissaoVenda(
            DadosComissaoVendaViewModel dadosComissaoVendaViewModel)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);
                configuracoes.DadosComissaoVenda = dadosComissaoVendaViewModel;

                await _integracaoService.Alterar(new IntegracaoViewModel
                {
                    Id = integracao.Id,
                    SincronizacaoHabilitada = integracao.SincronizacaoHabilitada,
                    LocalEstoqueId = integracao.LocalEstoqueId,
                    Ativo = integracao.Ativo
                },
                JsonConvert.SerializeObject(configuracoes));
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task AlterarLocalEstoque(
            Guid localEstoqueId)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                await _integracaoService.AlterarLocalEstoque(integracao.Id, localEstoqueId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task CopiarTabelaPreco(
            Guid tabelaPrecoId)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                #region TabelaPreco

                var tabelaPrecoCopiar = await _tabelaPrecoRepository.FindByKey(tabelaPrecoId);

                var tabelaPrecoCadastrarViewModel = new TabelaPrecoCadastrarViewModel();
                tabelaPrecoCadastrarViewModel.Nome = IdentificacaoIntegracao.TRAY.ObterDescricao();
                tabelaPrecoCadastrarViewModel.TipoCalculo = tabelaPrecoCopiar.TipoCalculo;
                tabelaPrecoCadastrarViewModel.Percentual = tabelaPrecoCopiar.Percentual;
                tabelaPrecoCadastrarViewModel.PadronizarPreco = tabelaPrecoCopiar.PadronizarPreco;
                tabelaPrecoCadastrarViewModel.ArredondamentoAbaixo = tabelaPrecoCopiar.ArredondamentoAbaixo;
                tabelaPrecoCadastrarViewModel.ArredondamentoAcima = tabelaPrecoCopiar.ArredondamentoAcima;
                tabelaPrecoCadastrarViewModel.Ativo = tabelaPrecoCopiar.Ativo;

                #region TabelaPrecoLoja

                var listaTabelaPrecoLojaCopiar = await _tabelaPrecoLojaRepository.ListarPorIdTabelaPreco(tabelaPrecoId);

                var listaLojaId = new List<Guid>();

                foreach (var item in listaTabelaPrecoLojaCopiar)
                    listaLojaId.Add(item.LojaId);

                tabelaPrecoCadastrarViewModel.LojasSelecionadas = listaLojaId?.ToArray();

                #endregion

                var tabelaPrecoIdNova = await _tabelaPrecoService.Cadastrar(tabelaPrecoCadastrarViewModel);

                if (tabelaPrecoIdNova == null ||
                    tabelaPrecoIdNova == Guid.Empty)
                    return;

                #endregion

                #region TabelaPrecoProduto

                var listaTabelaPrecoProduto =
                    _tabelaPrecoProdutoRepository.Where(c => c.TabelaPrecoId == tabelaPrecoId)
                                                 .ToList();

                foreach (var tabelaPrecoProduto in listaTabelaPrecoProduto)
                {
                    var tabelaPrecoProdutoV2ViewModel = new TabelaPrecoProdutoV2ViewModel();
                    tabelaPrecoProdutoV2ViewModel.TabelaPrecoId = tabelaPrecoIdNova.Value;
                    tabelaPrecoProdutoV2ViewModel.ProdutoId = tabelaPrecoProduto.ProdutoId;
                    tabelaPrecoProdutoV2ViewModel.PrecoVenda = new PrecoVendaV2ViewModel() { PrecoVenda = tabelaPrecoProduto.PrecoVenda };

                    await _tabelaPrecoProdutoV2Service.Cadastrar(tabelaPrecoProdutoV2ViewModel, false);
                }

                #endregion

                #region TabelaProdutoCorTamanho

                var listaTabelaPrecoProdutoCorTamanho =
                    _tabelaPrecoProdutoCorTamanhoRepository.Where(c => c.TabelaPrecoId == tabelaPrecoId)
                                                           .ToList();

                foreach (var tabelaPrecoProdutoCorTamanho in listaTabelaPrecoProdutoCorTamanho)
                {
                    var tabelaPrecoProdutoCorTamanhoV2ViewModel = new TabelaPrecoProdutoCorTamanhoViewModel();
                    tabelaPrecoProdutoCorTamanhoV2ViewModel.TabelaPrecoId = tabelaPrecoIdNova.Value;
                    tabelaPrecoProdutoCorTamanhoV2ViewModel.ProdutoCorTamanhoId = tabelaPrecoProdutoCorTamanho.ProdutoCorTamanhoId;
                    tabelaPrecoProdutoCorTamanhoV2ViewModel.PrecoVenda = tabelaPrecoProdutoCorTamanho.PrecoVenda;
                    tabelaPrecoProdutoCorTamanhoV2ViewModel.DataHoraUltimaAlteracao = tabelaPrecoProdutoCorTamanho.DataHoraUltimaAlteracao;

                    await _tabelaPrecoProdutoCorTamanhoV2Service.Cadastrar(tabelaPrecoProdutoCorTamanhoV2ViewModel, false);
                }

                #endregion

                await _integracaoService.AlterarTabelaPreco(integracao.Id, tabelaPrecoIdNova.Value);

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);
                configuracoes.PossuiTabelaPrecoCriada = true;

                await _integracaoService.Alterar(new IntegracaoViewModel
                {
                    Id = integracao.Id,
                    SincronizacaoHabilitada = integracao.SincronizacaoHabilitada,
                    LocalEstoqueId = integracao.LocalEstoqueId,
                    Ativo = integracao.Ativo
                },
                JsonConvert.SerializeObject(configuracoes));
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task AlterarTabelaPreco(
            Guid tabelaPrecoId)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                await _integracaoService.AlterarTabelaPreco(integracao.Id, tabelaPrecoId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task AlterarPromocao(
            Guid promocaoId)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                await _integracaoService.AlterarPromocao(integracao.Id, promocaoId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }
        public async Task AlterarTipoCadastro(
            DadosTipoCadastroViewModel dadosTipoCadastroViewModel)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);
                configuracoes.DadosTipoCadastro = dadosTipoCadastroViewModel;

                await _integracaoService.Alterar(new IntegracaoViewModel
                {
                    Id = integracao.Id,
                    SincronizacaoHabilitada = integracao.SincronizacaoHabilitada,
                    LocalEstoqueId = integracao.LocalEstoqueId,
                    Ativo = integracao.Ativo
                },
                JsonConvert.SerializeObject(configuracoes));
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task AlterarBuscarProduto(
            DadosBuscarProdutoViewModel dadosBuscarProdutoViewModel)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);
                configuracoes.DadosBuscarProduto = dadosBuscarProdutoViewModel;

                await _integracaoService.Alterar(new IntegracaoViewModel
                {
                    Id = integracao.Id,
                    SincronizacaoHabilitada = integracao.SincronizacaoHabilitada,
                    LocalEstoqueId = integracao.LocalEstoqueId,
                    Ativo = integracao.Ativo
                },
                JsonConvert.SerializeObject(configuracoes));
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return;
            }
        }

        public async Task ExportarCadastroProduto(
            ExportarCadastroProdutoRequest exportarCadastroProdutoRequest)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                exportarCadastroProdutoRequest.HTTP_REFERER_MULTIEMPRESA = _aspNetUserInfo?.HostUrl;
                exportarCadastroProdutoRequest.LojaId = _aspNetUserInfo?.LojaId?.ToString();
                exportarCadastroProdutoRequest.UsuarioId = Guid.Parse(_aspNetUserInfo?.Id?.ToString());
                exportarCadastroProdutoRequest.IdentificacaoIntegracao = (int)IdentificacaoIntegracao.TRAY;

                _serviceBusEnqueueMessage.Send(SystemConst.QUEUE_ZENDAR_EXPORT_PRODUCT,
                                               JsonConvert.SerializeObject(exportarCadastroProdutoRequest));
            }
            catch (Exception ex)
            {
                NotificarErro(ResourceMensagem.IntegracaoTrayService_FalhaExportarCadastroProduto);

                return;
            }
        }

        public async Task ImportarCadastroProduto(
            bool ignorarReferenciaEan)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);

                var importarCadastroProdutoRequest = new ImportarCadastroProdutoRequest()
                {
                    SiteStatus = (int)configuracoes.DadosBuscarProduto.SiteStatus,
                    TipoCadastro = (int)configuracoes.DadosTipoCadastro.TipoCadastro,
                    IgnorarReferenciaEan = ignorarReferenciaEan
                };

                await _zendarSyncTrayApi.ImportarCadastroProduto(importarCadastroProdutoRequest);
            }
            catch (Exception ex)
            {
                NotificarErro(ResourceMensagem.IntegracaoTrayService_FalhaImportarCadastroProduto);

                return;
            }
        }

        public async Task<Guid> ImportarProduto(
			string siteId,
			Guid lojaId,
			bool withTransaction = true)
        {
            try
            {
				var integracao = await Obter();

				if (PossuiAvisos() || PossuiErros())
					return Guid.Empty;

				await _zendarSyncTrayApi.CadastrarProduto(siteId);

				var produtoPlataforma = await _zendarSyncTrayApi.ObterProdutoSnapshotPorSiteId(siteId);

                if (produtoPlataforma is null)
                {
                    NotificarAvisoRegistroNaoEncontrado("produto da plataforma");
                    return Guid.Empty;
                }

				var produto = await _produtoPlataformaService.CadastrarAlterarProduto(produtoPlataforma, (int)IdentificacaoIntegracao.TRAY,
																		              lojaId, integracao.TabelaPrecoId, _trayUrl, withTransaction);

				if (PossuiAvisos() || PossuiErros())
					return Guid.Empty;

                return produto.Id;
			}
            catch (Exception ex)
            {
				NotificarErro(ResourceMensagem.IntegracaoTrayService_FalhaImportarCadastroProduto, ex.Message);

				return Guid.Empty;
            }
		}

		public async Task AtualizacaoTabelaPreco(
            AtualizacaoTabelaPrecoRequest atualizacaoTabelaPreco)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                integracao.TabelaPrecoId = atualizacaoTabelaPreco.TabelaPrecoId;

                atualizacaoTabelaPreco.HTTP_REFERER_MULTIEMPRESA = _aspNetUserInfo?.HostUrl;
                atualizacaoTabelaPreco.LojaId = _aspNetUserInfo?.LojaId?.ToString();
                atualizacaoTabelaPreco.UsuarioId = Guid.Parse(_aspNetUserInfo?.Id?.ToString());
                atualizacaoTabelaPreco.Token = _aspNetUserInfo?.Token;
                atualizacaoTabelaPreco.Notificar = true;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);
                configuracoes.AtualizacaoTabelaPreco = true;

                var integracaoViewModel = _mapper.Map<IntegracaoViewModel>(integracao);

                await _integracaoService.Alterar(integracaoViewModel, JsonConvert.SerializeObject(configuracoes));

                await _integracaoService.AlterarTabelaPreco(integracao.Id, atualizacaoTabelaPreco.TabelaPrecoId);

                await _zendarSyncTrayApi.AtualizacaoTabelaPreco(atualizacaoTabelaPreco);
            }
            catch (Exception ex)
            {
                NotificarErro("Falha ao exportar Tabela de preço para tray");

                return;
            }
        }

        public async Task AtualizacaoPromocao(
          AtualizacaoPromocaoRequest atualizacaoPromocao)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                if (integracao.PromocaoId != atualizacaoPromocao.PromocaoId)
                    atualizacaoPromocao.PromocaoIdExcluir = integracao.PromocaoId;

                atualizacaoPromocao.HTTP_REFERER_MULTIEMPRESA = _aspNetUserInfo?.HostUrl;
                atualizacaoPromocao.LojaId = _aspNetUserInfo?.LojaId?.ToString();
                atualizacaoPromocao.UsuarioId = Guid.Parse(_aspNetUserInfo?.Id?.ToString());
                atualizacaoPromocao.Token = _aspNetUserInfo?.Token;
                atualizacaoPromocao.Notificar = true;

                await _integracaoService.AlterarPromocao(integracao.Id, atualizacaoPromocao.PromocaoId);

                await _zendarSyncTrayApi.AtualizacaoPromocao(atualizacaoPromocao);
            }
            catch (Exception ex)
            {
                NotificarErro("Falha ao exportar Promoção para tray");

                return;
            }
        }

        public async Task<Guid?> CadastrarProduto(
            string cadastroPlataformaId)
        {
            string mensagem = "Falha ao cadastrar um produto automaticamente.";

            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                try
                {
                    await _zendarSyncTrayApi.CadastrarProduto(cadastroPlataformaId);
                }
                catch
                {
                    NotificarErro(mensagem);

                    return null;
                }

                ProdutoSnapshotViewModel produtoSnapshotViewModel = null;

                try
                {
                    produtoSnapshotViewModel = await _zendarSyncTrayApi.ObterProdutoSnapshotPorSiteId(cadastroPlataformaId);

                    if (produtoSnapshotViewModel.Marca == null)
                    {
                        NotificarAviso("Por favor preencha uma marca deste produto na Tray");

                        return null;
                    }
                }
                catch
                {
                    NotificarErro(mensagem);

                    return null;
                }

                var produtoV2ViewModel =
                      await _produtoPlataformaService.CadastrarAlterarProduto(produtoSnapshotViewModel,
                                                                              (int)IdentificacaoIntegracao.TRAY,
                                                                              _aspNetUserInfo.LojaId.Value,
                                                                              integracao.TabelaPrecoId,
                                                                              _trayUrl);

                return produtoV2ViewModel.Id;
            }
            catch (Exception ex)
            {
                NotificarErro(mensagem);

                return null;
            }
        }

        public async Task Desistir()
        {
            string mensagem = "Falha ao desistir da configuração.";

            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return;

                #region Integracao Pendencia Pedido

                var listaIntegracaoPendenciaPedido =
                    await _integracaoPendenciaPedidoService.ObterListaPorIdentificacaoIntegracao(IdentificacaoIntegracao.TRAY);

                foreach (var item in listaIntegracaoPendenciaPedido)
                    await _integracaoPendenciaPedidoRepository.Delete(item.Id);

                #endregion

                #region Integracao Pedido

                var listaIntegracaoPedido = _integracaoPedidoService.ObterListaPedidoPorIntegracaoId(integracao.Id);

                foreach (var item in listaIntegracaoPedido)
                    await _integracaoPedidoRepository.Delete(item.Id);

                #endregion

                #region Integracao Situacao Pedido

                var listaSituacaoPedido = await _integracaoSituacaoPedidoService.ListarSelect(IdentificacaoIntegracao.TRAY);

                foreach (var item in listaSituacaoPedido)
                    await _integracaoSituacaoPedidoRepository.Delete(item.Id);

                #endregion

                #region Integracao Origem Pedido

                var listaOrigemPedido = await _integracaoOrigemPedidoService.ListarSelect(IdentificacaoIntegracao.TRAY);

                foreach (var item in listaOrigemPedido)
                    await _integracaoOrigemPedidoRepository.Delete(item.Id);

                #endregion

                #region Integracao

                await _integracaoRepository.Delete(integracao.Id);

                #endregion

                await _zendarSyncTrayApi.Desistir(_aspNetUserInfo.LojaId.Value);
            }
            catch (Exception ex)
            {
                NotificarErro(mensagem);

                return;
            }
        }

        #endregion

        #region Obter

        public async Task<Guid?> ObterId()
        {
            try
            {
                return await _integracaoService.ObterId(IdentificacaoIntegracao.TRAY, _aspNetUserInfo.LojaId.Value);
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<IntegracaoObterViewModel> Obter(Guid? lojaId = null)
        {
            try
            {
                return await _integracaoService.Obter(IdentificacaoIntegracao.TRAY, lojaId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<IntegracaoTotalizadorViewModel> ObterTotalizador(
            int mes)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var integracaoTotalizadorViewModel = await _integracaoPedidoService.ObterTotalizador(integracao.Id, mes);

                return integracaoTotalizadorViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<int> ObterQuantidadeNotaFiscalAlerta()
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return 0;

                var listaPedido = _integracaoPedidoService.ObterListaPedidoPorIntegracaoId(integracao.Id);

                var quantidadeRetornoIndisponivel = 0;
                var quantidadeRejeitada = 0;

                foreach (var integracaoPedido in listaPedido)
                {
                    if (integracaoPedido.OperacaoId != null)
                    {
                        var documentoFiscal = await _documentoFiscalRepository.ObterStatusEMensagemPorOperacaoId(integracaoPedido.OperacaoId.Value);

                        if (documentoFiscal != null)
                        {
                            switch (documentoFiscal.Status)
                            {
                                case StatusFiscal.RETORNO_INDISPONIVEL:
                                    quantidadeRetornoIndisponivel++;
                                    break;
                                case StatusFiscal.REJEITADA:
                                    quantidadeRejeitada++;
                                    break;
                            }
                        }
                    }
                }

                var quantidadeNotaFiscalAlerta = quantidadeRetornoIndisponivel + quantidadeRejeitada;

                return quantidadeNotaFiscalAlerta;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return 0;
            }
        }

        public async Task<int> ObterQuantidadePendencia()
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return 0;

                var quantidadePendenciaPedido = await _integracaoPendenciaPedidoService.ObterQuantidadePendenciaPedido(integracao.Id);

                return quantidadePendenciaPedido;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return 0;
            }
        }

        public async Task<DadosAutenticacaoViewModel> ObterAutenticacao()
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                return JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes).DadosAutenticacao;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<DadosCanalVendaViewModel> ObterCanalVenda()
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                return JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes).DadosCanalVenda;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<Guid?> ObterVendedor()
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                return integracao.VendedorId;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<DadosComissaoVendaViewModel> ObterComissaoVenda()
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                return JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes).DadosComissaoVenda;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<Guid?> ObterLocalEstoque()
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                return integracao.LocalEstoqueId;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<Guid?> ObterTabelaPreco()
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                return integracao.TabelaPrecoId;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<Guid?> ObterPromocao()
        {
            try
            {
                var integracao = await Obter();

                if (integracao == null)
                {
                    _notificador.Limpar();

                    return null;
                }

                return integracao.PromocaoId;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<DadosTipoCadastroViewModel> ObterTipoCadastro()
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                return JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes).DadosTipoCadastro;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<DadosBuscarProdutoViewModel> ObterBuscarProduto()
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                return JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes).DadosBuscarProduto;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<QuantidadeProdutoViewModel> ObterQuantidadeProduto()
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);

                var dadosBuscarProduto = configuracoes.DadosBuscarProduto;

                var result = await _zendarSyncTrayApi.ObterQuantidadeProduto((int)dadosBuscarProduto.SiteStatus);

                return result;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<ListaIdNomeEnderecoViewModel>> ObterListaClienteCpfPendencia(
            Guid integracaoPedidoId)
        {
            try
            {
                var integracao = await Obter();

                if (PossuiAvisos() || PossuiErros())
                    return null;

                var listaIdNomeViewModel = await _integracaoPendenciaPedidoService.ObterListaClienteCpfPendencia(integracaoPedidoId);

                return listaIdNomeViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<GridPaginadaRetorno<ProdutoV2ViewModel>> ObterListaProdutoSitePaginado(
            GridPaginadaConsulta gridPaginada,
            ProdutoFiltrosViewModel produtoFiltrosViewModel,
            bool cadastro)
        {
            try
            {
                var listaProdutoPaginadoViewModel =
                    await _produtoV2Service.ObterListaProdutoGrid(gridPaginada,
                                                                  produtoFiltrosViewModel,
                                                                  IdentificacaoIntegracao.TRAY);

                List<EntidadeSnapshotViewModel> listaProdutoEntidadeViewModel = null;

                try
                {
                    listaProdutoEntidadeViewModel = await _zendarSyncTrayApi.ObterListaProduto();
                }
                catch
                {
                    listaProdutoEntidadeViewModel = new List<EntidadeSnapshotViewModel>();
                }

                var listaProdutoSiteViewModel =
                    await ObterListaProdutoSite(listaProdutoPaginadoViewModel, listaProdutoEntidadeViewModel, cadastro);

                GridPaginadaRetorno<ProdutoV2ViewModel> gridProdutoPaginadoViewModel = new GridPaginadaRetorno<ProdutoV2ViewModel>();

                gridProdutoPaginadoViewModel.Registros = listaProdutoSiteViewModel.Skip(gridPaginada.Skip)
                                                                                  .Take(gridPaginada.TamanhoPagina)
                                                                                  .ToList();

                gridProdutoPaginadoViewModel.Total = listaProdutoSiteViewModel.Count();


                return gridProdutoPaginadoViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<GridPaginadaRetorno<EntidadeSnapshotViewModel>> ObterListaMarcaSnapshot(
            GridPaginadaConsulta gridPaginada,
            string nome)
        {
            try
            {
                var gridPaginadaRetorno = new GridPaginadaRetorno<EntidadeSnapshotViewModel>();

                var listaMarcaSnapshot = await _zendarSyncTrayApi.ObterListaMarcaSnapshot();

                if (listaMarcaSnapshot == null)
                    return null;

                var query =
                    listaMarcaSnapshot?.Where(x => string.IsNullOrEmpty(nome) ||
                                                   TratarString(x.Nome).Contains(TratarString(nome)))
                                      ?.OrderBy(x => x.Nome)
                                      ?.Select(x => x)
                                      ?.AsQueryable();

                gridPaginadaRetorno.CarregarPaginacao(query, gridPaginada);

                return gridPaginadaRetorno;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<CategoriaSnapshotViewModel>> ObterListaCategoriaSnapshot(
            string nome)
        {
            try
            {
                var listaCategoriaSnapshot = await _zendarSyncTrayApi.ObterListaCategoriaSnapshot();

                if (listaCategoriaSnapshot == null)
                    return null;

                listaCategoriaSnapshot =
                    listaCategoriaSnapshot?.Where(x => string.IsNullOrEmpty(nome) ||
                                                       TratarString(x.Nome).Contains(TratarString(nome)))
                                          ?.OrderBy(x => x.Nome)
                                          ?.Select(x => x)
                                          ?.ToList();

                return listaCategoriaSnapshot;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<List<EntidadeSnapshotViewModel>> ObterListaFormaPagamentoSnapshot(
            string nome)
        {
            try
            {
                var listaFormaPagamentoSnapshot = await _zendarSyncTrayApi.ObterListaFormaPagamentoSnapshot();

                if (listaFormaPagamentoSnapshot == null)
                    return null;

                listaFormaPagamentoSnapshot =
                    listaFormaPagamentoSnapshot?.Where(x => string.IsNullOrEmpty(nome) ||
                                                            TratarString(x.Nome).Contains(TratarString(nome)))
                                               ?.OrderBy(x => x.Nome)
                                               ?.Select(x => x)
                                               ?.ToList();

                return listaFormaPagamentoSnapshot;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        public async Task<GridPaginadaRetorno<ProdutoSnapshotViewModel>> ObterListaProdutoSnapshot(
            GridPaginadaConsulta gridPaginada,
            string nome)
        {
            try
            {
                var gridPaginadaRetorno = new GridPaginadaRetorno<ProdutoSnapshotViewModel>();

                var listaProdutoSnapshot = await _zendarSyncTrayApi.ObterListaProdutoSnapshot();

                if (listaProdutoSnapshot == null)
                    return null;

                var query =
                    listaProdutoSnapshot?.Where(x => string.IsNullOrEmpty(nome) ||
                                                                 TratarString(x.Nome).Contains(TratarString(nome)))
                                        ?.OrderBy(x => x.Nome)
                                        ?.Select(x => x)
                                        ?.AsQueryable();

                gridPaginadaRetorno.CarregarPaginacao(query, gridPaginada);

                return gridPaginadaRetorno;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

		public async Task<Guid?> ObterVinculoProduto(
			string siteId)
		{
			try
			{
				var produto = await _zendarSyncTrayApi.ObterProdutoSnapshotPorSiteId(siteId);

				return produto?.ZendarId;
			}
			catch { }

            return null;
		}

		public async Task<Guid?> ObterVinculoVariacao(
			string siteId)
		{
			try
			{
				var variacao = await _zendarSyncTrayApi.ObterVariacaoPorSiteId(siteId);

				return variacao?.ZendarId;
			}
			catch { }

            return null;
		}


		#endregion

		#region Helpers

		private static List<IntegracaoSituacaoPedidoViewModel> ObterListaSituacaoPadrao()
        {
            return new List<IntegracaoSituacaoPedidoViewModel>()
            {
                new IntegracaoSituacaoPedidoViewModel("A ENVIAR", "A enviar", IdentificacaoSituacao.FINALIZADO),
                new IntegracaoSituacaoPedidoViewModel("AGUARDANDO ENVIO", "Aguardando envio", IdentificacaoSituacao.FINALIZADO),
                new IntegracaoSituacaoPedidoViewModel("AGUARDANDO PAGAMENTO", "Aguardando pagamento", IdentificacaoSituacao.EM_ABERTO),
                new IntegracaoSituacaoPedidoViewModel("AGUARDANDO VINDI", "Aguardando Vindi", IdentificacaoSituacao.EM_ABERTO),
                new IntegracaoSituacaoPedidoViewModel("CANCELADO", "Cancelado", IdentificacaoSituacao.CANCELADO),
                new IntegracaoSituacaoPedidoViewModel("ENVIADO", "Enviado", IdentificacaoSituacao.FINALIZADO),
                new IntegracaoSituacaoPedidoViewModel("FINALIZADO", "Finalizado", IdentificacaoSituacao.FINALIZADO),
                new IntegracaoSituacaoPedidoViewModel("PENDENTE", "Pendente", IdentificacaoSituacao.PENDENTE),
                new IntegracaoSituacaoPedidoViewModel("A ENVIAR VINDI", "A enviar Vindi", IdentificacaoSituacao.FINALIZADO),
                new IntegracaoSituacaoPedidoViewModel("A RETIRAR", "A Retirar", IdentificacaoSituacao.FINALIZADO),
            };
        }

        private async Task<List<ProdutoV2ViewModel>> ObterListaProdutoSite(
            List<ProdutoV2ViewModel> listaProdutoPaginadoViewModel,
            List<EntidadeSnapshotViewModel> listaProdutoEntidadeViewModel,
            bool cadastro)
        {
            try
            {
                var listaProdutoCadastroPaginadoViewModel = new List<ProdutoV2ViewModel>();

                foreach (var produtoPaginadoViewModel in listaProdutoPaginadoViewModel.Where(c => c.Ativo))
                {
                    var produtoCadastradoEntidadeViewModel =
                        listaProdutoEntidadeViewModel.Where(c => c.ZendarId == produtoPaginadoViewModel.Id)
                                                     .FirstOrDefault();

                    var totalEstoqueAtual =
                       await _produtoCorTamanhoEstoqueRepository.ObterSaldoProduto(produtoPaginadoViewModel.Id, _aspNetUserInfo.LojaId.Value);

                    var estoqueAtual =
                        produtoPaginadoViewModel.ProdutoCores?
                                                .FirstOrDefault()?
                                                .ProdutoCorTamanhos?
                                                .FirstOrDefault()?
                                                .ProdutoCorTamanhoEstoques?
                                                .FirstOrDefault();

                    if (estoqueAtual != null)
                        estoqueAtual.EstoqueAtual = totalEstoqueAtual;

                    var produtoPrecoLoja = produtoPaginadoViewModel.Precos?
                                                                   .ProdutoPrecoLojas?
                                                                   .FirstOrDefault();

                    if (produtoPrecoLoja != null)
                    {
                        var precoProduto =
                            await _produtoRepository.ObterPrecoVendaCustoPorLoja(produtoPaginadoViewModel.Id, _aspNetUserInfo.LojaId.Value);

                        produtoPrecoLoja.PrecoVenda.PrecoVenda = precoProduto.precoVenda;
                        produtoPrecoLoja.PrecoCusto = precoProduto.precoCusto;
                    }

                    if (cadastro)
                    {
                        if (produtoCadastradoEntidadeViewModel != null)
                            listaProdutoCadastroPaginadoViewModel.Add(produtoPaginadoViewModel);
                    }
                    else
                    {
                        if (produtoCadastradoEntidadeViewModel == null)
                            listaProdutoCadastroPaginadoViewModel.Add(produtoPaginadoViewModel);
                    }
                }

                return listaProdutoCadastroPaginadoViewModel;
            }
            catch (Exception ex)
            {
                NotificarErro(ex.Message);

                return null;
            }
        }

        private string TratarString(
            string text)
        {
            text = text.ToLower();

            return new string(text
                .Normalize(NormalizationForm.FormD)
                .Where(ch => char.GetUnicodeCategory(ch) != UnicodeCategory.NonSpacingMark)
                .ToArray());
        }

        protected async Task SetZendarTriggerCache(Guid? lojaId = null)
        {
            bool possuiIntegracao = false;

            var integracaoTray =
                await Obter(lojaId);

            if (integracaoTray != null)
            {
                if (integracaoTray.SincronizacaoHabilitada)
                    possuiIntegracao = true;
            }

            await _sincronizacaoIntegracaoService.HabilitarTodas();
		}

        #endregion

        public void Dispose()
        {
            _integracaoService?.Dispose();
            _integracaoSituacaoPedidoService?.Dispose();
        }
    }
}
