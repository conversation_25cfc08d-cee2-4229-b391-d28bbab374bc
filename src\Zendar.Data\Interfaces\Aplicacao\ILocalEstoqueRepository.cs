﻿using Multiempresa.Shared.Helpers;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.Data.Helpers;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Enums;
using Zendar.Data.ViewModels;

namespace Zendar.Data.Interfaces.Aplicacao
{
    public interface ILocalEstoqueRepository : IRepository<LocalEstoque>
    {
        GridPaginadaRetorno<LocalEstoquePaginadaViewModel> ListarPaginado(GridPaginadaConsulta gridPaginada, string nome, StatusConsulta statusConsulta, Guid? lojaId);
        Task<List<IdNomeViewModel>> ListarSelect(StatusConsulta statusConsulta, Guid lojaId);
        Task<Guid> ObterLocalEstoqueIdPadrao(Guid lojaId);
        Task<LocalEstoque> ObterComFantasia(Guid Id);
        Task<string> ObterNome(Guid id);
        Task<List<string>> ObterNomes(IEnumerable<Guid> ids);
        Task<LocalEstoque> ObterComProdutos(Guid id);
    }
}
