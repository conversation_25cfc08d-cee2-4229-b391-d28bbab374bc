﻿using System;
using System.Collections.Generic;
using Zendar.Business.Helpers;
using Zendar.Data.Enums.Stargate;

namespace Zendar.Business.Interfaces
{
    public interface IAspNetUserInfo
    {
        string Nome { get; }

        string Token { get; }

        string NomeHeaderDesktop { get; }

        string Email { get; }

        string Id { get; }

        string CodigoContaEmpresa { get; }

        string HostUrl { get; }

        string Url { get; }

        bool Autenticado { get; }

        bool Trigger { get; }

        bool UsuarioAdministrador { get; }

        string SecurityStamp { get; }

        Guid? LojaId { get; }

        Guid? ChavePermissaoTemporaria { get; }

        int? TimezoneOffset { get; }

        string Identificador { get; }

        string DispositivoId { get; }

        string RefreshToken { get; }

        TipoSistema Sistema { get; }

        ReferenciaServicoStargate? Servico { get; }

        List<TokenServico> Servicos { get; }

        bool PossuiServico(ReferenciaServicoStargate servico);

        void PreencherCodigoContaEmpresa(string codigoContaEmpresa);

        void PreencherToken(string token);

        void PreencherLojaId(Guid? lojaId);

        void PreencherUsuarioId(string id);

        void PreencherHostUrl(string hostUrl);

        void PreencherTimezoneOffset(int timezoneOffset);

        void HabilitarTrigger(bool trigger);

        string ObterLoginCacheKey(string userId, ReferenciaServicoStargate? referenciaServico = null);
    }
}
