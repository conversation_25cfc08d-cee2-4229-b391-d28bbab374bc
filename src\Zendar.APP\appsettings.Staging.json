{"ConnectionStrings": {"DefaultConnection": "", "MultiEmpresaConnection": ""}, "StorageSettings": {"App": "DefaultEndpointsProtocol=https;AccountName=zendarapphom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarapphom.blob.core.windows.net/;QueueEndpoint=https://zendarapphom.queue.core.windows.net/;TableEndpoint=https://zendarapphom.table.core.windows.net/;FileEndpoint=https://zendarapphom.file.core.windows.net/;", "ArquivosFiscais": "DefaultEndpointsProtocol=https;AccountName=zendararquivosfiscaishom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendararquivosfiscaishom.blob.core.windows.net/;QueueEndpoint=https://zendararquivosfiscaishom.queue.core.windows.net/;TableEndpoint=https://zendararquivosfiscaishom.table.core.windows.net/;FileEndpoint=https://zendararquivosfiscaishom.file.core.windows.net/;", "Certificados": "DefaultEndpointsProtocol=https;AccountName=zendarcertificadoshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarcertificadoshom.blob.core.windows.net/;QueueEndpoint=https://zendarcertificadoshom.queue.core.windows.net/;TableEndpoint=https://zendarcertificadoshom.table.core.windows.net/;FileEndpoint=https://zendarcertificadoshom.file.core.windows.net/;", "Imagens": "DefaultEndpointsProtocol=https;AccountName=zendarimagenshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarimagenshom.blob.core.windows.net/;QueueEndpoint=https://zendarimagenshom.queue.core.windows.net/;TableEndpoint=https://zendarimagenshom.table.core.windows.net/;FileEndpoint=https://zendarimagenshom.file.core.windows.net/;", "Danfes": "DefaultEndpointsProtocol=https;AccountName=zendardanfeshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendardanfeshom.blob.core.windows.net/;QueueEndpoint=https://zendardanfeshom.queue.core.windows.net/;TableEndpoint=https://zendardanfeshom.table.core.windows.net/;FileEndpoint=https://zendardanfeshom.file.core.windows.net/;", "Importacao": "DefaultEndpointsProtocol=https;AccountName=zendarimportacaohom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarimportacaohom.blob.core.windows.net/;QueueEndpoint=https://zendarimportacaohom.queue.core.windows.net/;TableEndpoint=https://zendarimportacaohom.table.core.windows.net/;FileEndpoint=https://zendarimportacaohom.file.core.windows.net/;", "Backup": "DefaultEndpointsProtocol=https;AccountName=zendarbackuphom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarbackuphom.blob.core.windows.net/;QueueEndpoint=https://zendarbackuphom.queue.core.windows.net/;TableEndpoint=https://zendarbackuphom.table.core.windows.net/;FileEndpoint=https://zendarbackuphom.file.core.windows.net/;", "Relatorios": "DefaultEndpointsProtocol=https;AccountName=zendarrelatorioshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarrelatorioshom.blob.core.windows.net/;QueueEndpoint=https://zendarrelatorioshom.queue.core.windows.net/;TableEndpoint=https://zendarrelatorioshom.table.core.windows.net/;FileEndpoint=https://zendarrelatorioshom.file.core.windows.net/;", "ArquivosTemporarios": "DefaultEndpointsProtocol=https;AccountName=zendararqtemporarioshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendararqtemporarioshom.blob.core.windows.net/;QueueEndpoint=https://zendararqtemporarioshom.queue.core.windows.net/;TableEndpoint=https://zendararqtemporarioshom.table.core.windows.net/;FileEndpoint=https://zendararqtemporarioshom.file.core.windows.net/;"}, "JwtSettings": {"Chave": "", "ExpiracaoMinutos": 20, "Emissor": "Zendar-Homolog", "ValidoEm": "https://zendar-homolog-api.azurewebsites.net"}, "TemporaryAccess": {"Chave": "NcRfUjXn2r4u7x!A%D*G-KaPdSgVkYp3s6v8y/B?E(H+MbQeThWmZq4t7w!z%C&F)J@NcRfUjXn2r5u8x/A?D(G-KaPdSgVkYp3s6v9y$B&E)H@MbQeThWmZq4t7w!z%", "ExpiracaoToken": 5}, "LoginAplicacaoSettings": {"Login": "", "Hash": ""}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "None"}}, "EmailSettings": {"EmailsLogErro": "", "Layout": {"Powerstock": {"NomeSistema": "Powerstock", "CorTexto": "#194888", "UrlFundo": "https://zendarapphom.blob.core.windows.net/onboarding/email/fundo/powerstock.png", "UrlLogo": "https://zendarapphom.blob.core.windows.net/onboarding/email/logos/powerstock.png", "UrlBanner": "https://zendarapphom.blob.core.windows.net/onboarding/email/banner/powerstock.png", "CorDivisores": "#90CDF4", "IconePrimeiroAcesso": "https://zendarapphom.blob.core.windows.net/onboarding/email/icones/primeiro-acesso-powerstock.png", "IconeTreinamentoUso": "https://zendarapphom.blob.core.windows.net/onboarding/email/icones/treinamento-powerstock.png", "IconeCentralAjuda": "https://zendarapphom.blob.core.windows.net/onboarding/email/icones/central-ajuda-powerstock.png", "IconeDicasUso": "https://zendarapphom.blob.core.windows.net/onboarding/email/icones/dicas-uso-powerstock.png"}, "Zendar": {"NomeSistema": "Zendar", "CorTexto": "#5502B2", "UrlFundo": "https://zendarappdev.blob.core.windows.net/onboarding/email/fundo/zendar.png", "UrlLogo": "https://zendarappdev.blob.core.windows.net/onboarding/email/logos/zendar.png", "UrlBanner": "https://zendarappdev.blob.core.windows.net/onboarding/email/banner/zendar.png", "CorDivisores": "#D1BFF6", "IconePrimeiroAcesso": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/primeiro-acesso-zendar.png", "IconeTreinamentoUso": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/treinamento-zendar.png", "IconeCentralAjuda": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/central-ajuda-zendar.png", "IconeDicasUso": "https://zendarappdev.blob.core.windows.net/onboarding/email/icones/dicas-uso-zendar.png"}, "Fomer": {"NomeSistema": "Fomer", "CorTexto": "#FF005A", "UrlFundo": "https://zendarapphom.blob.core.windows.net/onboarding/email/fundo/fomer.png", "UrlLogo": "https://zendarapphom.blob.core.windows.net/onboarding/email/logos/fomer.png", "UrlBanner": "https://zendarapphom.blob.core.windows.net/onboarding/email/banner/fomer.png", "CorDivisores": "#FF84AC", "IconePrimeiroAcesso": "https://zendarapphom.blob.core.windows.net/onboarding/email/icones/primeiro-acesso-fomer.png", "IconeTreinamentoUso": "https://zendarapphom.blob.core.windows.net/onboarding/email/icones/treinamento-fomer.png", "IconeCentralAjuda": "https://zendarapphom.blob.core.windows.net/onboarding/email/icones/central-ajuda-fomer.png", "IconeDicasUso": "https://zendarapphom.blob.core.windows.net/onboarding/email/icones/dicas-uso-fomer.png", "FaleComEspecialista": "https://zendarapphom.blob.core.windows.net/onboarding/email/icones/especialista-fomer.png"}}}, "AllowedHosts": "*", "ServiceBusSettings": {"DefaultConnection": ""}, "MOVIDESK_TOKEN_API": "309afd53-b7d3-437c-ba7c-cdb169dda796", "SmartPOSSettings": {"EmailsParceiroStone": ""}, "ApiKey": "", "ZendarSyncApi": {"TrayUrl": ""}, "ApplicationInsights": {"InstrumentationKey": ""}, "ZoopSettingsAPI": {"MarketplaceId": "1548082506bc468f8e1eef6e1cfdd6ef", "SplitRecipientId": "fed3a41252bb408c954ebb29e7ed7cca", "Authorization": "Basic enBrX3Rlc3RfYTZ3ZmJSdHoyNFlMRU9QWTdEWWc5b2U5Og==", "UrlApi": "https://api.zoop.ws/v1/marketplaces"}, "ZenflixSettings": {"ZenflixUrl": ""}, "RedisSettings": {"Endpoint": "", "Port": "", "Password": "", "Configuration": "", "InstanceName": ""}}