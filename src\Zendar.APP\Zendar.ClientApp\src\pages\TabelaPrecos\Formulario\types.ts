import { TabelaPrecoItensProps } from './validationForm';

export type FieldArrayType = {
  tabelaPrecoProdutoCorTamanhos: TabelaPrecoItensProps[];
  tabelaPrecoProdutos: TabelaPrecoItensProps[];
};
export type Loja = {
  id: string;
  fantasia: string;
  endereco: string;
  cidade: string;
  selecionado: boolean;
};

export type ProdutoResponse = {
  produtoNome: string;
  corDescricao?: string;
  tamanhoDescricao?: string;
  precoVenda: number;
  precoCusto: number;
  markup: number;
  produtoCorTamanhoId?: string;
};

export type ProdutosProps = {
  codigo: string;
  precoVenda: number;
}[];

export type TabelaPrecoCadastrarProps = {
  id: string;
  valor: number;
};

export interface FormularioProps {
  isReadonly?: boolean;
  tabelaPrecoId?: string;
  isAlterar?: boolean;
}

export type ProdutoTabelaPrecoProps = {
  tabelaPrecoId: string;
  produtoCorTamanhoId?: string;
  produtoId?: string;
  precoVenda?: number;
};

export type ProdutoComErro = {
  linha: number;
  codigo: string;
  nome: string;
  precoVendaAtual: string;
  precoVendaNovo: string;
  descricaoErro: string;
  dadosOriginais: string;
};

export type ResultadoImportacao = {
  produtosComSucesso: ProdutosProps;
  produtosComErro: ProdutoComErro[];
  totalProcessados: number;
};
