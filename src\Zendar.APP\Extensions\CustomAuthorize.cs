﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Multiempresa.Data.Enum;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Interfaces.Services.MultiEmpresa;
using Zendar.Business.ViewModels.Autenticacao;

namespace Zendar.APP.Extensions
{
    public class CustomAuthorization
    {
        public static bool ValidarClaimsUsuario(UsuarioAutenticadoCacheViewModel userCache, List<string> permissoes)
        {
            return userCache.Permissoes.Any(p => p.Permissao && permissoes.Any(x => p.Funcionalidade == x));
        }
    }

    public class ClaimsAuthorizeAttribute : TypeFilterAttribute
    {
        public ClaimsAuthorizeAttribute(params string[] claimValues) : base(typeof(RequisitoClaimFilter))
        {
            Arguments = new object[] { claimValues.ToList(), false };
        }
    }

    public class ClaimsAuthorizeWithTempAuthorizationAttribute : TypeFilterAttribute
    {
        public ClaimsAuthorizeWithTempAuthorizationAttribute(params string[] claimValues) : base(typeof(RequisitoClaimFilter))
        {
            Arguments = new object[] { claimValues.ToList(), true };
        }
    }

    public class RequisitoClaimFilter : IAsyncAuthorizationFilter
    {
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly ICacheService _cacheService;
        private readonly IClienteMultiEmpresaService _clienteMultiEmpresaService;

        private readonly List<string> _permissoes;
        private readonly bool _tempAuthorization;


        public RequisitoClaimFilter(List<string> permissoes,
                                    bool tempAuthorization,
                                    IAspNetUserInfo aspNetUserInfo,
                                    ICacheService cacheService,
                                    IClienteMultiEmpresaService clienteMultiEmpresaService)
        {
            _permissoes = permissoes;
            _tempAuthorization = tempAuthorization;
            _aspNetUserInfo = aspNetUserInfo;
            _cacheService = cacheService;
            _clienteMultiEmpresaService = clienteMultiEmpresaService;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            if (!await _clienteMultiEmpresaService.ValidarVersaoBancoDados(_aspNetUserInfo.HostUrl))
            {
                context.Result = new StatusCodeResult(StatusCodes.Status423Locked);
                return;
            }

            var cacheKey = _aspNetUserInfo.ObterLoginCacheKey(_aspNetUserInfo.Id, _aspNetUserInfo.Servico);
            var userCache = await _cacheService.ObterAsync<UsuarioAutenticadoCacheViewModel>(cacheKey);

            if (!context.HttpContext.User.Identity.IsAuthenticated || !ValidarSecurityStamp(userCache))
            {
                context.Result = new StatusCodeResult(StatusCodes.Status401Unauthorized);
                return;
            }

            if (_permissoes.Any())
            {
                if (!CustomAuthorization.ValidarClaimsUsuario(userCache, _permissoes))
                {
                    if (_tempAuthorization && _aspNetUserInfo.ChavePermissaoTemporaria.HasValue)
                    {
                        var permissoesTemporarias = await _cacheService.ObterAsync<List<string>>(_aspNetUserInfo.ChavePermissaoTemporaria.ToString());

                        if (permissoesTemporarias != null && permissoesTemporarias.Any())
                        {
                            if (_permissoes.Any(x => permissoesTemporarias.Any(y => y == x))) return;
                        }
                    }

                    context.Result = new StatusCodeResult(StatusCodes.Status405MethodNotAllowed);
                }
            }
        }

        private bool ValidarSecurityStamp(UsuarioAutenticadoCacheViewModel userCache)
        {
            return _aspNetUserInfo.SecurityStamp == userCache?.SecurityStamp;
        }
    }
}
