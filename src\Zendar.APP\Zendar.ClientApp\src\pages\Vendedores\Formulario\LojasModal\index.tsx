import { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>lex,
  ModalBody,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  Text,
  VStack,
  Switch,
  useDisclosure,
  ModalProps,
  Button,
  useMediaQuery,
  HStack,
} from '@chakra-ui/react';
import { create, InstanceProps } from 'react-modal-promise';

import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import ShadowScrollbar from 'components/PDV/Geral/ShadowScrollbar';
import useWindowSize from 'helpers/layout/useWindowSize';

export interface LojasInterface {
  id: string;
  fantasia: string;
  endereco: string;
  cidade: string;
  selecionado: boolean;
}

interface ModalLojasProps
  extends Omit<ModalProps, 'children' | 'isOpen' | 'onClose'>,
    InstanceProps<void> {
  lojasState: LojasInterface[];
  title?: string;
  handleLojas?: (newLojas: LojasInterface[], onClose?: () => void) => void;
}

const ModalLojasComponent = ({
  title = 'Selecionar lojas',
  lojasState,
  handleLojas,
}: ModalLojasProps) => {
  const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });
  const [lojas, setLojas] = useState<LojasInterface[]>([] as LojasInterface[]);
  const { height: windowHeight } = useWindowSize();
  const [isSmallerThan900] = useMediaQuery('(max-width: 900px)');
  const isZoomed = window.devicePixelRatio > 1;

  const handleToogleLojaSelecionada = useCallback((idLoja: string) => {
    setLojas((prev) =>
      prev.map((lojaItem) => {
        const { id, selecionado } = lojaItem;

        return {
          ...lojaItem,
          selecionado: id === idLoja ? !selecionado : selecionado,
        };
      })
    );
  }, []);

  useEffect(() => {
    setLojas(lojasState);
  }, [lojasState]);

  return (
    <ModalPadraoChakra
      isOpen={isOpen}
      onClose={onClose}
      isCentered
      size={!isSmallerThan900 ? 'xl' : 'full'}
    >
      <ModalContent
        marginBottom={{ base: 0, md: '3.75rem' }}
        marginTop={{ base: 0, md: '3.75rem' }}
        h="unset"
        maxW={{ base: '100%', md: '600px' }}
      >
        <ModalHeader
          mt={isSmallerThan900 ? 12 : undefined}
          mb={isSmallerThan900 ? 8 : undefined}
          borderBottom="1px"
          borderColor="gray.100"
          px="0"
          mx={{ base: 6, md: 8 }}
        >
          <VStack alignItems="flex-start" spacing="1" lineHeight="1">
            <Text color="primary.500" fontSize={{ base: 'xl', md: 'md' }}>
              {title}
            </Text>
            <Text color="gray.400" fontSize={{ base: 'sm', md: 'xs' }}>
              Marque apenas as lojas que ficarão habilitadas neste cadastro
            </Text>
          </VStack>
        </ModalHeader>
        <ModalBody p={{ base: 6, md: 8 }}>
          <ShadowScrollbar
            maxHeight={
              isSmallerThan900
                ? windowHeight - 340
                : windowHeight - (isZoomed ? 440 : 640)
            }
            paddingTop="0"
            exibirScrollVertical
            renderViewStyle={{ paddingLeft: '6px', paddingRight: '6px' }}
            shadowTopStyle={{
              background:
                'transparent linear-gradient(180deg,rgb(255, 255, 255)  0%,  #FFFFFF00 100%) 0% 0% no-repeat padding-box',
              height: 40,
            }}
            shadowBottomStyle={{
              background:
                'transparent linear-gradient(180deg, #FFFFFF00 0%,rgb(255, 255, 255) 100%) 0% 0% no-repeat padding-box',
              height: 40,
            }}
          >
            <VStack spacing="6" alignItems="flex-start">
              {lojas.map((lojaItem) => (
                <HStack
                  key={lojaItem.id}
                  as="label"
                  mb="0 !important"
                  cursor="pointer"
                  userSelect="none"
                  spacing="6"
                  alignItems="center"
                >
                  <Switch
                    name={`loja.${lojaItem.id}`}
                    isChecked={lojaItem.selecionado}
                    width="min-content"
                    colorScheme="primary"
                    onChange={() => handleToogleLojaSelecionada(lojaItem.id)}
                  />
                  <VStack spacing="1" lineHeight="none" alignItems="flex-start">
                    <Text fontSize="sm" fontWeight="bold">
                      {`${lojaItem.fantasia} | ${lojaItem.cidade}`}
                    </Text>
                    <Text fontSize="xs" color="gray.300">
                      {lojaItem.endereco}
                    </Text>
                  </VStack>
                </HStack>
              ))}
            </VStack>
          </ShadowScrollbar>
        </ModalBody>
        <ModalFooter pb="24px" justifyContent="center" gap={{ base: 4, md: 6 }}>
          <Button
            id="fechar"
            name="fechar"
            variant="cancel"
            colorScheme="red"
            minW="min-content"
            width={{ base: 'full', sm: '100px' }}
            fontWeight="normal"
            color="gray.500"
            borderColor="gray.500"
            onClick={onClose}
          >
            Fechar
          </Button>
          <Button
            id="salvarDocumentoExportacao"
            name="salvarDocumentoExportacao"
            colorScheme="secondary"
            minW="min-content"
            width={{ base: 'full', sm: '120px' }}
            fontWeight="normal"
            onClick={() => {
              if (handleLojas) {
                handleLojas(lojas, onClose);
              }
            }}
          >
            Confirmar
          </Button>
        </ModalFooter>
      </ModalContent>
    </ModalPadraoChakra>
  );
};

export const ModalLojas = create<ModalLojasProps>(ModalLojasComponent);
