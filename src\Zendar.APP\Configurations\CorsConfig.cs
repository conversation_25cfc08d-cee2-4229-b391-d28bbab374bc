﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Zendar.APP.Configurations
{
    public static class OriginsSettings
    {
        public static IServiceCollection AddCorsConfiguration(this IServiceCollection services, IWebHostEnvironment webHostEnvironment, string name)
        {
            if (webHostEnvironment.IsDevelopment())
            {
                string[] origins = new[]
                {
                    // Zendar
                    "http://localhost:3000",
                    "https://zendar-dev-app.azurewebsites.net",
                    "https://zendar-hom-app.azurewebsites.net",

                    // Zenflix
                    "https://localhost:5000",
                    "http://localhost:5000",

                    "https://localhost:7152/",
                    "https://sti3dashboard-harvey-homolog.azurewebsites.net",

                    // Stargate
					"https://localhost:44331",
                    "https://stargate-fomer-dev-api.azurewebsites.net",

                    // Fomer
					"http://localhost:3001",
                    "https://fomer-dev-app.azurewebsites.net",

                    // Delivery
					"http://localhost:3002",
                    "https://fomerdelivery-dev-app.azurewebsites.net"
                };

                services.AddCors(o => o.AddPolicy(name, builder =>
                {
                    builder.AllowAnyOrigin()
                           .AllowAnyMethod()
                           .AllowAnyHeader()
                           .AllowCredentials()
                           .WithOrigins(origins)
                           .Build();
                }));
            }
            else if (webHostEnvironment.IsStaging())
            {
                services.AddCors(opt =>
                {
                    opt.AddPolicy(name,
                        builder =>
                        {
                            builder.AllowAnyOrigin()
                                   .AllowAnyHeader()
                                   .AllowAnyMethod()
                                   .AllowCredentials()
                                   .WithOrigins("https://homolog.fomer.app", "https://homolog.powerchef.app", "https://homolog.zendar.app", "https://homolog.powerstock.app", "https://revenda.powerstock.app", "https://sti3dashboard-harvey-homolog.azurewebsites.net", "https://semprenamoda.powerstock.app", "https://zendar-homolog-app.azurewebsites.net", "https://zendar-dev-app.azurewebsites.net", "http://localhost:3000", "https://multipay-api.azurewebsites.net", "https://zenflix-homolog-api.azurewebsites.net", "https://zendar-hom-app.azurewebsites.net")
                                   .SetIsOriginAllowedToAllowWildcardSubdomains()
                                   .Build();
                        });
                });
            }
            else
            {
                services.AddCors(opt =>
                {
                    opt.AddPolicy(name,
                        builder =>
                        {
                            builder.AllowAnyOrigin()
                              .AllowAnyHeader()
                              .AllowAnyMethod()
                              .AllowCredentials()
                              .WithOrigins(
                                "https://*.zendar.com.br",
                                "https://*.fomer.app",
                                "https://*.powerchef.app",
                                "https://*.zendar.app",
                                "https://*.powerstock.app",
                                "https://*.fomersistema.app",
                                "https://*.fomer.delivery",
                                "https://*.oidelivery.app",
                                "https://zendar-multiempresa-api.azurewebsites.net",
                                "https://multipay-api.azurewebsites.net",
                                "https://zenflix-api.azurewebsites.net",
                                "https://fomer-multiempresa-api.azurewebsites.net",
								"https://smartpos-background.azurewebsites.net")
                              .SetIsOriginAllowedToAllowWildcardSubdomains()
                              .Build();
                        });
                });
            }

            return services;
        }
    }
}
