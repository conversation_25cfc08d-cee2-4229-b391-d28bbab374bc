import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { setDateMaxHours, setDateMinHours } from 'helpers/data/setHoursDate';

import OptionType from 'types/optionType';

import { IdentificacaoIntegracao } from 'constants/enum/IdentificacaoIntegracao';
import { enumRelatorioProdutosVendidosPadrao } from 'constants/enum/RelatoriosPadrao';
import StatusConsultaEnum from 'constants/enum/statusConsulta';
import { StatusOperacao } from 'constants/enum/statusVenda';
import TipoFiltroProdutoEstoqueEnum from 'constants/enum/tipoFiltroProdutoEstoque';
import { TipoFiscal } from 'constants/enum/tipoFiscal';
import ConstanteMensagemValidacao from 'constants/mensagensValidacoes';

export type CamposPersonalizados = {
  campoPersonalizadoId: string;
  valor: string;
};

type ClienteSelectProps = {
  label: string;
  value: string;
};

export type FormData = {
  tipoRelatorio: number | null;
  produtoId: OptionType<string>;
  dataEmissaoInicio: Date;
  dataEmissaoFim: Date;
  tipoEstoque: number;
  statusConsulta: number;
  cores: string;
  tamanhos: string;
  categoriasProduto: string;
  marcas: string;
  tags: string;
  camposPersonalizados: CamposPersonalizados[];
  vendedorId: string;
  clienteFornecedorId: ClienteSelectProps | null;
  numeracaoComandaInicial?: number;
  numeracaoComandaFinal?: number;
  tipoAgrupamento?: number;
  origem?: number | null;
  tipoFiscal?: number[] | null;
  localEstoqueIds?: string;
  statusVenda?: number;
};

const schema = yup.object().shape({
  tipoRelatorio: yup
    .number()
    .nullable()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
  dataEmissaoInicio: yup
    .date()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .typeError(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .test(
      'data-inicio-menor-final',
      'Data inicial maior que a final.',
      function (value) {
        const { dataEmissaoFim } = this.parent;
        return (
          !value ||
          !dataEmissaoFim ||
          new Date(value) <= new Date(dataEmissaoFim)
        );
      }
    ),
  dataEmissaoFim: yup
    .date()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .typeError(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .test(
      'data-final-maior-inicio',
      'Data final menor que a inicial.',
      function (value) {
        const { dataEmissaoInicio } = this.parent;
        return (
          !dataEmissaoInicio ||
          !value ||
          new Date(value) >= new Date(dataEmissaoInicio)
        );
      }
    ),
  tipoAgrupamento: yup
    .number()
    .nullable()
    .when('tipoRelatorio', {
      is: (value) =>
        value === enumRelatorioProdutosVendidosPadrao.ITENS_MAIS_VENDIDOS,
      then: yup.number().required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
    }),
});

export const yupResolver = yupResolverInstance(schema);

export const valoresPadraoRelatorioProdutosVendidos = {
  dataEmissaoInicio: setDateMinHours(new Date()),
  dataEmissaoFim: setDateMaxHours(new Date()),
  clienteFornecedorId: null,
  statusConsulta: StatusConsultaEnum.TODOS,
  tipoEstoque: TipoFiltroProdutoEstoqueEnum.TODOS,
  origem: IdentificacaoIntegracao.TODAS,
  tipoFiscal: [TipoFiscal.NFCE, TipoFiscal.NFE, TipoFiscal.SEM_FISCAL],
  statusOperacao: StatusOperacao.TODAS,
  localEstoqueId: undefined,
};
