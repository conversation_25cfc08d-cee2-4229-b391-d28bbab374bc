﻿using Microsoft.EntityFrameworkCore;
using Multiempresa.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Zendar.Data.Contexts;
using Zendar.Data.Enums;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.ViewModels;

namespace Zendar.Data.Repository.Aplicacao
{
    public class CaixaMovimentacaoRepository : RepositoryAplicacao<CaixaMovimentacao>, ICaixaMovimentacaoRepository
    {
        public CaixaMovimentacaoRepository(AplicacaoContexto context) : base(context)
        {
        }

        public GridPaginadaRetorno<ControleCaixaPaginadaViewModel> ListarPaginado(GridPaginadaConsulta gridPaginada, ControleCaixaFiltrosPaginadaViewModel filtros, Guid lojaId, Guid usuarioId, bool listarTodosCaixas)
        {
            var gridPaginadaRetorno = new GridPaginadaRetorno<ControleCaixaPaginadaViewModel>();

            var query = FiltrarCaixaMovimentacao(filtros, lojaId);

            if (!listarTodosCaixas)
                query = query.Where(x => x.UsuarioAberturaId.Equals(usuarioId));


            var queryOperacao = query.Where(x => x.ContaFinanceira.TipoContaFinanceira == TipoContaFinanceira.CAIXA ||
                                                 x.ContaFinanceira.Dispositivo != null)
                                     .OrderBy($"{gridPaginada.CampoOrdenacao} {gridPaginada.DirecaoOrdenacao}")
                                     .Select(o => new ControleCaixaPaginadaViewModel
                                     {
                                         CaixaMovimentacaoId = o.Id,
                                         DataHoraAbertura = o.DataHoraAbertura,
                                         DataHoraFechamento = o.DataHoraFechamento,
                                         ContaFinanceiraNome = o.ContaFinanceira.Nome,
                                         UsuarioAberturaId = o.UsuarioAbertura.Id,
                                         UsuarioAberturaNome = o.UsuarioAbertura.Nome,
                                         Situacao = o.UsuarioFechamentoId.HasValue ? "Caixa fechado" : "Caixa aberto",
                                         CaixaDispositivo = o.ContaFinanceira.Dispositivo != null
                                     });

            gridPaginadaRetorno.CarregarPaginacao(queryOperacao, gridPaginada);

            //Busco o ultimo caixa aberto pelo usuario
            var ultimoCaixaAbertoUsuarioLogado = gridPaginadaRetorno.Registros
                .OrderByDescending(x => x.DataHoraFechamento)
                .FirstOrDefault(x => x.UsuarioAberturaId == usuarioId && x.DataHoraFechamento.HasValue);

            if (ultimoCaixaAbertoUsuarioLogado != null)
            {
                //Caso exista, verifico de o usuario tem um caixa aberto, se não tiver, aplico true para a flag ReabrirCaixa no ultimo caixa do usuario
                ultimoCaixaAbertoUsuarioLogado.ReabrirCaixa = !ExisteCaixaAberto(usuarioId, lojaId);
            }

            return gridPaginadaRetorno;
        }

        public GridPaginadaRetorno<ControleCaixaPaginadaViewModel> ListarCaixaMovelPaginado(GridPaginadaConsulta gridPaginada, ControleCaixaFiltrosPaginadaViewModel filtros, Guid lojaId, Guid usuarioId, bool listarTodosCaixas)
        {
            var gridPaginadaRetorno = new GridPaginadaRetorno<ControleCaixaPaginadaViewModel>();

            var query = FiltrarCaixaMovimentacao(filtros, lojaId);

            if (!listarTodosCaixas)
                query = query.Where(x => x.UsuarioAberturaId.Equals(usuarioId));


            var queryOperacao = query.Where(x => x.ContaFinanceira.TipoContaFinanceira == TipoContaFinanceira.CAIXA_MOVEL ||
                                                 x.ContaFinanceira.Dispositivo != null)
                                     .OrderBy($"{gridPaginada.CampoOrdenacao} {gridPaginada.DirecaoOrdenacao}")
                                     .Select(o => new ControleCaixaPaginadaViewModel
                                     {
                                         CaixaMovimentacaoId = o.Id,
                                         DataHoraAbertura = o.DataHoraAbertura,
                                         DataHoraFechamento = o.DataHoraFechamento,
                                         ContaFinanceiraNome = o.ContaFinanceira.Nome,
                                         UsuarioAberturaId = o.UsuarioAbertura.Id,
                                         UsuarioAberturaNome = o.UsuarioAbertura.Nome,
                                         Situacao = o.UsuarioFechamentoId.HasValue ? "Caixa fechado" : "Caixa aberto",
                                         CaixaDispositivo = o.ContaFinanceira.Dispositivo != null
                                     });

            gridPaginadaRetorno.CarregarPaginacao(queryOperacao, gridPaginada);

            //Busco o ultimo caixa aberto pelo usuario
            var ultimoCaixaAbertoUsuarioLogado = gridPaginadaRetorno.Registros
                .OrderByDescending(x => x.DataHoraFechamento)
                .FirstOrDefault(x => x.UsuarioAberturaId == usuarioId && x.DataHoraFechamento.HasValue);

            if (ultimoCaixaAbertoUsuarioLogado != null)
            {
                //Caso exista, verifico de o usuario tem um caixa aberto, se não tiver, aplico true para a flag ReabrirCaixa no ultimo caixa do usuario
                ultimoCaixaAbertoUsuarioLogado.ReabrirCaixa = !ExisteCaixaAberto(usuarioId, lojaId);
            }

            return gridPaginadaRetorno;
        }

        private IQueryable<CaixaMovimentacao> FiltrarCaixaMovimentacao(ControleCaixaFiltrosPaginadaViewModel filtros, Guid lojaId)
        {
            var query = DbSet.Where(o => o.DataHoraAbertura >= filtros.DataInicioAbertura &&
                                         o.DataHoraAbertura <= filtros.DataFimAbertura &&
                                         o.ContaFinanceira.LojaId.Equals(lojaId));

            if (filtros.ContafinanceiraId.HasValue)
            {
                query = query.Where(o => o.ContaFinanceiraId == filtros.ContafinanceiraId.Value);
            }

            if (filtros.UsuarioId.HasValue)
            {
                query = query.Where(o => o.UsuarioAberturaId.Equals(filtros.UsuarioId.Value));
            }

            return query;
        }

        public async Task<CaixaMovimentacao> ObterParaFecharCaixa(Guid idCaixaMovimentacao)
        {
            return await DbSet.Where(x => x.Id.Equals(idCaixaMovimentacao))
                .Include(x => x.ContaFinanceira).ThenInclude(c => c.Dispositivo)
                .Include(x => x.CaixaConferencias)
                .FirstOrDefaultAsync();
        }

        public async Task<Guid?> ObterIdUltimaMovimentacaoCaixa(Guid contaFinanceira)
        {
            var idMovimentacao = await DbSet
                .Where(x => x.ContaFinanceiraId == contaFinanceira && !x.DataHoraFechamento.HasValue)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            return idMovimentacao == Guid.Empty ? null : idMovimentacao;
        }

        private bool ExisteCaixaAberto(Guid usuarioId, Guid lojaId)
        {
            return DbSet
                .Any(x => x.UsuarioAberturaId == usuarioId && x.ContaFinanceira.LojaId == lojaId &&
                          !x.DataHoraFechamento.HasValue &&
                          x.ContaFinanceira.TipoContaFinanceira == TipoContaFinanceira.CAIXA && 
                          x.ContaFinanceira.Dispositivo == null);

        }

        public async Task<CaixaViewModel> ObterFechamentoCaixaSimples(Guid idCaixaMovimentacao)
        {
            return await DbSet.Where(x => x.Id.Equals(idCaixaMovimentacao))
                .Select(x => new CaixaViewModel
                {
                    ContaFinanceiraIcone = x.ContaFinanceira.Icone,
                    ContaFinanceiraNome = x.ContaFinanceira.Nome,
                    SaldoInicial = x.SaldoAbertura,
                    DataHoraAbertura = x.DataHoraAbertura,
                    UsuarioAbertura = x.UsuarioAbertura.Nome,
                    DataHoraFechamento = x.DataHoraFechamento,
                    UsuarioFechamento = x.UsuarioFechamento.Nome,
                }).FirstOrDefaultAsync();
        }

        public Task<CaixaMovimentacao> ObterInformacoesCaixa(Guid id)
        {
            return DbSet.Where(c => (c.MovimentacoesFinanceirasBaixa.Any() || c.Operacoes.Any()) && c.Id.Equals(id)).Select(c => new CaixaMovimentacao
            {
                ContaFinanceira = new ContaFinanceira
                {
                    Icone = c.ContaFinanceira.Icone,
                    Nome = c.ContaFinanceira.Nome
                },
                UsuarioAbertura = new Usuario
                {
                    Nome = c.UsuarioAbertura.Nome
                },
                DataHoraAbertura = c.DataHoraAbertura,
                UsuarioFechamento = new Usuario
                {
                    Nome = c.UsuarioFechamento.Nome
                },
                DataHoraFechamento = c.DataHoraFechamento,
                SaldoAbertura = c.SaldoAbertura
            }).FirstOrDefaultAsync();
        }

        public async Task<CaixaMovimentacao> ObterMovimentacaoDispositivo(string identificador, Guid lojaId, Guid usuarioId, Guid? caixaMovimentacaoId = null, Guid? agrupamentoIntegracaoId = null)
        {
            return await DbSet.Where(c => c.UsuarioAberturaId == usuarioId &&
                                          c.UsuarioFechamentoId == null &&
                                          c.ContaFinanceira.LojaId == lojaId &&
                                          c.ContaFinanceira.Dispositivo.Identificador == identificador &&
                                          ((!caixaMovimentacaoId.HasValue || c.Id == caixaMovimentacaoId.Value) ||
                                           (!agrupamentoIntegracaoId.HasValue || c.AgrupamentoIntegracaoId == agrupamentoIntegracaoId.Value)))
                               .Select(c => new CaixaMovimentacao
                               {
                                   Id = c.Id,
                                   AgrupamentoIntegracaoId = c.AgrupamentoIntegracaoId,
                                   ContaFinanceiraId = c.ContaFinanceiraId,
                                   ContaFinanceira = new ContaFinanceira
                                   {
                                       Id = c.ContaFinanceira.Id,
                                       Dispositivo = new Dispositivo
                                       {
                                           Id = c.ContaFinanceira.Dispositivo.Id,
                                           ReferenciaServico = c.ContaFinanceira.Dispositivo.ReferenciaServico
                                       }
                                   }
                               })
                               .FirstOrDefaultAsync();
        }

        public async Task<CaixaMovimentacao> ObterUltimoCaixaMovimentacaoAberto(Guid contaFinanceiraId, Guid lojaId)
        {
            return await DbSet.Where(x => x.ContaFinanceiraId == contaFinanceiraId && x.UsuarioFechamentoId != null && x.ContaFinanceira.LojaId == lojaId)
                              .Select(x => new CaixaMovimentacao
                              {
                                  DataHoraFechamento = x.DataHoraFechamento,
                                  UsuarioFechamento = new Usuario { Nome = x.UsuarioFechamento.Nome }
                              })
                              .OrderByDescending(x => x.DataHoraFechamento)
                              .FirstOrDefaultAsync();
        }

        public async Task<Guid> ObterIdContaFinanceiraPeloCaixaMovimentacao(Guid caixaMovimentacaoId)
            => await DbSet
                        .Where(x => x.Id == caixaMovimentacaoId)
                        .Select(x => x.ContaFinanceiraId)
                        .FirstOrDefaultAsync();

        public async Task<CaixaMovimentacao> ObterConferenciaDinheiroCheque(Guid caixaMovimentacao)
        {
            return await DbSet
                            .Where(x => x.Id == caixaMovimentacao)
                            .Select(x => new CaixaMovimentacao
                            {
                                ContaFinanceiraId = x.ContaFinanceiraId,
                                CaixaConferencias = x.CaixaConferencias.Where(y => y.FormaPagamento.MeioPagamento.RegraMeioPagamento == RegraMeioPagamento.DINHEIRO ||
                                                                                   y.FormaPagamento.MeioPagamento.RegraMeioPagamento == RegraMeioPagamento.CHEQUE)
                                                                       .Select(y => new CaixaConferencia
                                                                       {
                                                                           FormaPagamentoId = y.FormaPagamentoId,
                                                                           FormaPagamento = new FormaPagamentoRecebimento { Nome = y.FormaPagamento.Nome },
                                                                           ValorTotalFormaPagamento = y.ValorTotalFormaPagamento,
                                                                       })
                                                                       .ToList()
                            })
                            .FirstOrDefaultAsync();
        }

        public async Task<List<Guid>> ObterListaIdCaixaAbertoUsuario(Guid usuarioId, Guid lojaId)
        {
            return await DbSet
                            .Where(x => x.UsuarioAberturaId == usuarioId &&
                                          x.UsuarioFechamentoId == null &&
                                          x.ContaFinanceira.LojaId == lojaId &&
                                          x.ContaFinanceira.Ativo &&
                                          (x.ContaFinanceira.TipoContaFinanceira == TipoContaFinanceira.CAIXA ||
                                           x.ContaFinanceira.Dispositivo != null))
                            .Select(x => x.Id)
                            .ToListAsync();
        }

        public async Task<CaixaMovimentacao> ObterComOperacoesParaVincularAgrupamnetoIntegracaoId(Guid caixaMovimentacaoId)
        {
            return await DbSet
                            .Where(x => x.Id == caixaMovimentacaoId)
                            .Include(x => x.Operacoes)
                            .FirstOrDefaultAsync();
        }

        public async Task<CaixaMovimentacao> ObterComOperacoesParaVincularAgrupamnetoIntegracaoId(Guid lojaId, string identificadorDispositivo)
        {
            return await DbSet
                            .Where(x => x.ContaFinanceira.LojaId == lojaId &&
                                        x.ContaFinanceira.Dispositivo.Identificador == identificadorDispositivo &&
                                        x.UsuarioFechamentoId == null &&
                                        x.ContaFinanceira.Dispositivo != null)
                            .Include(x => x.Operacoes)
                            .FirstOrDefaultAsync();
        }

        public async Task<List<IdValorViewModel>> ObterMovimentacoes(Guid caixaMovimentacao)
        {
            var operacaoSemBaixa = Db.Operacao
                                            .Where(x => x.CaixaMovimentacaoId == caixaMovimentacao &&
                                                        x.Status == StatusOperacao.EFETUADA)
                                            .SelectMany(x => x.MovimentacoesFinanceiras
                                                                    .Where(y => y.FormaPagamentoRecebimento.MeioPagamento.GerarBaixa == false)
                                                                    .Select(y => new IdValorViewModel
                                                                    {
                                                                        Id = y.FormaPagamentoRecebimentoId,
                                                                        Valor = x.TipoOperacao.AcaoFinanceira == TipoAcao.ENTRADA ? y.Valor : -y.Valor
                                                                    }));

            var baixasEntrada = Db.MovimentacaoFinanceiraBaixa
                                        .Where(x => x.CaixaMovimentacaoId == caixaMovimentacao &&
                                                    x.MovimentacaoFinanceira.Operacao.Status == StatusOperacao.EFETUADA)
                                        .Select(x => new IdValorViewModel
                                        {
                                            Id = x.FormaPagamentoRecebimentoId,
                                            Valor = x.AcaoFinanceira == TipoAcao.ENTRADA ? x.Valor : -x.Valor
                                        });

            return await operacaoSemBaixa.Union(baixasEntrada).ToListAsync();
        }

        public async Task<bool> UltimoCaixaFechado(Guid lojaId, DateTime dataHoraFechamento)
        {
            return !await DbSet.AnyAsync(x => x.DataHoraFechamento > dataHoraFechamento && x.ContaFinanceira.LojaId == lojaId);
        }

        public async Task<CaixaMovimentacao> ObterUltimoCaixaPorContaFinanceira(Guid contaFinanceiraId)
        {
            var caixaMovimentacao = await DbSet
                .Where(cm => cm.ContaFinanceiraId == contaFinanceiraId)
                .OrderByDescending(cm => cm.DataHoraAbertura)
                .FirstOrDefaultAsync();

            return caixaMovimentacao;
        }

        public async Task<CaixaMovimentacao> ObterComConferencias(Guid caixaMovimentacaoId)
        {
            return await DbSet
                .Where(cm => cm.Id == caixaMovimentacaoId)
                .Include(cm => cm.CaixaConferencias)
                .FirstOrDefaultAsync();
        }

        public async Task<CaixaMovimentacao> ObterPorAgrupamentoIntegracaoIdELojaId(
            Guid agrupamentoIntegracaoId,
            Guid lojaId)
        {
            return await DbSet.Where(c => c.ContaFinanceira.LojaId == lojaId &&
                                          c.AgrupamentoIntegracaoId == agrupamentoIntegracaoId)
                               .Select(c => new CaixaMovimentacao
                               {
                                   Id = c.Id,
                                   AgrupamentoIntegracaoId = c.AgrupamentoIntegracaoId,
                                   ContaFinanceiraId = c.ContaFinanceiraId,
                                   ContaFinanceira = new ContaFinanceira
                                   {
                                       Id = c.ContaFinanceira.Id,
                                       Dispositivo = new Dispositivo
                                       {
                                           Id = c.ContaFinanceira.Dispositivo.Id
                                       }
                                   }
                               })
                               .FirstOrDefaultAsync();
        }

        public async Task<CaixaMovimentacao> ObterPorAgrupamentoIntegracaoIdELojaIdEUsuarioId(
            Guid agrupamentoIntegracaoId,
            Guid lojaId,
            Guid usuarioId)
        {
            return await DbSet.Where(c => c.UsuarioAberturaId == usuarioId &&
                                          c.ContaFinanceira.LojaId == lojaId &&
                                          c.AgrupamentoIntegracaoId == agrupamentoIntegracaoId)
                               .Select(c => new CaixaMovimentacao
                               {
                                   Id = c.Id,
                                   AgrupamentoIntegracaoId = c.AgrupamentoIntegracaoId,
                                   ContaFinanceiraId = c.ContaFinanceiraId,
                                   ContaFinanceira = new ContaFinanceira
                                   {
                                       Id = c.ContaFinanceira.Id,
                                       Dispositivo = new Dispositivo
                                       {
                                           Id = c.ContaFinanceira.Dispositivo.Id
                                       }
                                   }
                               })
                               .FirstOrDefaultAsync();
        }
    }
}
