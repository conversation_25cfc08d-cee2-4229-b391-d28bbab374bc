import { GerarRelatorioEstoqueParametros } from 'api/Relatorio/GerarRelatorioEstoque';

import { FormData } from '../validationForms';

export const adaptarGerarRelatorioEstoque = (
  dados: FormData
): GerarRelatorioEstoqueParametros => {
  const listaCamposPersonalizados = dados.camposPersonalizados
    .filter((campoItem) => campoItem?.valor)
    .map((campoItem) => ({
      ...campoItem,
      valor: String(campoItem.valor),
    }));

  const camposPersonalizados =
    listaCamposPersonalizados.length > 0 ? listaCamposPersonalizados : null;
  const localEstoqueIds = dados?.localEstoqueIds || undefined;
  const produtoId = dados.produtoId?.value || undefined;

  return {
    ...dados,
    localEstoqueIds,
    produtoId,
    camposPersonalizados,
  };
};
