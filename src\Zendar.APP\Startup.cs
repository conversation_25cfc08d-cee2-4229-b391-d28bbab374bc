using MediatR;
using Microsoft.ApplicationInsights.AspNetCore.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Zendar.APP.Configurations;
using Zendar.APP.Middleware;
using Zendar.Business.API.ZendarSync.Handlers;
using Zendar.Business.Helpers;
using Zendar.Business.SignalR;

namespace Zendar.APP
{

    public class Startup
    {
        private readonly string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";
        private readonly IConfiguration _configuration;
        private readonly IWebHostEnvironment _hostEnvironment;

        public Startup(IWebHostEnvironment hostEnvironment)
        {
            _configuration = ConfigurationRootBuild.Get(hostEnvironment);
            _hostEnvironment = hostEnvironment;
        }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AutoMapperServiceConfig();
            services.AddMediatR(typeof(Startup));
            services.AddContextConfiguration();
            services.AddResolveDependencies();
            services.AddHttpClient();
            services.AddAuthenticationConfiguration(_configuration);
            services.AddAuthenticationTemporaryAccessConfiguration(_configuration);
            services.AddControllers();

            services.AddTransient<LoggingHttpRequestHandler>();
            services.AddTransient<SyncJwtHandler>();
            services.AddTransient<SyncApiKeyHandler>();

            services.AddSwaggerConfiguration();

            services.Configure<EmailSettings>(_configuration.GetSection(nameof(EmailSettings)));
            services.Configure<AzureDatabaseSettings>(_configuration.GetSection(nameof(AzureDatabaseSettings)));
            services.Configure<SmartPOSSettings>(_configuration.GetSection(nameof(SmartPOSSettings)));
            services.Configure<ZenflixSettings>(_configuration.GetSection(nameof(ZenflixSettings)));

            services.AddMemoryCache();
            services.AddCorsConfiguration(_hostEnvironment, MyAllowSpecificOrigins);

            services.AddHangfireConfig(_configuration, _hostEnvironment);
            services.AddSignalR();
            services.AddRedisConfig(_configuration, _hostEnvironment);

            services.AddHealthChecks();

            //#if !DEBUG
            //            //https://learn.microsoft.com/en-us/azure/azure-monitor/app/api-custom-events-metrics
            //            //https://learn.microsoft.com/pt-br/azure/azure-monitor/app/asp-net-core?tabs=netcoreold%2Cnetcore6#configure-the-application-insights-sdk
            ApplicationInsightsServiceOptions aiOptions = new()
            {
                // Disables adaptive sampling.
                EnableAdaptiveSampling = false,

                // Disables QuickPulse (Live Metrics stream).
                EnableQuickPulseMetricStream = false
            };

            services.AddApplicationInsightsTelemetry(aiOptions);
            //#endif
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment() || env.IsStaging())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwaggerUIConfiguration();
            }

            app.UseGlobalizationConfig();
            app.UseHttpsRedirection();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseCors(MyAllowSpecificOrigins);
            app.UseHangfireConfig();
            app.UseMiddleware<ApiKeyMiddleware>();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHub<NotificationHubService>("/notificationHub");
            });

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapHealthChecks("/health");
            });

            if (env.IsDevelopment())
            {
                //dbSeed.InicializarBancoDados().Wait();
            }
            else if (env.IsStaging())
            {
                //dbSeed.InicializarBancoDados().Wait();
            }
        }
    }
}
