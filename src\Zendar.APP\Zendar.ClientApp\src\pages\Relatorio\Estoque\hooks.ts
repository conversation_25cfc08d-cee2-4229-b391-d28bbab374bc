import jsFileDownload from 'js-file-download';
import { useCallback, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import { ImprimirPDF } from 'helpers/impressoes/imprimirPDF';

import { obterListaSelectLocalEstoque } from 'api/LocalEstoque/ObterListaSelect';
import { gerarRelatorioEstoque } from 'api/Relatorio/GerarRelatorioEstoque';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { enumRelatorioEstoque } from 'constants/enum/enumRelatorioEstoque';

import { adaptarGerarRelatorioEstoque } from './adapters';
import { validarTipoRelatorioEstoque } from './ValidarTipoRelatorioEstoque';
import { FormData } from './validationForms';

export const useRelatorioEstoque = (formMethods: UseFormReturn<FormData>) => {
  const [isLoading, setIsLoading] = useState(false);

  const { handleSubmit, watch } = formMethods;
  const { tipoRelatorio } = watch();
  const { id: lojaId } = auth.getLoja();

  const tipoEstoqueSemPreco =
    tipoRelatorio === enumRelatorioEstoque.POSICAO_ESTOQUE;
  const tipoEstoquePrecoCusto =
    tipoRelatorio === enumRelatorioEstoque.POSICAO_ESTOQUE_PRECO_CUSTO;
  const listaRelatoriosEstoque = enumRelatorioEstoque.properties.filter(
    (relatorio) => validarTipoRelatorioEstoque(relatorio)
  );

  const obterOpcoesLocaisDeEstoque = useCallback(async () => {
    const response = await obterListaSelectLocalEstoque({ lojaId });

    if (response?.avisos) {
      response?.avisos.forEach((aviso) => toast.warning(aviso));
    }

    if (response?.sucesso && response?.dados) {
      const opcoes = response.dados.map((local) => ({
        value: local.id,
        label: local.nome,
      }));

      return opcoes;
    }

    return [];
  }, [lojaId]);

  const obterEndpointRelatorio = (tipo: 'PDF' | 'CSV') => {
    const prefixo =
      tipo === 'CSV' ? 'RELATORIO_CSV_ESTOQUE' : 'RELATORIO_ESTOQUE';

    if (tipoEstoqueSemPreco)
      return ConstanteEnderecoWebservice[`${prefixo}_SEM_PRECO`];

    if (tipoEstoquePrecoCusto)
      return ConstanteEnderecoWebservice[`${prefixo}_PRECO_CUSTO`];

    return ConstanteEnderecoWebservice[`${prefixo}_PRECO_VENDA`];
  };

  const gerarArquivoRelatorio = (arquivoStr: string, tipo: 'PDF' | 'CSV') => {
    if (tipo === 'PDF') {
      ImprimirPDF(arquivoStr, 'relatorioProdutoEstoque');
      return;
    }

    jsFileDownload(
      Uint8Array.from(atob(arquivoStr), (c) => c.charCodeAt(0)),
      'Relatorio_estoque.csv'
    );
  };

  const handleGerarRelatorio = (tipo: 'PDF' | 'CSV') => {
    handleSubmit(async (data) => {
      setIsLoading(true);

      const endpoint = obterEndpointRelatorio(tipo);
      const relatorioAdaptado = adaptarGerarRelatorioEstoque(data);

      const response = await gerarRelatorioEstoque({
        endpoint,
        dados: relatorioAdaptado,
      });

      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso && response?.dados) {
        gerarArquivoRelatorio(response.dados, tipo);
      }

      setIsLoading(false);
    })();
  };

  return {
    isLoading,
    listaRelatoriosEstoque,
    obterOpcoesLocaisDeEstoque,
    handleGerarRelatorio,
  };
};
