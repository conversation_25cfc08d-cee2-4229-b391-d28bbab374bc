﻿using Multiempresa.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.ViewModels;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Mensagens;

namespace Zendar.Business.Services.Financeiro.CaixaMovimentacaoServices
{
    public class CaixaMovimentacaoService : BaseService, ICaixaMovimentacaoService
    {
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly ICaixaMovimentacaoRepository _caixaMovimentacaoRepository;
        private readonly ICaixaConferenciaRepository _caixaConferenciaRepository;

        public CaixaMovimentacaoService(INotificador notificador,
            IAspNetUserInfo aspNetUserInfo,
            ICaixaMovimentacaoRepository caixaMovimentacaoRepository,
            ICaixaConferenciaRepository caixaConferenciaRepository) : base(notificador)
        {
            _caixaMovimentacaoRepository = caixaMovimentacaoRepository;
            _aspNetUserInfo = aspNetUserInfo;
            _caixaConferenciaRepository = caixaConferenciaRepository;
        }

        public void Dispose()
        {
            _caixaMovimentacaoRepository?.Dispose();
            _caixaConferenciaRepository?.Dispose();
        }

        //CaixaV2Service -> ObterUltimoAbertoPorContaFinanceiraIdEUsuarioId
        public async Task<Guid?> ObterUltimoCaixaAbertoDeContaFinanceira(Guid? contaFinanceiraId, Guid usuarioId)
        {
            if (contaFinanceiraId.HasValue)
                return _caixaMovimentacaoRepository.FirstOrDefaultAsNoTracking(x => x.ContaFinanceiraId.Equals(contaFinanceiraId.Value)
                                                                            && !x.DataHoraFechamento.HasValue
                                                                            && x.ContaFinanceira.TipoContaFinanceira.Equals(TipoContaFinanceira.CAIXA)
                                                                            && x.UsuarioAberturaId.Equals(usuarioId)
                                                                           ).Result?.Id;

            return null;
        }

        //CaixaV2Service -> ObterIdPorUsuarioIdELojaId
        public async Task<Guid?> ObterIdCaixaMovimentacaoPorUsuario(Guid usuarioId, Guid? lojaId = null)
        {
            if (!lojaId.HasValue)
                lojaId = _aspNetUserInfo.LojaId;

            return (await _caixaMovimentacaoRepository.FirstOrDefaultAsNoTracking(x => x.ContaFinanceira.LojaId.Equals(lojaId.Value)
                                                                              && !x.DataHoraFechamento.HasValue
                                                                              && x.ContaFinanceira.TipoContaFinanceira.Equals(TipoContaFinanceira.CAIXA)
                                                                              && x.UsuarioAberturaId.Equals(usuarioId)
                                                                              && x.ContaFinanceira.Ativo
                                                                              && x.ContaFinanceira.Dispositivo == null
                                                                           ))?.Id;
        }

        //CaixaV2Service -> ObterListaIdAbertoPorUsuarioIdELojaId
        public async Task<List<Guid>> ObterListaIdCaixaAbertoUsuario(Guid usuarioId, Guid lojaId)
        {
            return await _caixaMovimentacaoRepository.ObterListaIdCaixaAbertoUsuario(usuarioId, lojaId);
        }

        //CaixaV2Service -> VincularAgrupamento
        public async Task VincularAgrupamentoIntegracaoAoCaixaMovimentacao(VincularAgrupamentoIntegracaoCaixaMovimentacaoViewModel viewModel)
        {
            CaixaMovimentacao caixaMovimentacao = null;

			// Obtém pelo Id do caixaMovimentaçao, pois SmartPOS pode ter mais de um caixa aberto.
			if (viewModel.CaixaMovimentacaoId.HasValue)
                caixaMovimentacao = await _caixaMovimentacaoRepository.ObterComOperacoesParaVincularAgrupamnetoIntegracaoId(viewModel.CaixaMovimentacaoId.Value);

            if (caixaMovimentacao == null)
            {
                NotificarAvisoRegistroNaoEncontrado("caixa movimentação");
                return;
            }

            caixaMovimentacao.AgrupamentoIntegracaoId = viewModel.AgrupamentoIntegracaoId;

            // Caso já tenha operações vinculadas e o caixa não foi fechado, mas o aplicativo enviou um novo agrupamentoIntegracaoId
            if (caixaMovimentacao.Operacoes != null && caixaMovimentacao.Operacoes.Any())
                caixaMovimentacao.Operacoes.ToList().ForEach(x =>
                {
                    x.AgrupamentoIntegracaoId = viewModel.AgrupamentoIntegracaoId;
                });

            await _caixaMovimentacaoRepository.SaveChanges();
        }

        //CaixaV2Service -> ExisteAbertoPorIdentificadorELojaId
        public async Task<bool> ExisteCaixaAbertoPorIdentificadorDispositivo(Guid lojaId, string identificador)
        {
            // Para smartpos, pode haver mais de um caixa aberto.
            return await _caixaMovimentacaoRepository.Any(x => x.ContaFinanceira.LojaId == lojaId &&
                                                               x.ContaFinanceira.Dispositivo.Identificador == identificador &&
                                                               x.UsuarioFechamentoId == null &&
                                                               x.DataHoraFechamento == null &&
                                                               x.ContaFinanceira.Dispositivo != null);
        }

        //CaixaV2Service -> ObterIdPorAgrupamentoIdELojaId
        public async Task<CaixaMovimentacao> ObterCaixaMovimentacaoPorAgrupamentoId(Guid lojaId, Guid agrupamentoIntegracaoId)
        {
            return await _caixaMovimentacaoRepository.FirstOrDefaultAsNoTracking(x => x.ContaFinanceira.LojaId == lojaId &&
                                                                                      x.AgrupamentoIntegracaoId == agrupamentoIntegracaoId);
        }

        //CaixaV2Service -> Excluir
        public async Task Excluir(Guid caixaMovimentacaoId, Guid usuarioId)
        {
            var caixaMovimentacao = await _caixaMovimentacaoRepository
                .FirstOrDefaultAsNoTracking(cm => cm.Id == caixaMovimentacaoId);

            if (caixaMovimentacao == null)
            {
                NotificarAvisoRegistroNaoEncontrado("caixa");
                return;
            }

            if (!caixaMovimentacao.UsuarioFechamentoId.HasValue)
            {
                NotificarAviso(ResourceMensagem.CaixaMovientacaoService_CaixaEstaAberto);
                return;
            }

            if (caixaMovimentacao.UsuarioAberturaId != usuarioId)
            {
                NotificarAviso(ResourceMensagem.CaixaMovientacaoService_CaixaPertenceOutroUsuario);
                return;
            }

            var ultimoCaixaMovimentacao = await _caixaMovimentacaoRepository
                .ObterUltimoCaixaPorContaFinanceira(caixaMovimentacao.ContaFinanceiraId);
            if (ultimoCaixaMovimentacao != null && ultimoCaixaMovimentacao.Id != caixaMovimentacao.Id)
            {
                NotificarAviso(ResourceMensagem.CaixaNaoEhUltimoAberto);
                return;
            }

            bool possuiOperacoesOuBaixas = await _caixaMovimentacaoRepository
                .Any(cm => cm.Id == caixaMovimentacaoId &&
                        (cm.MovimentacoesFinanceirasBaixa.Any() || cm.Operacoes.Any()));
            if (possuiOperacoesOuBaixas)
            {
                NotificarAviso(ResourceMensagem.CaixaMovimentacaoService_CaixaComBaixaOuOperacao);
                return;
            }

            await _caixaConferenciaRepository.LimparConferenciaReabrirCaixa(caixaMovimentacaoId);

            await _caixaMovimentacaoRepository.Delete(caixaMovimentacaoId);
        }

        //CaixaV2Service -> CreditarValor
        public async Task CreditarValorCaixa(Guid caixaMovimentacaoId, IEnumerable<(Guid FormaRecebimentoId, decimal Valor)> formasRecebimento)
        {
            var caixaMovimentacao = await _caixaMovimentacaoRepository.ObterComConferencias(caixaMovimentacaoId);

            if (caixaMovimentacao == null)
            {
                NotificarAvisoRegistroNaoEncontrado("caixa");
                return;
            }

            caixaMovimentacao.SaldoFechamento += formasRecebimento.Sum(i => i.Valor);

            if (caixaMovimentacao.CaixaConferencias != null &&
                caixaMovimentacao.CaixaConferencias.Any())
            {
                foreach (var formaRecebimento in formasRecebimento)
                {
                    var caixaConferencia = caixaMovimentacao.CaixaConferencias
                        .FirstOrDefault(cf => cf.FormaPagamentoId == formaRecebimento.FormaRecebimentoId);

                    if (caixaConferencia == null) continue;

                    caixaConferencia.Creditar(formaRecebimento.Valor);
                }
            }

            await _caixaMovimentacaoRepository.SaveChanges();
        }

        //CaixaV2Service -> DebitarValor
        public async Task DebitarValorCaixa(Guid caixaMovimentacaoId, IEnumerable<(Guid FormaRecebimentoId, decimal Valor)> formasRecebimento)
        {
            var caixaMovimentacao = await _caixaMovimentacaoRepository.ObterComConferencias(caixaMovimentacaoId);

            if (caixaMovimentacao == null)
            {
                NotificarAvisoRegistroNaoEncontrado("caixa");
                return;
            }

            caixaMovimentacao.SaldoFechamento -= formasRecebimento.Sum(i => i.Valor);

            if (caixaMovimentacao.CaixaConferencias != null &&
                caixaMovimentacao.CaixaConferencias.Any())
            {
                foreach (var formaRecebimento in formasRecebimento)
                {
                    var caixaConferencia = caixaMovimentacao.CaixaConferencias
                        .FirstOrDefault(cf => cf.FormaPagamentoId == formaRecebimento.FormaRecebimentoId);

                    if (caixaConferencia == null) continue;

                    caixaConferencia.Debitar(formaRecebimento.Valor);
                }
            }

            await _caixaMovimentacaoRepository.SaveChanges();
        }
    }
}
