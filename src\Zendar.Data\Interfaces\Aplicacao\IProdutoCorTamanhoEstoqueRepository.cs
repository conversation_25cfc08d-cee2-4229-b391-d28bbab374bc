﻿using Multiempresa.Shared.Helpers;
using Multiempresa.Shared.ViewModels.FiltroViewModels;
using Multiempresa.Shared.ViewModels.InventarioViewModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.Data.Helpers;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.ViewModels;

namespace Zendar.Data.Interfaces.Aplicacao
{
    public interface IProdutoCorTamanhoEstoqueRepository : IRepository<ProdutoCorTamanhoEstoque>
    {
        GridPaginadaRetorno<ProdutoCorTamanho> ObterItensConferenciaPaginadaPorLocalEstoque(GridPaginadaConsulta gridPaginada, Guid localEstoqueId, ICollection<ConferenciaEstoqueFiltros> filtros = null);
        Task<List<Guid>> ObterIdPorLocalEstoque(Guid localEstoqueId, ICollection<ConferenciaEstoqueFiltros> filtros = null);
        Task<decimal> ObterSaldoProduto(Guid produtoId, Guid lojaId);
		List<ProdutoVariacaoSaldoViewModel> ObterSaldoVariacoes(Guid produtoId, Guid? corId);
        decimal ObterSaldoTotalProduto(Guid produtoId, Guid lojaLogada);
        List<ProdutoCorTamanhoEstoque> ObterSaldosProdutoCorTamanho(ICollection<Guid> produtoCorTamanhoId, Guid localEstoqueId);
        Task<decimal> ObterSaldoProdutoEstoque(Guid produtoCorTamanhoId, Guid localEstoqueId);
        List<InventarioViewModel> RelatorioListagemInventario(InventarioFiltrosViewModel inventarioFiltrosViewModel);
        Task<List<ProdutoCorTamanhoEstoque>> ObterRelatoriosEstoque(RelatorioEstoqueFiltrosViewModel filtrosEstoqueViewModel, Guid lojaId);
        Task<decimal> ObterSaldoProdutoPorLoja(Guid lojaId, Guid produtoId);
        Task<List<ProdutoCorTamanhoEstoque>> ObterProdutosIdQuantidadePorEstoqueId(Guid localEstoqueId);
    }
}
