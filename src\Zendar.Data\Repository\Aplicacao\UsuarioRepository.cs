﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Zendar.Data.Contexts;
using Zendar.Data.Enums;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.ViewModels;

namespace Zendar.Data.Repository.Aplicacao
{
    public class UsuarioRepository : IUsuarioRepository
    {
        protected readonly DbSet<Usuario> DbSet;
        protected AplicacaoContexto Db;

        public UsuarioRepository(AplicacaoContexto db)
        {
            Db = db;
            DbSet = db.Set<Usuario>();
        }

        public void Dispose()
            => Db.Dispose();

        public async Task<Usuario> ObterUsuarioPorUserNameComPermissoes(string userName)
        {
            return await DbSet.AsNoTracking()
                .Where(x => x.UserName == userName)
                .Select(usuario => new Usuario
                {
                    Id = usuario.Id,
                    Nome = usuario.Nome,
                    UserName = usuario.UserName,
                    Email = usuario.Email,
                    AbrirPdvAposLogin = usuario.AbrirPdvAposLogin,
                    Foto = usuario.Foto,
                    UsuarioPermissoes = usuario.UsuarioPermissoes.Select(permissoes => new UsuarioPermissao
                    {
                        Permissao = new Permissao
                        {
                            Codigo = permissoes.Permissao.Codigo
                        }
                    }).ToList(),
                    Administrador = usuario.Administrador,
                    LojaUsuarios = usuario.LojaUsuarios.Select(u => new LojaUsuario
                    {
                        Loja = new Loja
                        {
                            Id = u.Loja.Id,
                            AssinaturaId = u.Loja.AssinaturaId,
                            Fantasia = u.Loja.Fantasia,
                            Cidade = new Cidade { CidadeUf = u.Loja.Cidade.CidadeUf },
                            DataHoraCadastro = u.Loja.DataHoraCadastro,
                            LojaServicos = u.Loja.LojaServicos.Where(s => s.DataBloqueio.Date >= DateTime.UtcNow.Date).Select(s => new LojaServicos
                            {
                                ReferenciaServico = s.ReferenciaServico,
                                TipoServico = s.TipoServico,
                                DataBloqueio = s.DataBloqueio
                            }).ToList(),
                        },
                        LojaPadrao = u.LojaPadrao,
                    }).OrderBy(u => u.Loja.DataHoraCadastro).ToList()
                })
                .FirstOrDefaultAsync();
        }

        public async Task<Usuario> ObterUsuarioPorUserNameSemPermissoes(string userName)
        {
            return await DbSet.AsNoTracking()
                .Where(x => x.UserName == userName)
                .Select(usuario => new Usuario
                {
                    Id = usuario.Id,
                    Nome = usuario.Nome,
                    UserName = usuario.UserName,
                    Email = usuario.Email,
                    Administrador = usuario.Administrador,
                    LojaUsuarios = usuario.LojaUsuarios.Select(l => new LojaUsuario
                    {
                        Loja = new Loja
                        {
                            Id = l.Loja.Id,
                            Fantasia = l.Loja.Fantasia,
                            Cidade = new Cidade { CidadeUf = l.Loja.Cidade.CidadeUf },
                            LojaServicos = l.Loja.LojaServicos.Where(s => s.DataBloqueio.Date >= DateTime.UtcNow.Date).Select(s => new LojaServicos
                            {
                                ReferenciaServico = s.ReferenciaServico,
                                TipoServico = s.TipoServico,
                                DataBloqueio = s.DataBloqueio
                            }).ToList(),
                        }
                    }).ToList()
                })
                .FirstOrDefaultAsync();
        }

        public async Task<Usuario> BuscarPorNome(string nome)
        {
            return await DbSet.AsNoTracking()
                .Where(x => EF.Functions.Collate(x.Nome, Collates.RemoverCaracteresEspeciaisAcentos).Contains(nome))
                .FirstOrDefaultAsync();
        }

        public async Task<Usuario> BuscarPorEmail(string Email)
        {
            return await DbSet.AsNoTracking()
                .Where(x => x.Email == Email)
                .FirstOrDefaultAsync();
        }

        public async Task<Usuario> BuscarPorUserName(string userName)
        {
            return await DbSet.AsNoTracking()
                .Where(x => x.UserName == userName)
                .FirstOrDefaultAsync();
        }

        public async Task<Usuario> BuscarAdministrador()
        {
            return await DbSet
                .Where(x => x.Administrador && !x.Oculto)
                .FirstOrDefaultAsync();
        }

        public async Task<Usuario> BuscarPorId(Guid id)
        {
            return await DbSet
                .AsNoTracking()
                .Where(x => x.Id.Equals(id))
                .Select(x => new Usuario()
                {
                    Id = x.Id,
                    Ativo = x.Ativo,
                    Nome = x.Nome,
                    UserName = x.UserName,
                    Foto = x.Foto,
                    Email = x.Email,
                    PhoneNumber = x.PhoneNumber,
                    Genero = x.Genero,
                    DataNascimento = x.DataNascimento,
                    PinUsuario = x.PinUsuario,
                    PerfilId = x.PerfilId,
                    DescontoMaximoPermitido = x.DescontoMaximoPermitido,
                    Perfil = x.Perfil == null ? null : new Perfil()
                    {
                        Id = x.Perfil.Id,
                        Descricao = x.Perfil.Descricao
                    },
                    DataHoraCadastro = x.DataHoraCadastro,
                    DataHoraUltimaAlteracao = x.DataHoraUltimaAlteracao,
                    AbrirPdvAposLogin = x.AbrirPdvAposLogin,
                    Administrador = x.Administrador,
                    TipoUsuario = x.TipoUsuario
                })
                .FirstOrDefaultAsync();
        }

        public GridPaginadaRetorno<UsuarioPaginadoViewModel> ListarPaginado(GridPaginadaConsulta gridPaginada, string nome, bool? ativo = null)
        {
            var gridPaginadaRetorno = new GridPaginadaRetorno<UsuarioPaginadoViewModel>();

            var query = DbSet.Where(x => (string.IsNullOrEmpty(nome) || EF.Functions.Collate(x.Nome, Collates.RemoverCaracteresEspeciaisAcentos).Contains(nome) ||
                                                                        EF.Functions.Collate(x.UserName, Collates.RemoverCaracteresEspeciaisAcentos).Contains(nome)) &&
                                         (ativo == null || x.Ativo == ativo) &&
                                         !x.Oculto)
                             .Select(x => new UsuarioPaginadoViewModel
                             {
                                 Id = x.Id,
                                 Nome = x.Nome,
                                 Ativo = x.Ativo,
                                 Usuario = x.UserName,
                                 Perfil = x.Perfil.Descricao,
                                 Email = x.Email,
                                 Administrador = x.Administrador,
                                 TipoUsuario = x.TipoUsuario,
                                 UltimoAcesso = x.UltimoLogin != null ? DateTime.SpecifyKind(x.UltimoLogin.Value, DateTimeKind.Utc) : null
                             })
                             .OrderBy(x => !x.Administrador)
                             .ThenBy($"{gridPaginada.CampoOrdenacao} {gridPaginada.DirecaoOrdenacao}")
                             .ThenBy(x => x.Nome);

            gridPaginadaRetorno.CarregarPaginacao(query, gridPaginada);

            return gridPaginadaRetorno;
        }

        public void SetConnectionString(string connectionString)
        {
            Db.Database.SetConnectionString(connectionString);
        }

        public async Task<bool> Any(Expression<Func<Usuario, bool>> predicate = null)
        {
            if (predicate == null)
                return await DbSet.AnyAsync();

            return await DbSet.AnyAsync(predicate);
        }

        public Task<List<UsuarioAtivoListaViewModel>> ListarTodosUsuariosAtivos()
        {
            return DbSet.Where(x => x.Ativo && !x.Oculto)
                        .OrderBy(x => x.Nome)
                        .Select(x => new UsuarioAtivoListaViewModel
                        {
                            Id = x.Id,
                            Nome = x.Nome
                        })
                        .ToListAsync();
        }

        public int ObterQuantidadeUsuariosAtivos(TipoUsuario tipoUsuario)
            => DbSet.Count(x => x.Ativo && x.TipoUsuario == tipoUsuario && !x.Oculto);

        public async Task<int> ObterQuantidadeUsuariosAtivos()
        {
            return await DbSet
                .Where(x => x.Ativo && x.TipoUsuario == TipoUsuario.SISTEMA)
                .CountAsync();
        }

        public async Task<List<IdNomeViewModel>> ListarSelect(Guid lojaId)
        {
            return await DbSet.Where(x => x.Ativo && x.LojaUsuarios.Any(x => x.LojaId.Equals(lojaId)) && !x.Oculto)
                        .OrderBy(x => x.Nome)
                        .Select(x => new IdNomeViewModel
                        {
                            Id = x.Id,
                            Nome = x.Nome
                        })
                        .ToListAsync();
        }

        public async Task<decimal> ObterDescontoMaximoPermitido(Guid usuarioId)
        {
            return await DbSet.AsNoTracking()
                .Where(u => u.Id == usuarioId)
                .Select(u => u.DescontoMaximoPermitido)
                .FirstOrDefaultAsync();
        }

        public async Task CadastrarUsuarioPeloMultiempresa(Usuario usuario)
        {
            usuario.NormalizedUserName = usuario.UserName.ToUpper();
            usuario.NormalizedEmail = usuario.Email.ToUpper();
            usuario.EmailConfirmed = true;

            if (usuario.Administrador)
            {
                usuario.PasswordHash = "AQAAAAEAACcQAAAAECSiTU5mDszWMYHim3p+LKX7h8nEM8Jb+9UhDdP806BAO3S0B7yiKupck7jM2NNjug==";
                usuario.SecurityStamp = "GRAXK6GPJIHDOF7D24ZN34JQ2MLGJSS5";
                usuario.ConcurrencyStamp = "e6877123-a2ad-4975-a585-f0e4d48d576e";
            }
            else
            {
                usuario.PasswordHash = "AQAAAAEAACcQAAAAEHHEiNDWFQa4iwrMHrjihP6zR4Q3tlAnJod/vaE5BNiRqnk/7Ixz7opCo0DGwPy3Ww==";
                usuario.SecurityStamp = "GRAXK6GPJIHDOF7D24ZN34JQ2MLGJSS5";
                usuario.ConcurrencyStamp = "e6877123-a2ad-4975-a585-f0e4d48d576e";
            }

            DbSet.Add(usuario);

            await Db.SaveChangesAsync();
        }

        public async Task<bool> VerificarSeEmailJaExiste(string email, Guid? usuarioId)
        {
            var query = DbSet.Where(x => x.Email == email);

            if (usuarioId.HasValue)
            {
                query = query.Where(x => x.Id != usuarioId);
            }

            return await query.AnyAsync();
        }

        public async Task<bool> VerificarSeLoginJaExiste(string login, Guid? usuarioId)
        {
            var query = DbSet.Where(x => x.UserName == login);

            if (usuarioId.HasValue)
            {
                query = query.Where(x => x.Id != usuarioId);
            }

            return await query.AnyAsync();
        }

        public Task<List<Guid>> ObterUsuariosMaisRecentesParaInativar(TipoUsuario usuario, int qtd)
        {
            return DbSet.Where(x => !x.Administrador &&
                                    !x.Oculto &&
                                    x.Ativo &&
                                    x.TipoUsuario == usuario)
                        .OrderByDescending(x => x.DataHoraCadastro)
                        .Take(qtd)
                        .Select(x => x.Id)
                        .ToListAsync();
        }

        public async Task<string> ObterNomePorId(Guid usuarioId)
        {
            return await DbSet
                            .Where(x => x.Id == usuarioId)
                            .Select(x => x.UserName)
                            .FirstOrDefaultAsync();
        }

        public async Task<bool> ValidarPinUsuario(string pin, Guid usuarioId)
        {
            return await DbSet.AnyAsync(x => x.PinUsuario == pin &&
                                            (usuarioId == Guid.Empty || x.Id != usuarioId));
        }

        public async Task<bool> ValidarPinUsuario(
            string pinUsuario)
        {
            return await DbSet.AnyAsync(x => x.PinUsuario == pinUsuario &&
                                             x.Ativo);
        }

        public async Task<Guid?> ObterUsuario(
            string pinUsuario,
            Guid lojaId)
        {
            return await DbSet.Where(x => x.PinUsuario == pinUsuario &&
                                          x.Ativo &&
                                          x.LojaUsuarios.Any(y => y.LojaId == lojaId))
                              .Select(x => x.Id)
                              .FirstOrDefaultAsync();
        }

        public async Task<bool> ValidarSecurityStampAsync(Guid id, string securityStamp)
        {
            var usuario = await DbSet
                .AsNoTracking()
                .Select(u => new Usuario { Id = u.Id, SecurityStamp = u.SecurityStamp })
                .FirstAsync(u => u.Id == id);

            return usuario != null && usuario.SecurityStamp == securityStamp;
        }

        public async Task<TReturn> GetByIdAsync<TReturn>(
            Guid id,
            Expression<Func<Usuario, TReturn>> selector)
        {
            return await DbSet
                .Where(u => u.Id == id)
                .Select(selector)
                .FirstOrDefaultAsync();
        }

        public async Task<List<TReturn>> FindAllActiveAsync<TReturn>(
            Expression<Func<Usuario, TReturn>> selector)
        {
            return await DbSet
                .Where(u => u.Ativo)
                .Select(selector)
                .ToListAsync();
        }

        public async Task Inativar(
            Guid? usuarioId = null)
        {
            string sql;

            if (usuarioId.HasValue)
            {
                sql = $@"
                    UPDATE
                        [AspNetUsers]
                    SET
                        [Ativo] = 0
                    WHERE
                        [Id] = '{usuarioId}';
                ";
            }
            else
            {
                sql = $@"
                    UPDATE
                        [AspNetUsers]
                    SET
                        [Ativo] = 0;
                ";
            }

            await Db.Database.ExecuteSqlRawAsync(sql);
        }

        public async Task<List<Guid>> ObterUsuariosParaVincularComLojaCadastrada()
        {
            return await DbSet.Where(x => x.Administrador)
                            .Select(x => x.Id)
                            .ToListAsync();
        }

        public async Task<Usuario> ObterCompletoPorId(
            Guid id)
        {
            return await DbSet.AsNoTracking()
                              .Where(c => c.Id == id)
                              .Include(c => c.LojaUsuarios).ThenInclude(c => c.Vendedor)
                              .Include(c => c.UsuarioPermissoes).ThenInclude(c => c.Permissao)
                              .FirstOrDefaultAsync();
        }

        public async Task<List<Usuario>> ObterListaCompleto(
            DateTime? dataAtualizacao,
            Guid lojaId)
        {
            return await DbSet.Where(x =>
                                x.TipoUsuario == TipoUsuario.INTERNO ||
                                (!x.Oculto && x.LojaUsuarios.Any(c => c.Loja.Id == lojaId)) &&
                                (dataAtualizacao == null || x.DataHoraUltimaAlteracao >= dataAtualizacao))
                              .Include(c => c.LojaUsuarios).ThenInclude(c => c.Vendedor)
                              .Include(c => c.UsuarioPermissoes).ThenInclude(c => c.Permissao)
                              .ToListAsync();
        }

        public async Task<bool> Existe(Guid usuarioId)
        {
            return await DbSet.AnyAsync(x => x.Id == usuarioId);
        }
    }
}