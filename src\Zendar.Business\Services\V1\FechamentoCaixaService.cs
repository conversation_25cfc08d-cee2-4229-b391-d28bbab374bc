﻿using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Configuration;
using Multiempresa.Shared.Helpers.Convertores;
using Refit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Zendar.Business.API.ZendarSync.Clients;
using Zendar.Business.API.ZendarSync.Handlers;
using Zendar.Business.API.ZendarSync.Services;
using Zendar.Business.Consts;
using Zendar.Business.Helpers.ImpressaoRelatorioPdf;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.Financeiro.ContaFinanceiraServices.ContaFinanceiraSaldoServices;
using Zendar.Business.Services.OperacaoServices.OperacaoTransferenciasDinheiroServices;
using Zendar.Business.SignalR;
using Zendar.Business.ViewModels;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services
{
    public class FechamentoCaixaService : BaseService, IFechamentoCaixaService
    {
        private readonly IHubContext<NotificationHubService> _hubContext;
        private readonly ICaixaService _caixaService;
        private readonly ICaixaMovimentacaoRepository _caixaMovimentacaoRepository;
        private readonly ILojaRepository _lojaRepository;
        private readonly IContaFinanceiraRepository _contaFinanceiraRepository;
        private readonly ILogAuditoriaService _logAuditoriaService;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IEnvioEmailService _envioEmailService;
        private readonly IContaFinanceiraSaldoService _contaFinanceiraSaldoService;
        private readonly IFormaPagamentoService _formaPagamentoService;
        private readonly IOperacaoTransferenciasDinheiroService _transferenciaDinheiroService;

        public FechamentoCaixaService(
            IConfiguration configuration,
            INotificador notificador,
            ICaixaService caixaService,
            IContaFinanceiraRepository contaFinanceiraRepository,
            ILojaRepository lojaRepository,
            ILogAuditoriaService logAuditoriaService,
            IAspNetUserInfo aspNetUserInfo,
            IHubContext<NotificationHubService> hubContext,
            ICaixaMovimentacaoRepository caixaMovimentacaoRepository,
            IEnvioEmailService envioEmailService,
            IContaFinanceiraSaldoService contaFinanceiraSaldoService,
            IFormaPagamentoService formaPagamentoService,
            IOperacaoTransferenciasDinheiroService transferenciaDinheiroService) : base(notificador)
        {
            _caixaService = caixaService;
            _contaFinanceiraRepository = contaFinanceiraRepository;
            _caixaMovimentacaoRepository = caixaMovimentacaoRepository;
            _logAuditoriaService = logAuditoriaService;
            _aspNetUserInfo = aspNetUserInfo;
            _lojaRepository = lojaRepository;
            _hubContext = hubContext;
            _envioEmailService = envioEmailService;
            _contaFinanceiraSaldoService = contaFinanceiraSaldoService;
            _formaPagamentoService = formaPagamentoService;
            _transferenciaDinheiroService = transferenciaDinheiroService;
        }

        public async Task<CaixaViewModel> ObterParaImprimir(CaixaMovimentacaoObterParaImprimirViewModel caixaMovimentacaoObterParaImprimirViewModel)
        {
            caixaMovimentacaoObterParaImprimirViewModel.ListarMovimentacoes = caixaMovimentacaoObterParaImprimirViewModel.ListarMovimentacoes.HasValue ? caixaMovimentacaoObterParaImprimirViewModel.ListarMovimentacoes.Value : false;

            var retorno = new CaixaViewModel();

            // Verificar se é nulo
            if (!await _caixaMovimentacaoRepository.Any(c => c.Id.Equals(caixaMovimentacaoObterParaImprimirViewModel.CaixaMovimentacaoId)))
            {
                retorno = await _caixaMovimentacaoRepository.ObterFechamentoCaixaSimples(caixaMovimentacaoObterParaImprimirViewModel.CaixaMovimentacaoId);

                ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, retorno);

                retorno.DataHoraUsuarioAbertura = $"{retorno.DataHoraAbertura} - {retorno.UsuarioAbertura}";
                retorno.DataHoraUsuarioFechamento = $"{retorno.DataHoraFechamento} - {retorno.UsuarioFechamento}";

                return retorno;
            }
            else
                retorno = await _caixaService.ObterCaixa(caixaMovimentacaoObterParaImprimirViewModel.CaixaMovimentacaoId, caixaMovimentacaoObterParaImprimirViewModel.ListarMovimentacoes.Value);

            return retorno;
        }

        public async Task Fechar(FechamentoCaixaFecharViewModel fechamentoCaixaFecharViewModel, bool pdv = true)
        {
            var caixaMovimentacao = await _caixaMovimentacaoRepository.ObterParaFecharCaixa(fechamentoCaixaFecharViewModel.IdCaixaMovimentacao);

            if (caixaMovimentacao == null)
            {
                NotificarAviso(ResourceMensagem.FechamentoCaixaService_NaoEncontrada);
                return;
            }

            if (caixaMovimentacao.FoiFechado())
            {
                NotificarAviso(ResourceMensagem.FechamentoCaixaService_JaEstaFechado);
                return;
            }

            if (pdv && caixaMovimentacao.IsCaixaDispositivo(ReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA))
            {
                NotificarAviso(
                    string.Format(ResourceMensagem.FechamentoCaixaService_FecharCaixaDispositivo,
                                  ReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA.ObterDescricao()));
                return;
            }

            caixaMovimentacao.UsuarioFechamentoId = fechamentoCaixaFecharViewModel.UsuarioId ?? Guid.Parse(_aspNetUserInfo.Id);
            caixaMovimentacao.DataHoraFechamento = DateTime.UtcNow;
            caixaMovimentacao.SaldoFechamento = caixaMovimentacao.ContaFinanceira.Saldo;

            caixaMovimentacao.CaixaConferencias = fechamentoCaixaFecharViewModel.Pagamentos
                .Select(x => new Data.Models.Aplicacao.CaixaConferencia
                {
                    CaixaMovimentacaoId = fechamentoCaixaFecharViewModel.IdCaixaMovimentacao,
                    FormaPagamentoId = x.FormaPagamentoRecebimentoId,
                    ValorInformado = x.ValorInformado,
                    ValorTotalFormaPagamento = x.ValorTotalFormaPagamento
                }).ToList();

            await _caixaMovimentacaoRepository.SaveChanges();

            if (caixaMovimentacao.ContaFinanceira.Dispositivo == null)
                await AtualizarSaldoDinheiroCheque(fechamentoCaixaFecharViewModel.IdCaixaMovimentacao);

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel(fechamentoCaixaFecharViewModel.Tela, LogAuditoriaOperacao.ALTERAR, $"Nome: {caixaMovimentacao.ContaFinanceira.Nome}, Aberto > Fechado"));

            if (pdv)
                await DispararMensagemCaixaFechadoSignalR(caixaMovimentacao.UsuarioAberturaId);
        }

        private async Task AtualizarSaldoDinheiroCheque(Guid caixaMovimentacaoId)
        {
            var conferencias = await _caixaMovimentacaoRepository.ObterConferenciaDinheiroCheque(caixaMovimentacaoId);

            if (conferencias == null || !conferencias.CaixaConferencias.Any())
                return;

            foreach (var conferencia in conferencias.CaixaConferencias)
            {
                await _contaFinanceiraSaldoService.AtualizarSaldo(conferencias.ContaFinanceiraId, conferencia.FormaPagamentoId, conferencia.ValorTotalFormaPagamento);
            }
        }

        private async Task DispararMensagemCaixaFechadoSignalR(Guid usuarioId)
        {
            await _hubContext.Clients.Group($"{usuarioId}_caixa-fechado").SendAsync("caixa-fechado");
        }

        public async Task<byte[]> ImpressaoA4(CaixaMovimentacaoObterParaImprimirViewModel caixaMovimentacaoObterParaImprimirViewModel)
        {
            var dados = await ObterParaImprimir(caixaMovimentacaoObterParaImprimirViewModel);

            var nomeLoja = await _lojaRepository.FirstOrDefault(x => x.Id.Equals(_aspNetUserInfo.LojaId.Value), x => new Loja
            {
                Fantasia = x.Fantasia,
            });

            return new ImpressaoFechamentoCaixa(dados, nomeLoja.Fantasia, DateTime.UtcNow.AddHours(_aspNetUserInfo.TimezoneOffset.Value), _aspNetUserInfo.Sistema).ToArray();
        }

        public async Task EnviarEmail(CaixaMovimentacaoObterParaImprimirViewModel caixaMovimentacaoObterParaImprimirViewModel, List<string> listaEmail)
        {
            var dados = await ObterParaImprimir(caixaMovimentacaoObterParaImprimirViewModel);

            var nomeLoja = await _lojaRepository.FirstOrDefault(x => x.Id.Equals(_aspNetUserInfo.LojaId.Value), x => new Loja
            {
                Fantasia = x.Fantasia,
            });

            _envioEmailService.EnviarEmailComprovante(new ImpressaoFechamentoCaixa(dados, nomeLoja.Fantasia, DateTime.UtcNow.AddHours(_aspNetUserInfo.TimezoneOffset.Value), _aspNetUserInfo.Sistema).ToArray(),
                                                      "FechamentoCaixa",
                                                      $"fechamento do {dados.ContaFinanceiraNome}",
                                                      "Fechamento do caixa",
                                                      listaEmail,
                                                      nomeLoja.Fantasia);
        }

        public void Dispose()
        {
            _contaFinanceiraRepository.Dispose();
            _lojaRepository.Dispose();
            _caixaMovimentacaoRepository.Dispose();
            _contaFinanceiraSaldoService.Dispose();
            _formaPagamentoService.Dispose();
            _transferenciaDinheiroService.Dispose();
        }

        public async Task<Guid?> FecharCaixaPDVExterno(AbrirFecharCaixaDispositivoViewModel abrirFecharCaixaDispositivoViewModel)
        {
            var caixaMovimentacao =
                await _caixaMovimentacaoRepository.ObterMovimentacaoDispositivo(
                    abrirFecharCaixaDispositivoViewModel.Identificador,
                    abrirFecharCaixaDispositivoViewModel.LojaId,
                    Guid.Parse(abrirFecharCaixaDispositivoViewModel.UsuarioId),
				    abrirFecharCaixaDispositivoViewModel.CaixaMovimentacaoId,
                    abrirFecharCaixaDispositivoViewModel.AgrupamentoIntegracaoId);

            if (caixaMovimentacao == null || caixaMovimentacao.Id == Guid.Empty)
            {
                return null;
            }

            var caixaConferencias = await _caixaService.ObterConferenciaCaixa(caixaMovimentacao.Id, new() { SaldoInicial = abrirFecharCaixaDispositivoViewModel.SaldoInicial });

            var fecharCaixaViewModel = new FechamentoCaixaFecharViewModel()
            {
                Tela = LogAuditoriaTela.INTEGRACAO,
                IdCaixaMovimentacao = caixaMovimentacao.Id,
                UsuarioId = Guid.Parse(abrirFecharCaixaDispositivoViewModel.UsuarioId),
                Pagamentos = caixaConferencias.CaixaConferencias.Select(x => new FechamentoCaixaPagamentosViewModel
                {
                    FormaPagamentoRecebimentoId = x.FormaRecebimentoId,
                    ValorInformado = x.Valor,
                    ValorTotalFormaPagamento = x.Valor
                }).ToList()
            };

            var dinheiro = await _formaPagamentoService.ObterFormaRecebimentoDinheiro();

            var saldoDinheiro = caixaConferencias.CaixaConferencias.Where(x => x.FormaRecebimentoId == dinheiro.Id.Value).Sum(x => x.Valor);

            var contaCofreId = await _contaFinanceiraRepository.ObterContaCofrePorLoja(abrirFecharCaixaDispositivoViewModel.LojaId);

            var transferencia = new OperacaoTransferenciaViewModel
            {
                UsuarioId = Guid.Parse(abrirFecharCaixaDispositivoViewModel.UsuarioId),
                DataEmissao = DateTime.UtcNow,
                DataHoraUsuario = DateTime.UtcNow.AddHours(_aspNetUserInfo.TimezoneOffset.Value),
                FormaPagamentoRecebimentoId = dinheiro.Id.Value,
                Valor = Math.Abs(saldoDinheiro),
                LogAuditoriaTela = Zendar.Data.Enums.LogAuditoriaTela.INTEGRACAO
            };

            // Realizar sangria
            if (saldoDinheiro > 0)
            {
                transferencia.AgrupamentoIntegracaoSaidaId = caixaMovimentacao.AgrupamentoIntegracaoId;
                transferencia.CaixaMovimentacaoSaidaId = caixaMovimentacao.Id;
                transferencia.ContaFinanceiraIdSaida = caixaMovimentacao.ContaFinanceira.Id;
                transferencia.ContaFinanceiraIdEntrada = contaCofreId;
                transferencia.Observacao = "Sangria para o fechamento do caixa.";
            }
            // Reaizar suprimento
            else if (saldoDinheiro < 0)
            {
                transferencia.AgrupamentoIntegracaoEntradaId = caixaMovimentacao.AgrupamentoIntegracaoId;
                transferencia.CaixaMovimentacaoEntradaId = caixaMovimentacao.Id;
                transferencia.ContaFinanceiraIdEntrada = caixaMovimentacao.ContaFinanceira.Id;
                transferencia.ContaFinanceiraIdSaida = contaCofreId;
                transferencia.Observacao = "Suprimento para o fechamento do caixa.";
            }

            await _transferenciaDinheiroService.Transferencia(transferencia,
                                                              true,
                                                              true);

            await Fechar(fecharCaixaViewModel, false);

            return caixaMovimentacao.Id;
        }
    }
}
