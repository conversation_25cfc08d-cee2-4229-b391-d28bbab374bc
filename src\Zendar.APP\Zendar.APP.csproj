﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net5.0</TargetFramework>
		<UserSecretsId>2f23e484-5271-44d4-9199-f438b3590c7d</UserSecretsId>
		<TypeScriptCompileBlocked>true</TypeScriptCompileBlocked>
		<Version Condition=" '$(version_number)' == '' ">*******</Version>
		<Version Condition=" '$(version_number)' != '' ">1.$(version_number)</Version>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Api\Requests\**" />
	  <Compile Remove="Api\V2\Produto\ProdutoFiscais\**" />
	  <Compile Remove="Api\V2\Produto\ProdutoInformacoesAdicionais\**" />
	  <Content Remove="Api\Requests\**" />
	  <Content Remove="Api\V2\Produto\ProdutoFiscais\**" />
	  <Content Remove="Api\V2\Produto\ProdutoInformacoesAdicionais\**" />
	  <EmbeddedResource Remove="Api\Requests\**" />
	  <EmbeddedResource Remove="Api\V2\Produto\ProdutoFiscais\**" />
	  <EmbeddedResource Remove="Api\V2\Produto\ProdutoInformacoesAdicionais\**" />
	  <None Remove="Api\Requests\**" />
	  <None Remove="Api\V2\Produto\ProdutoFiscais\**" />
	  <None Remove="Api\V2\Produto\ProdutoInformacoesAdicionais\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="BarcodeLib" Version="2.4.0" />
		<PackageReference Include="Hangfire.AspNetCore" Version="1.7.24" />
		<PackageReference Include="Hangfire.Core" Version="1.7.24" />
		<PackageReference Include="Hangfire.Dashboard.BasicAuthorization" Version="1.0.2" />
		<PackageReference Include="Hangfire.SqlServer" Version="1.7.24" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="10.0.1" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.21.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="5.0.7" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="5.0.7">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Azure" Version="1.1.0" />
		<PackageReference Include="Microsoft.Extensions.Caching.SqlServer" Version="5.0.1" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="5.0.2" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.1.4" />
		<PackageReference Include="System.Drawing.Common" Version="6.0.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Zendar.Business\Zendar.Business.csproj" />
	</ItemGroup>
	
	<ItemGroup>
		<Folder Include="Properties\PublishProfiles\" />
		<Folder Include="Properties\ServiceDependencies\" />
		<Folder Include="Zendar.ClientApp\src\pages\Importar\Importar\" />
	</ItemGroup>
	
	<ItemGroup>
	  <Reference Include="Multiempresa.Data">
	    <HintPath>..\Zendar.Business\DLL\Multiempresa.Data.dll</HintPath>
	  </Reference>
	  <Reference Include="Multiempresa.Shared">
	    <HintPath>..\Zendar.Data\DLL\Multiempresa.Shared.dll</HintPath>
	  </Reference>
	  <Reference Include="MultiPay.Portable">
	    <HintPath>..\Zendar.Business\DLL\MultiPay.Portable.dll</HintPath>
	  </Reference>
	  <Reference Include="MultiPay.Shared">
	    <HintPath>..\Zendar.Business\DLL\MultiPay.Shared.dll</HintPath>
	  </Reference>
	  <Reference Include="ZendarPackage.NotaFiscal">
	    <HintPath>..\Zendar.Business\DLL\ZendarPackage.NotaFiscal.dll</HintPath>
	  </Reference>
	</ItemGroup>


</Project>
