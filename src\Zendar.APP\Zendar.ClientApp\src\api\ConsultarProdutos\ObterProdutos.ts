import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';

import api, { ResponseApi } from 'services/api';

import { GridPaginadaRetorno } from 'components/Grid/Paginacao';
import { PaginationData } from 'components/update/Pagination';

import OptionType from 'types/optionType';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';

type Produto = {
  id: string;
  ativo: boolean;
  nome: string;
  referencia: string | null;
  sku: string;
  precoVenda: number;
  precoCompra: number;
  estoque: number;
  tipoProduto: number;
  volumeUnitario: boolean;
  possuiCores: boolean;
  possuiTamanhos: boolean;
};

type Filtros = {
  nome: string;
  sku: string;
  skuIdentificadorReferencia: string;
  codigoBarrasFornecedor: string;
  codigoBarrasEtiqueta: string;
  cores: string[] | null;
  tamanhos: string[] | null;
  categorias: string[] | null;
  marcas: string[] | null;
  tipoEstoque: OptionType<number> | number | null;
  statusConsulta?: number;
};

export const obterProdutosConsultarProdutos = ({
  paginationData,
  filtros,
}: {
  paginationData: PaginationData;
  filtros: Filtros;
}) => {
  return api.post<void, ResponseApi<GridPaginadaRetorno<Produto>>>(
    formatQueryPagegTable(
      ConstanteEnderecoWebservice.PRODUTO_CONSULTA_AVANCADA_LISTAGEM,
      paginationData
    ),
    {
      ...filtros,
    }
  );
};
