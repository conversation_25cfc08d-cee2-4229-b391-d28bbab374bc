import {
  <PERSON><PERSON><PERSON><PERSON>,
  FormLabel,
  useToken,
  Flex,
  Box,
  useMediaQuery,
  Text,
} from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { toast } from 'react-toastify';

import { setDateMinHours, setDateMaxHours } from 'helpers/data/setHoursDate';
import { formatOptionsSelectClient } from 'helpers/format/formatSelectClient';
import { validarListaServicos } from 'helpers/validation/validarListaServicos';

import api, { ResponseApi } from 'services/api';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import InputDateRange from 'components/PDV/InputDateRange';
import AsyncSelect from 'components/PDV/Select/AsyncSelectPadrao';
import CreatableSelect from 'components/PDV/Select/CreatableSelect';
import SelectPadrao from 'components/PDV/Select/SelectPadrao';
import { SelectCategoria } from 'components/Select/SelectCategoria';
import { SelectMulti } from 'components/Select/SelectMultiCheckbox';
import { CamposPersonalizados } from 'components/update/CamposPersonalizados';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import { Switch } from 'components/update/Switch';
import InputDateOrTime from 'components/v2/ui/InputDateOrTime';

import CampoPersonalizadoInterface from 'types/campoPersonalizado';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { IdentificacaoIntegracaoOptions } from 'constants/enum/IdentificacaoIntegracao';
import enumReferenciaServicoStargate from 'constants/enum/referenciaServicoStargate';
import { enumRelatorioProdutosVendidosPadrao } from 'constants/enum/RelatoriosPadrao';
import StatusConsultaEnum from 'constants/enum/statusConsulta';
import { StatusPesquisaClientesFornecedor } from 'constants/enum/statusPesquisaClientesFornecedor';
import { TipoAgrupamentoRelatorioItensMaisVendidos } from 'constants/enum/tipoAgrupamentoRelatorioItensMaisVendidos';
import TipoCadastroCampoPersonalizadoEnum from 'constants/enum/tipoCadastroCampoPersonalizado';
import TipoFiltroProdutoEstoqueEnum from 'constants/enum/tipoFiltroProdutoEstoque';
import { IconesCadastroPromocao } from 'icons';

import { NumberInput } from '../Input/NumberInput';

import { opcoesStatusOperacao, opcoesTipoFiscal } from './constants';
import {
  ListaRelatorioPersonalizadoProps,
  ListarSelectProps,
  ProdutoProps,
  Options,
  CamposPersonalizadosProps,
  ClienteProps,
} from './types';
import { validarTipoRelatorioProdutosVendidos } from './ValidarTipoRelatorioProdutosVendidos';

export const CustomFormStandardComponent = ({
  type,
  isLoading,
  optionsRelatorio,
  obterOpcoesLocaisDeEstoque = async () => [],
}: ListaRelatorioPersonalizadoProps) => {
  const [loading, setLoading] = useState(false);
  const [cores, setCores] = useState<Options[]>([]);
  const [coresIsLoading, setCoresIsLoading] = useState(false);
  const [tamanhos, setTamanhos] = useState<Options[]>([]);
  const [tamanhosIsLoading, setTamnhosIsLoading] = useState(false);
  const [marcas, setMarcas] = useState<Options[]>([]);
  const [marcasIsLoading, setMarcasIsLoading] = useState(false);
  const [tags, setTags] = useState<Options[]>([]);
  const [tagsIsLoading, setTagsIsLoading] = useState(false);
  const [tabelaPreco, setTabelaPreco] = useState<Options[]>([]);
  const [relatorios, setListRelatorio] = useState<Options[]>([]);
  const [relatoriosIsloading, setRelatoriosLoading] = useState(false);
  const [tabelaPrecoIsLoading, setTabelaPrecoIsLoading] = useState(false);
  const [camposPersonalizados, setCamposPersonalizados] = useState<
    CampoPersonalizadoInterface[]
  >([]);
  const [listaDeVendedores, setListaDeVendores] = useState<Options[]>([]);
  const [produtoIsLoading, setProdutoIsLoading] = useState(false);
  const [listaCaixas, setListaCaixas] = useState<Options[]>([]);
  const [isLoadingListaDeVendores, setIsLoadingListaDeVendores] =
    useState<boolean>(false);

  const { setValue, getValues, watch, clearErrors } = useFormContext();
  const [white, gray400] = useToken('colors', ['white', 'gray.400']);

  const tipoRelatorioSelecionado = watch('tipoRelatorio');
  const inclusoHorasNoPeriodo = watch('inclusoHorasNoPeriodo');

  const [maiorQue1200] = useMediaQuery('(min-width: 1200px)');

  const relatorioEstoqueOuPersonalizados =
    type === 'Estoque' || type === 'Personalizados';

  const relatoriosProdutosVendidos =
    enumRelatorioProdutosVendidosPadrao.properties
      .filter(
        (item) =>
          validarTipoRelatorioProdutosVendidos(item) &&
          item.value !== enumRelatorioProdutosVendidosPadrao.PRODUTO_COM_PRECO
      )
      .map((item) => {
        return {
          label: item.name,
          value: item.value,
        } as Options;
      });

  const possuiServicoFrenteCaixa = validarListaServicos([
    enumReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA,
    enumReferenciaServicoStargate.MODULO_FRENTE_CAIXA,
  ]);

  const tipoEstoque = Object.entries(
    TipoFiltroProdutoEstoqueEnum.properties
  ).map((value: any) => {
    return (
      {
        label: value[1].name,
        value: value[1].value,
      } || {}
    );
  });

  const getCoresValues = useCallback(async () => {
    setCoresIsLoading(true);
    const response = await api.get<void, ResponseApi<ListarSelectProps[]>>(
      ConstanteEnderecoWebservice.COR_LISTAR_SELECT,
      {
        params: {
          statusConsulta: StatusConsultaEnum.TODOS,
          listarPadraoSistema: true,
        },
      }
    );

    if (response) {
      if (response.avisos) {
        response.avisos.map((item: string) => toast.warning(item));
      }

      if (response.sucesso) {
        setCores(
          response.dados.map((cor) => {
            return {
              label: cor.nome,
              value: cor.id,
            } as Options;
          })
        );
      }
    }
    setCoresIsLoading(false);
  }, []);

  const getTamanhosValues = useCallback(async () => {
    setTamnhosIsLoading(true);
    const response = await api.get<void, ResponseApi<ListarSelectProps[]>>(
      ConstanteEnderecoWebservice.TAMANHO_LISTAR_SELECT,
      {
        params: {
          statusConsulta: StatusConsultaEnum.TODOS,
          listarPadraoSistema: true,
        },
      }
    );

    if (response) {
      if (response.avisos) {
        response.avisos.map((item: string) => toast.warning(item));
      }

      if (response.sucesso) {
        setTamanhos(
          response.dados.map((tamanho) => {
            return {
              label: tamanho.nome,
              value: tamanho.id,
            } as Options;
          })
        );
      }
    }
    setTamnhosIsLoading(false);
  }, []);

  const getMarcasValues = useCallback(async () => {
    setMarcasIsLoading(true);
    const response = await api.get<void, ResponseApi<ListarSelectProps[]>>(
      ConstanteEnderecoWebservice.MARCA_LISTAR_SELECT,
      {
        params: { statusConsulta: StatusConsultaEnum.TODOS },
      }
    );

    if (response) {
      if (response.avisos) {
        response.avisos.map((item: string) => toast.warning(item));
      }

      if (response.sucesso) {
        setMarcas(
          response.dados.map((marca) => {
            return {
              label: marca.nome,
              value: marca.id,
            } as Options;
          })
        );
      }
    }
    setMarcasIsLoading(false);
  }, []);

  const getTagsValues = useCallback(async () => {
    setTagsIsLoading(true);
    const response = await api.get<void, ResponseApi<ListarSelectProps[]>>(
      ConstanteEnderecoWebservice.TAG_LISTAR_SELECT
    );

    if (response) {
      if (response.avisos) {
        response.avisos.map((item: string) => toast.warning(item));
      }

      if (response.sucesso) {
        setTags(
          response.dados.map((tag) => {
            return {
              label: tag.nome,
              value: tag.id,
            } as Options;
          })
        );
      }
    }
    setTagsIsLoading(false);
  }, []);

  const getTabelaPrecoValues = useCallback(async () => {
    setTabelaPrecoIsLoading(true);
    const response = await api.get<void, ResponseApi<ListarSelectProps[]>>(
      ConstanteEnderecoWebservice.TABELA_PRECO_LISTAR_TABELAS_PRECO
    );

    if (response) {
      if (response.avisos) {
        response.avisos.map((item: string) => toast.warning(item));
      }

      if (response.sucesso) {
        setTabelaPreco(
          response.dados.map((tabelasPreco) => {
            return {
              label: tabelasPreco.nome,
              value: tabelasPreco.id,
            } as Options;
          })
        );
      }
    }

    setTabelaPrecoIsLoading(false);
  }, []);

  const getCamposPersonalizados = useCallback(async () => {
    setLoading(true);
    const response = await api.get<
      void,
      ResponseApi<CampoPersonalizadoInterface[]>
    >(ConstanteEnderecoWebservice.CAMPO_PERSONALIZADO_LISTARPORTIPOCADASTRO, {
      params: { tipoCadastro: TipoCadastroCampoPersonalizadoEnum.PRODUTO },
    });

    if (response) {
      if (response.avisos) {
        response.avisos.map((item: string) => toast.warning(item));
      }

      if (response.sucesso && response.dados) {
        setCamposPersonalizados(response.dados);

        const currentCamposPersonalizados = getValues('camposPersonalizados');

        setValue(
          'camposPersonalizados',
          response.dados.map((campoPersonalizado: any) => {
            const currentCampoPersonalizado = currentCamposPersonalizados
              ? currentCamposPersonalizados.find(
                  (x: CamposPersonalizadosProps) =>
                    x?.campoPersonalizadoId === campoPersonalizado.id
                )
              : undefined;

            return {
              campoPersonalizadoId: campoPersonalizado.id,
              valor: currentCampoPersonalizado
                ? currentCampoPersonalizado.valor
                : null,
            } as CamposPersonalizadosProps;
          })
        );
      }
    }
    setLoading(false);
  }, [getValues, setValue]);

  const getListaRelatorios = useCallback(async () => {
    setRelatoriosLoading(true);
    const response = await api.get<void, ResponseApi<ListarSelectProps[]>>(
      ConstanteEnderecoWebservice.RELATORIOS_PERSONALIZADOS_LISTAR
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response.sucesso && response.dados) {
        const listRelatorios = response.dados.map((relatorioItem) => {
          return {
            label: relatorioItem.nome,
            value: relatorioItem.id,
          } as Options;
        });

        setListRelatorio(listRelatorios);
      }
    }
    setRelatoriosLoading(false);
  }, []);

  const getListaDeVendedores = async () => {
    setIsLoadingListaDeVendores(true);
    const response = await api.get<void, ResponseApi<ListarSelectProps[]>>(
      ConstanteEnderecoWebservice.VENDEDOR_LISTAR_SELECT_POR_LOJA
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((item) => toast.warn(item));
      }

      if (response.sucesso && response.dados) {
        const listOfSellers = response.dados.map(
          (seller) =>
            ({
              label: seller.nome,
              value: seller.id,
            } as Options)
        );
        setListaDeVendores(listOfSellers);
      }
    }
    setIsLoadingListaDeVendores(false);
  };

  const handleGetClientes = useCallback(async (inputValue?: string) => {
    const response = await api.get<void, ResponseApi<ClienteProps[]>>(
      ConstanteEnderecoWebservice.CLIENTE_FORNECEDOR_LISTAR_SELECT,
      {
        params: {
          filtroTipoCadastroPessoa:
            type === 'RelatorioCompras'
              ? StatusPesquisaClientesFornecedor.FORNECEDORES
              : StatusPesquisaClientesFornecedor.CLIENTES,
          cpfCnpjNomeApelidoCodigoExterno: inputValue,
        },
      }
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response.sucesso && response.dados) {
        const dados = response.dados.map((client) => {
          const option = formatOptionsSelectClient(client);
          return option;
        });
        return dados;
      }
    }

    return [];
  }, []);

  const obterProdutoListaPaginada = useCallback(async (inputValue?: string) => {
    setProdutoIsLoading(true);

    const response = await api.get<void, ResponseApi<ProdutoProps[]>>(
      ConstanteEnderecoWebservice.LISTAR_SELECT_PRODUTO,
      {
        params: {
          nomeSkuCodigoExternoBarrasGtinEan: inputValue,
        },
      }
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso && response?.dados) {
        setProdutoIsLoading(false);

        return response?.dados.map((produto) => ({
          label: produto.nome,
          value: produto.id,
        }));
      }
    }

    setProdutoIsLoading(false);
    return [];
  }, []);

  const obterCaixas = async () => {
    setLoading(true);
    const response = await api.get<void, ResponseApi<ListarSelectProps[]>>(
      ConstanteEnderecoWebservice.CONTA_FINANCEIRA_LISTAR_CAIXA_ATIVO
    );

    if (response) {
      if (response.sucesso) {
        const caixas = response.dados.map(({ id, nome }) => ({
          label: nome,
          value: id,
        }));

        setListaCaixas(caixas);
      }
    }
    setLoading(false);
  };

  const resetarErrosInputsDataHora = () => {
    clearErrors('dataEmissaoInicio');
    clearErrors('dataEmissaoFim');
  };

  const formatarData = (dataOriginal: string, incluirHoras: boolean) => {
    if (!dataOriginal || isNaN(Date.parse(dataOriginal))) {
      return null;
    }

    const data = new Date(dataOriginal);

    if (incluirHoras) {
      data.setUTCHours(data.getUTCHours() - 3);
      return new Date(data).toISOString().slice(0, 16);
    }

    return new Date(`${data.toISOString()}`);
  };

  const handleFormatarDatas = ({ incluirHoras }: { incluirHoras: boolean }) => {
    const dataEmissaoInicio = watch('dataEmissaoInicio');
    const dataEmissaoFim = watch('dataEmissaoFim');

    const dataInicioFormatada = formatarData(dataEmissaoInicio, incluirHoras);
    const dataFimFormatada = formatarData(dataEmissaoFim, incluirHoras);

    if (!dataInicioFormatada || !dataFimFormatada) {
      return;
    }

    setValue(
      'dataEmissaoInicio',
      incluirHoras
        ? dataInicioFormatada
        : setDateMinHours(dataInicioFormatada as Date)
    );
    setValue(
      'dataEmissaoFim',
      incluirHoras
        ? dataFimFormatada
        : setDateMaxHours(dataFimFormatada as Date)
    );
  };

  useEffect(() => {
    obterCaixas();
  }, []);

  useEffect(() => {
    getCoresValues();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    getTamanhosValues();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    getMarcasValues();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    getTagsValues();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    getTabelaPrecoValues();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    getCamposPersonalizados();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    getListaRelatorios();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    getListaDeVendedores();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {(isLoading || loading) && <LoadingPadrao />}
      <Box bg="white" p="8" borderRadius="md" boxShadow="lg">
        <SimpleGridForm marginY="6">
          <GridItem
            colSpan={
              maiorQue1200 || !inclusoHorasNoPeriodo
                ? { base: 12, sm: 6, lg: 4 }
                : 12
            }
          >
            {type === 'ProdutosComPreco' ? (
              <SelectPadrao
                placeholder="Selecione uma tabela de preço"
                id="tabelaPrecoId"
                name="tabelaPrecoId"
                label="Tabela de preço"
                required
                isLoading={tabelaPrecoIsLoading}
                options={tabelaPreco}
              />
            ) : (
              <SelectPadrao
                placeholder="Selecione um relatório"
                id="tipoRelatorio"
                name="tipoRelatorio"
                label="Relatório"
                isLoading={
                  relatorioEstoqueOuPersonalizados ? relatoriosIsloading : false
                }
                options={
                  optionsRelatorio ||
                  (relatorioEstoqueOuPersonalizados
                    ? relatorios
                    : relatoriosProdutosVendidos)
                }
                required
              />
            )}
          </GridItem>
          {(type === 'ProdutosVendidos' || type === 'RelatorioCompras') && (
            <>
              <GridItem
                colSpan={
                  maiorQue1200 || !inclusoHorasNoPeriodo
                    ? {
                        base: 12,
                        sm: 6,
                        lg: 4,
                      }
                    : 12
                }
              >
                <Flex w="full" wrap="wrap">
                  <FormLabel
                    mb="0"
                    fontSize="sm"
                    color="black"
                    lineHeight="1.2"
                  >
                    Período *
                  </FormLabel>
                  {type === 'ProdutosVendidos' && (
                    <>
                      <Flex
                        margin="0 0 0 auto"
                        gap="12px"
                        height="16px"
                        align="flex-start"
                        mb="2px"
                      >
                        <Box>
                          <Switch
                            size="sm"
                            colorScheme="blue"
                            id="inclusoHorasNoPeriodo"
                            name="inclusoHorasNoPeriodo"
                            onChange={(e) => {
                              const incluirHoras = e?.target?.checked;
                              resetarErrosInputsDataHora();
                              handleFormatarDatas({
                                incluirHoras,
                              });
                            }}
                            sx={{
                              '.chakra-switch__track': {
                                boxShadow: 'none !important',
                              },
                            }}
                          />
                        </Box>
                        <FormLabel
                          fontSize="xs"
                          color="gray.700"
                          userSelect="none"
                          cursor="pointer"
                          mr="0px"
                          htmlFor="inclusoHorasNoPeriodo"
                        >
                          Incluir horas no filtro
                        </FormLabel>
                      </Flex>
                    </>
                  )}
                </Flex>
                {inclusoHorasNoPeriodo ? (
                  <Flex
                    gap={{ base: 2 }}
                    direction={{ base: 'column', sm: 'row' }}
                    align="center"
                  >
                    <InputDateOrTime
                      type="datetime-local"
                      name="dataEmissaoInicio"
                      icon={IconesCadastroPromocao.Calendario}
                    />
                    <Text display={{ base: 'none', sm: 'block' }}>até</Text>
                    <InputDateOrTime
                      type="datetime-local"
                      name="dataEmissaoFim"
                      icon={IconesCadastroPromocao.Calendario}
                    />
                  </Flex>
                ) : (
                  <InputDateRange
                    name="dataEmissao"
                    startDateName="dataEmissaoInicio"
                    placeholder="Selecione a data"
                    endDateName="dataEmissaoFim"
                    borderColor="gray.100"
                    borderRadius="base"
                    inputValueIsSmaller={false}
                    maxDate={new Date()}
                  />
                )}
              </GridItem>
              {type === 'ProdutosVendidos' && possuiServicoFrenteCaixa && (
                <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
                  <FormLabel
                    mb="0"
                    fontSize="sm"
                    color="black"
                    lineHeight="1.2"
                  >
                    Mesa/Comanda
                  </FormLabel>
                  <Flex gap={{ base: 2 }} align="center">
                    <NumberInput
                      name="numeracaoComandaInicial"
                      placeholder="Numeração inicial"
                      scale={0}
                      max={9999}
                      pl={4}
                      textAlign="left"
                      canBeUndefined
                    />
                    até
                    <NumberInput
                      name="numeracaoComandaFinal"
                      placeholder="Numeração final"
                      scale={0}
                      max={9999}
                      pl={4}
                      textAlign="left"
                      canBeUndefined
                    />
                  </Flex>
                </GridItem>
              )}
              <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
                <AsyncSelect
                  id="clienteFornecedorId"
                  name="clienteFornecedorId"
                  placeholder={
                    type === 'RelatorioCompras'
                      ? 'Informe o fornecedor'
                      : 'Informe o cliente'
                  }
                  label={type === 'RelatorioCompras' ? 'Fornecedor' : 'Cliente'}
                  handleGetOptions={handleGetClientes}
                  asControlledByObject
                  isClearable
                  shouldAppearTheAddress
                />
              </GridItem>
              {type === 'ProdutosVendidos' && (
                <>
                  {tipoRelatorioSelecionado ===
                    enumRelatorioProdutosVendidosPadrao.ITENS_MAIS_VENDIDOS && (
                    <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
                      <SelectPadrao
                        id="tipoAgrupamento"
                        name="tipoAgrupamento"
                        label="Agrupamento"
                        placeholder="Selecione um tipo de agrupamento"
                        required
                        options={
                          TipoAgrupamentoRelatorioItensMaisVendidos.options
                        }
                      />
                    </GridItem>
                  )}
                  {tipoRelatorioSelecionado ===
                    enumRelatorioProdutosVendidosPadrao.RELATORIO_PRODUTO_POR_VENDA && (
                    <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
                      <SelectMulti
                        id="contasFinanceirasId"
                        closeMenuOnSelect={false}
                        name="contasFinanceirasId"
                        label="Caixa"
                        isMulti
                        textLabelSelectAll="Todos os caixas"
                        variant="outline"
                        options={listaCaixas}
                        isSearchable
                        styles={{
                          control: () => ({
                            height: '36px !important',
                          }),
                        }}
                        minHeight="34px"
                        placeholder="Selecione os caixas"
                        isDisabled={listaCaixas?.length < 1}
                      />
                    </GridItem>
                  )}
                  <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
                    <SelectPadrao
                      id="vendedorId"
                      name="vendedorId"
                      label="Vendedor"
                      placeholder="Selecione um vendedor"
                      options={listaDeVendedores}
                      isLoading={isLoadingListaDeVendores}
                      isClearable
                    />
                  </GridItem>
                </>
              )}
            </>
          )}
          <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
            <AsyncSelect
              id="produtoId"
              name="produtoId"
              label="Produto"
              placeholder="Digite o nome ou referência"
              isLoading={produtoIsLoading}
              handleGetOptions={obterProdutoListaPaginada}
              asControlledByObject
              isClearable
            />
          </GridItem>
          <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
            <SelectPadrao
              id="statusConsulta"
              name="statusConsulta"
              label="Status"
              placeholder="Selecione um status"
              options={StatusConsultaEnum.status}
              isClearable={false}
            />
          </GridItem>
          <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
            <SelectPadrao
              id="tipoEstoque"
              name="tipoEstoque"
              label="Estoque"
              placeholder="Selecione um tipo de estoque"
              isClearable={false}
              options={tipoEstoque}
            />
          </GridItem>
          <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
            <SelectCategoria name="categoriasProduto" label="Categoria" />
          </GridItem>
          <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
            <CreatableSelect
              id="marcas"
              name="marcas"
              label="Marca"
              isMulti
              placeholder="Selecione as opções desejadas"
              options={marcas}
              isLoading={marcasIsLoading}
              multiValueBg={gray400}
              multiValueColor={white}
              creatableButtonShow={false}
              creatableInputTextPreffix=""
              handleCreateOption={() => {}}
            />
          </GridItem>
          {(type === 'ProdutosVendidos' || type === 'Estoque') && (
            <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
              <AsyncSelect
                id="localEstoqueIds"
                name="localEstoqueIds"
                label="Local de estoque"
                placeholder="Selecione o local de estoque"
                handleGetOptions={obterOpcoesLocaisDeEstoque}
                isSearchable={false}
                isMulti
                isClearable
              />
            </GridItem>
          )}
          {type === 'ProdutosVendidos' && (
            <>
              <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
                <SelectMulti
                  id="tipoFiscal"
                  name="tipoFiscal"
                  label="Tipo fiscal"
                  textLabelSelectAll="Todos"
                  placeholder="Selecione as opções desejadas"
                  options={opcoesTipoFiscal}
                  isSearchable={false}
                />
              </GridItem>
              <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
                <SelectPadrao
                  id="statusVenda"
                  name="statusVenda"
                  label="Status da venda"
                  placeholder="Selecione as opções desejadas"
                  options={opcoesStatusOperacao}
                  isSearchable={false}
                />
              </GridItem>
              <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
                <SelectPadrao
                  id="origem"
                  label="Origem"
                  name="origem"
                  options={IdentificacaoIntegracaoOptions}
                  isSearchable={false}
                />
              </GridItem>
            </>
          )}
          <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
            <CreatableSelect
              id="cores"
              name="cores"
              label="Cor"
              isMulti
              placeholder="Selecione as opções desejadas"
              options={cores}
              isLoading={coresIsLoading}
              multiValueBg={gray400}
              multiValueColor={white}
              creatableButtonShow={false}
              creatableInputTextPreffix=""
              handleCreateOption={() => {}}
            />
          </GridItem>
          <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
            <CreatableSelect
              id="tamanhos"
              name="tamanhos"
              label="Tamanho"
              isMulti
              placeholder="Selecione as opções desejadas"
              options={tamanhos}
              isLoading={tamanhosIsLoading}
              multiValueBg={gray400}
              multiValueColor={white}
              creatableButtonShow={false}
              creatableInputTextPreffix=""
              handleCreateOption={() => {}}
            />
          </GridItem>
          <GridItem colSpan={{ base: 12, sm: 6, lg: 4 }}>
            <CreatableSelect
              id="tags"
              name="tags"
              label="Tags"
              isMulti
              placeholder="Selecione as opções desejadas"
              options={tags}
              isLoading={tagsIsLoading}
              multiValueBg={gray400}
              multiValueColor={white}
              creatableButtonShow={false}
              creatableInputTextPreffix=""
              handleCreateOption={() => {}}
            />
          </GridItem>
          <GridItem
            colSpan={{ base: 12, md: 12, lg: 12 }}
            sx={{ '& > div': { padding: '1rem 0 0  !Important' } }}
          >
            <CamposPersonalizados
              labelOfNull
              camposPersonalizados={camposPersonalizados}
            />
          </GridItem>
        </SimpleGridForm>
      </Box>
    </>
  );
};
