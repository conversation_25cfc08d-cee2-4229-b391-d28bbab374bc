﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Zendar.Data.Enums;
using Zendar.Data.Helpers;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.ViewModels;

namespace Zendar.Data.Interfaces.Aplicacao
{
    public interface IUsuarioRepository : IDisposable
    {
        Task<bool> Any(Expression<Func<Usuario, bool>> predicate = null);
        Task<Usuario> ObterUsuarioPorUserNameComPermissoes(string userName);
        Task<Usuario> ObterUsuarioPorUserNameSemPermissoes(string userName);
        Task<Usuario> BuscarPorNome(string nome);
        Task<Usuario> BuscarPorUserName(string userName);
        Task<Usuario> BuscarPorEmail(string Email);
        Task<Usuario> BuscarAdministrador();
        Task<Usuario> BuscarPorId(Guid Id);
        GridPaginadaRetorno<UsuarioPaginadoViewModel> ListarPaginado(GridPaginadaConsulta gridPaginada, string nome, bool? ativo = null);
        void SetConnectionString(string connectionString);
        Task<List<UsuarioAtivoListaViewModel>> ListarTodosUsuariosAtivos();
        int ObterQuantidadeUsuariosAtivos(TipoUsuario tipoUsuario);
        Task<int> ObterQuantidadeUsuariosAtivos();
        Task<List<IdNomeViewModel>> ListarSelect(Guid lojaId);
        Task CadastrarUsuarioPeloMultiempresa(Usuario usuario);
        Task<bool> VerificarSeEmailJaExiste(string email, Guid? usuarioId);
        Task<bool> VerificarSeLoginJaExiste(string login, Guid? usuarioId);
        Task<List<Guid>> ObterUsuariosMaisRecentesParaInativar(TipoUsuario usuario, int qtd);
        Task<string> ObterNomePorId(Guid usuarioId);
        Task<bool> ValidarPinUsuario(string pin, Guid usuarioId);

        Task<bool> ValidarPinUsuario(
            string pinUsuario);

        Task<Guid?> ObterUsuario(
            string pinUsuario,
            Guid lojaId);

        Task<bool> ValidarSecurityStampAsync(Guid id, string securityStamp);

        Task<TReturn> GetByIdAsync<TReturn>(
            Guid id,
            Expression<Func<Usuario, TReturn>> selector = null);

        Task<List<TReturn>> FindAllActiveAsync<TReturn>(
            Expression<Func<Usuario, TReturn>> selector = null);

        /// <summary>
        /// Inativar usuário(s).
        /// </summary>
        /// <param name="usuarioId">Id do usuário para inativar. Caso nada seja informado, todos os usuários serão inativados</param>
        Task Inativar(Guid? usuarioId = null);

        Task<List<Guid>> ObterUsuariosParaVincularComLojaCadastrada();

        Task<Usuario> ObterCompletoPorId(
            Guid id);

        Task<List<Usuario>> ObterListaCompleto(
            DateTime? dataAtualizacao,
        Guid lojaId);

        Task<decimal> ObterDescontoMaximoPermitido(Guid usuarioId);

        Task<bool> Existe(Guid usuarioId);
    }
}