import { useCallback, useState } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';

import {
  Loja,
  ProdutosProps,
  ProdutoComErro,
  ResultadoImportacao,
} from './types';

export type TabelaPrecoHook = {
  obterLojas: () => Promise<Loja[]>;
  obterProdutosParaExportar: (
    deveExportarTodos: boolean,
    tabelaPrecoId: string | null,
    tabelaPrecoSimples?: boolean
  ) => Promise<string>;
  validarCSV: (arquivo: File) => boolean;
  lerArquivoCSV: (
    arquivo: File,
    tabelaPrecoSimples?: boolean
  ) => Promise<ResultadoImportacao | undefined>;
  exportarProdutosComErro: (
    produtosComErro: ProdutoComErro[],
    tabelaPrecoSimples?: boolean
  ) => void;
  importarProdutoNaTabelaPreco: (
    produtos: ProdutosProps,
    tabelaPrecoSimples?: boolean,
    deveRecarregar?: boolean
  ) => Promise<void>;
  idDoParametroRota: string;
  setIsLoading: (isLoading: boolean) => void;
  isLoading: boolean;
};

export const useTabelaPreco = () => {
  const [isLoading, setIsLoading] = useState(false);

  const idRota = useParams<{ id: string }>();
  const idDoParametroRota = idRota.id;

  const obterLojas = useCallback(async () => {
    const response = await api.get<void, ResponseApi<Loja[]>>(
      ConstanteEnderecoWebservice.OBTER_LOJAS_TABELA_PRECO,
      {
        params: { tabelaPrecoId: idDoParametroRota || null },
      }
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso: string) => toast.warning(aviso));
      }

      if (response.sucesso) {
        return response.dados;
      }
    }

    return [];
  }, [idDoParametroRota]);

  const baixarItensExportadosEmFormatoCSV = useCallback(
    (itens: any, nomeArquivo?: string) => {
      const dataHora = new Date();
      const dataFormatada = `${dataHora.getDate()}-${
        dataHora.getMonth() + 1
      }-${dataHora.getFullYear()}-0${dataHora.getHours()}-${dataHora.getMinutes()}`;

      const url = window.URL.createObjectURL(new Blob([itens]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute(
        'download',
        nomeArquivo || `${dataFormatada}-tabela-preco.csv`
      );
      link.click();
    },
    []
  );

  const obterProdutosParaExportar = useCallback(
    async (
      deveExportarTodos: boolean,
      tabelaPrecoId: string | null,
      tabelaPrecoSimples = false
    ) => {
      const url = tabelaPrecoSimples
        ? ConstanteEnderecoWebservice.TABELA_PRECO_OBTER_PRODUTOS_SIMPLES_PARA_EXPORTAR
        : ConstanteEnderecoWebservice.TABELA_PRECO_OBTER_PRODUTOS_COR_TAMANHO_PARA_EXPORTAR;

      const response = await api.get<void, ResponseApi<string>>(url, {
        params: { tabelaPrecoId: deveExportarTodos ? null : tabelaPrecoId },
      });

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }

        if (response.sucesso) {
          baixarItensExportadosEmFormatoCSV(response.dados);
        }
      }

      return '';
    },
    [baixarItensExportadosEmFormatoCSV]
  );

  const exportarProdutosComErro = useCallback(
    (produtosComErro: ProdutoComErro[], tabelaPrecoSimples = false) => {
      if (produtosComErro.length === 0) {
        toast.warning('Não há produtos com erro para exportar');
        return;
      }

      const cabecalho = tabelaPrecoSimples
        ? 'Codigo;Produto;Preco de venda atual;Preco de venda novo'
        : 'Codigo;Produto;Cor;Tamanho;Preco de venda atual;Preco de venda novo';

      const conteudoCSV = [
        cabecalho,
        ...produtosComErro.map((produto) => {
          if (tabelaPrecoSimples) {
            return `${produto.codigo};${produto.nome};${produto.precoVendaAtual};${produto.precoVendaNovo};`;
          } else {
            return `${produto.codigo};${produto.nome};;;${produto.precoVendaAtual};${produto.precoVendaNovo};`;
          }
        }),
      ].join('\r\n');

      const dataHora = new Date();
      const dataFormatada = `${dataHora.getDate()}-${
        dataHora.getMonth() + 1
      }-${dataHora.getFullYear()}-${dataHora.getHours()}-${dataHora.getMinutes()}`;

      const nomeArquivo = `${dataFormatada}-produtos-com-erro.csv`;
      baixarItensExportadosEmFormatoCSV(conteudoCSV, nomeArquivo);
    },
    [baixarItensExportadosEmFormatoCSV]
  );

  const importarProdutoNaTabelaPreco = useCallback(
    async (
      produtos: ProdutosProps,
      tabelaPrecoSimples?: boolean,
      deveRecarregar = true
    ) => {
      setIsLoading(true);
      const url = tabelaPrecoSimples
        ? ConstanteEnderecoWebservice.TABELA_PRECO_IMPORTAR_PRODUTOS_LISTAGEM_PRODUTOS_SIMPLES
        : ConstanteEnderecoWebservice.TABELA_PRECO_IMPORTAR_PRODUTOS_LISTAGEM_PRODUTOS_COR_TAMANHO;
      const response = await api.post<void, ResponseApi<string>>(
        `${url}/${idRota.id}`,
        {
          produtos,
        }
      );
      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso: string) => toast.warning(aviso));
          setIsLoading(false);
        }

        if (response.sucesso) {
          setIsLoading(false);
          if (deveRecarregar) {
            toast.success('Produtos importados com sucesso!');
            setTimeout(() => {
              window.location.reload();
            }, 2000);
          }
        }
      }
      setIsLoading(false);
    },
    [idRota.id]
  );

  const normalizarValorMonetario = (valorTexto: string): string => {
    if (!valorTexto?.trim()) return '0';

    let valor = valorTexto.replace(/['"\s]/g, '').replace(/[^\d.,]/g, '');

    const temPonto = valor.includes('.');
    const temVirgula = valor.includes(',');

    if (temPonto && temVirgula) {
      valor = valor.replace(/\./g, '').replace(',', '.');
    } else if (temVirgula) {
      valor = valor.replace(',', '.');
    } else if (temPonto) {
      const partes = valor.split('.');
      if (partes.length === 2) {
        const parteDecimal = partes[1];

        if (parteDecimal.length === 3 && parteDecimal !== '0000') {
          valor = valor.replace('.', '');
        }
      } else if (partes.length > 2) {
        valor = valor.replace(/\./g, '');
      }
    }

    return valor || '0';
  };

  const lerArquivoCSV = useCallback(
    async (
      arquivoCSV: File,
      tabelaPrecoSimples?: boolean
    ): Promise<ResultadoImportacao | undefined> => {
      if (arquivoCSV.size === 0) {
        toast.warning('Não é possível importar um arquivo vazio');
        return;
      }

      try {
        setIsLoading(true);

        const lerConteudoArquivo = (arquivo: File): Promise<string> => {
          return new Promise((resolver, rejeitar) => {
            const leitor = new FileReader();

            leitor.onload = () => {
              if (leitor.result) {
                resolver(leitor.result.toString());
              } else {
                rejeitar(new Error('Erro ao ler o arquivo.'));
              }
            };

            leitor.onerror = () => {
              rejeitar(new Error('Erro ao carregar o arquivo.'));
            };

            leitor.readAsText(arquivo);
          });
        };
        const conteudoArquivo = await lerConteudoArquivo(arquivoCSV);
        const linhasCSV = conteudoArquivo.split('\r\n');

        if (linhasCSV.length > 0) {
          const linhaCabecalho = linhasCSV[0].toLowerCase();
          const colunasCabecalho = linhaCabecalho.split(';');
          const numeroColunas = colunasCabecalho.length;

          const temColunaCor = colunasCabecalho.some((coluna: string) =>
            coluna.includes('cor')
          );
          const temColunaTamanho = colunasCabecalho.some((coluna: string) =>
            coluna.includes('tamanho')
          );

          if (
            tabelaPrecoSimples &&
            (numeroColunas >= 6 || temColunaCor || temColunaTamanho)
          ) {
            toast.warning(
              'O arquivo possui colunas de variação. Para produtos com variação, use a aba Preços especiais por variação'
            );
            return;
          }

          if (
            !tabelaPrecoSimples &&
            numeroColunas <= 4 &&
            !temColunaCor &&
            !temColunaTamanho
          ) {
            toast.warning(
              'O arquivo não possui colunas de variação. Para produtos sem variação, use a aba Preços fixos'
            );
            return;
          }
        }

        const processarLinha = (linhaCsv: string, index: number) => {
          if (index === 0 || !linhaCsv.trim()) {
            return null;
          }

          let codigo = '';
          let nome = '';
          let precoVendaAtual = '';
          let precoVendaNovo = '';

          const campos = linhaCsv.split(';');

          if (tabelaPrecoSimples) {
            [codigo, nome, precoVendaAtual, precoVendaNovo] = campos;
          } else {
            [codigo, nome, , , precoVendaAtual, precoVendaNovo] = campos;
          }

          if (!codigo) {
            return {
              tipo: 'erro' as const,
              produto: {
                linha: index + 1,
                codigo: codigo || '',
                nome: nome || '',
                precoVendaAtual: precoVendaAtual || '',
                precoVendaNovo: precoVendaNovo || '',
                descricaoErro: 'Código do produto não é válido',
                dadosOriginais: linhaCsv,
              },
            };
          }

          if (codigo === 'I' || codigo === 'C') {
            return {
              tipo: 'erro' as const,
              produto: {
                linha: index + 1,
                codigo: codigo || '',
                nome: nome || '',
                precoVendaAtual: precoVendaAtual || '',
                precoVendaNovo: precoVendaNovo || '',
                descricaoErro: 'Código do produto não informado',
                dadosOriginais: linhaCsv,
              },
            };
          }

          if (!precoVendaNovo) {
            return {
              tipo: 'erro' as const,
              produto: {
                linha: index + 1,
                codigo: codigo || '',
                nome: nome || '',
                precoVendaAtual: precoVendaAtual || '',
                precoVendaNovo: '',
                descricaoErro: 'Preço de venda novo não informado',
                dadosOriginais: linhaCsv,
              },
            };
          }

          if (precoVendaNovo.match(/[a-zA-Z]/)) {
            return {
              tipo: 'erro' as const,
              produto: {
                linha: index + 1,
                codigo: codigo || '',
                nome: nome || '',
                precoVendaAtual: precoVendaAtual || '',
                precoVendaNovo: precoVendaNovo || '',
                descricaoErro: 'Preço de venda novo não é um número válido',
                dadosOriginais: linhaCsv,
              },
            };
          }

          const precoVenda = Number(normalizarValorMonetario(precoVendaNovo));

          if (precoVenda <= 0) {
            return {
              tipo: 'erro' as const,
              produto: {
                linha: index + 1,
                codigo: codigo || '',
                nome: nome || '',
                precoVendaAtual: precoVendaAtual || '',
                precoVendaNovo: 0,
                descricaoErro: 'Preço de venda deve ser maior que zero',
                dadosOriginais: linhaCsv,
              },
            };
          }

          const letterToReplace = tabelaPrecoSimples ? 'I' : 'C';

          return {
            tipo: 'sucesso' as const,
            produto: {
              codigo: codigo.replace(letterToReplace, ''),
              precoVenda,
            },
          };
        };

        const resultadosProcessamento = linhasCSV
          .map(processarLinha)
          .filter(
            (resultado): resultado is NonNullable<typeof resultado> =>
              resultado !== null
          );

        const produtosComSucesso = resultadosProcessamento
          .filter((resultado) => resultado.tipo === 'sucesso')
          .map(
            (resultado) =>
              resultado.produto as { codigo: string; precoVenda: number }
          );

        const produtosComErro = resultadosProcessamento
          .filter((resultado) => resultado.tipo === 'erro')
          .map((resultado) => resultado.produto as ProdutoComErro);

        const totalProcessados = resultadosProcessamento.length;

        const resultado: ResultadoImportacao = {
          produtosComSucesso,
          produtosComErro,
          totalProcessados,
        };

        if (produtosComSucesso.length > 0 && produtosComErro.length === 0) {
          await importarProdutoNaTabelaPreco(
            produtosComSucesso,
            tabelaPrecoSimples,
            false
          );
        }

        return resultado;
      } catch (error) {
        toast.error('Erro ao processar o arquivo');
        return undefined;
      } finally {
        setIsLoading(false);
      }
    },
    [importarProdutoNaTabelaPreco, setIsLoading]
  );
  const validarCSV = useCallback((arquivo: File) => {
    if (arquivo.type !== 'text/csv') {
      toast.warning('O arquivo está em um formato inválido.');
      return false;
    }
    return true;
  }, []);

  return {
    obterLojas,
    obterProdutosParaExportar,
    validarCSV,
    lerArquivoCSV,
    exportarProdutosComErro,
    importarProdutoNaTabelaPreco,
    idDoParametroRota,
    setIsLoading,
    isLoading,
  };
};
