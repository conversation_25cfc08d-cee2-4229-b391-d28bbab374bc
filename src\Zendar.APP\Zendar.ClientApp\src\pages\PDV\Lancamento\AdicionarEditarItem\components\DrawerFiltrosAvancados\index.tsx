import { useMedia<PERSON><PERSON>y, Grid<PERSON>tem, FormLabel } from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';

import {
  useConsultaProdutosPdvContext,
  Filtros,
} from 'store/PDV/ConsultaProdutoPdv';

import { useEnterKeyOnFields } from 'hooks/useEnterKeyOnFields';

import Input from 'components/PDV/Input';
import SelectPadrao from 'components/PDV/Select/SelectPadrao';
import { SelectCategoria } from 'components/Select/SelectCategoria';
import { SelectMulti } from 'components/Select/SelectMultiCheckbox';
import { DrawerConsultaProdutos } from 'components/v2/ConsultaProdutos/Drawer';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import StatusConsultaEnum from 'constants/enum/statusConsulta';
import TipoFiltroProdutoEstoqueEnum from 'constants/enum/tipoFiltroProdutoEstoque';

type OptionsProps = {
  label: string;
  value: string;
};

type OptionsResponseProps = {
  id: string;
  nome: string;
};

type ConsultaProdutosPdvProps = {
  filtersSubmit: (filters: Filtros) => void;
  isOpen: boolean;
  onClose: () => void;
};

export const DrawerFiltrosAvancados = ({
  filtersSubmit,
  isOpen,
  onClose,
}: ConsultaProdutosPdvProps) => {
  const [cores, setCores] = useState<OptionsProps[]>([]);
  const [tamanhos, setTamanhos] = useState<OptionsProps[]>([]);
  const [marcas, setMarcas] = useState<OptionsProps[]>([]);
  const { isLoading, setIsLoading, isLoadingPesquisa } =
    useConsultaProdutosPdvContext();

  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
  const [isLargerThan1200] = useMediaQuery('(min-width: 1200px)');

  const formMethods = useForm<Filtros>({
    defaultValues: {
      nome: '',
      sku: '',
      codigoBarrasEtiqueta: '',
      codigoBarrasFornecedor: '',
      skuIdentificadorReferencia: '',
      tipoEstoque: TipoFiltroProdutoEstoqueEnum.produtos[0].value,
      cores: null,
      tamanhos: null,
      categorias: null,
      marcas: null,
    },
  });

  const { handleSubmit: submit, reset } = formMethods;

  const handleSubmit = submit(() => {
    const filtros = formMethods.watch();
    filtersSubmit(filtros);
    onClose();
  });

  const handleLimparPesquisa = () => {
    reset({
      nome: '',
      sku: '',
      codigoBarrasEtiqueta: '',
      codigoBarrasFornecedor: '',
      skuIdentificadorReferencia: '',
      tipoEstoque: TipoFiltroProdutoEstoqueEnum.produtos[0].value,
      cores: null,
      tamanhos: null,
      categorias: null,
      marcas: null,
    });
  };

  const preencherOpcoes = useCallback(
    (
      response: ResponseApi<OptionsResponseProps[]>,
      setStateValue: (value: React.SetStateAction<OptionsProps[]>) => void
    ) => {
      if (response) {
        if (response.avisos) {
          response.avisos.forEach((item: string) => toast.warning(item));
        }

        if (response.sucesso) {
          setStateValue(
            response.dados.map((item) => {
              return {
                label: item.nome,
                value: item.id,
              };
            })
          );
        }
      }
    },
    []
  );

  const getCores = useCallback(async () => {
    const response = await api.get<void, ResponseApi<OptionsResponseProps[]>>(
      ConstanteEnderecoWebservice.COR_LISTAR_SELECT,
      {
        params: {
          statusConsulta: StatusConsultaEnum.TODOS,
          listarPadraoSistema: true,
        },
      }
    );

    return response;
  }, []);

  const getTamanhos = useCallback(async () => {
    const response = await api.get<void, ResponseApi<OptionsResponseProps[]>>(
      ConstanteEnderecoWebservice.TAMANHO_LISTAR_SELECT,
      {
        params: {
          statusConsulta: StatusConsultaEnum.TODOS,
          listarPadraoSistema: true,
        },
      }
    );

    return response;
  }, []);

  const getMarcas = useCallback(async () => {
    const response = await api.get<void, ResponseApi<OptionsResponseProps[]>>(
      ConstanteEnderecoWebservice.MARCA_LISTAR_SELECT,
      {
        params: {
          statusconsulta: 1,
        },
      }
    );

    return response;
  }, []);

  const buscarOpcoes = useCallback(async () => {
    setIsLoading(true);
    const promises = [getCores(), getTamanhos(), getMarcas()];
    const [coresResponse, tamanhosResponse, marcasResponse] = await Promise.all(
      promises
    );
    preencherOpcoes(coresResponse, setCores);
    preencherOpcoes(tamanhosResponse, setTamanhos);
    preencherOpcoes(marcasResponse, setMarcas);
    setIsLoading(false);
  }, [preencherOpcoes, getCores, getTamanhos, getMarcas]);

  useEffect(() => {
    buscarOpcoes();
  }, [buscarOpcoes]);

  useEnterKeyOnFields(() => {
    if (isLoading) return;
    setTimeout(() => {
      handleSubmit();
    }, 100);
  }, [
    'nome',
    'sku',
    'codigoBarrasEtiqueta',
    'codigoBarrasFornecedor',
    'skuIdentificadorReferencia',
    'tipoEstoque',
    'categorias',
    'marcas',
  ]);

  return (
    <DrawerConsultaProdutos
      key="drawerFiltrosConsultaProdutosPdv"
      isOpen={isOpen}
      isLoading={isLoading}
      isLoadingPesquisa={isLoadingPesquisa}
      isLargerThan900={isLargerThan900}
      isLargerThan1200={isLargerThan1200}
      formMethods={formMethods}
      onClose={onClose}
      handleLimparPesquisa={handleLimparPesquisa}
      handleSubmit={handleSubmit}
    >
      <GridItem colSpan={12}>
        <Input
          id="nome"
          name="nome"
          placeholder="Digite o nome do produto"
          maxLength={50}
          label="Descrição do produto"
          autoFocus
        />
      </GridItem>
      <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
        <Input
          id="sku"
          placeholder="Digite um valor"
          maxLength={50}
          label="Código SKU"
          name="sku"
        />
      </GridItem>
      <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
        <Input
          id="skuIdentificadorReferencia"
          placeholder="Digite um valor"
          maxLength={50}
          label="Código/Referência"
          name="skuIdentificadorReferencia"
          helperText="Informe o código SKUIdentificador ou a referência do produto."
        />
      </GridItem>
      <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
        <Input
          id="codigoBarrasFornecedor"
          placeholder="Digite um valor"
          maxLength={50}
          label="Código de barras"
          name="codigoBarrasFornecedor"
          helperText="Informe o GTIN/EAN ou código externo do produto."
        />
      </GridItem>
      <GridItem colSpan={{ base: 12, md: 6, lg: 6 }}>
        <Input
          id="codigoBarrasEtiqueta"
          placeholder="Digite um valor"
          maxLength={50}
          label="Código da etiqueta"
          name="codigoBarrasEtiqueta"
          helperText="Informe o código da etiqueta padrão ou reduzido."
        />
      </GridItem>

      <GridItem colSpan={12}>
        <SelectPadrao
          label="Estoque"
          required
          id="tipoEstoque"
          placeholder="Clique aqui para selecionar"
          name="tipoEstoque"
          options={TipoFiltroProdutoEstoqueEnum.produtos}
        />
      </GridItem>
      <GridItem colSpan={12}>
        <FormLabel mb="1px" fontSize="sm" color="black" lineHeight="1.2">
          Cores
        </FormLabel>
        <SelectMulti
          name="cores"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleSubmit();
            }
          }}
          changeBackgroundOption
          optionBackgroundColor="teal.600"
          optionColor="yellow.400"
          textLabelSelectAll="Todas as cores"
          id="cores"
          placeholder={
            isLargerThan900
              ? 'Digite o nome da cor e clique para selecionar'
              : 'Selecione as cores'
          }
          variant="outline"
          options={cores}
          isMulti
          isSearchable={false}
          closeMenuOnSelect={false}
        />
      </GridItem>
      <GridItem colSpan={12}>
        <FormLabel mb="1px" fontSize="sm" color="black" lineHeight="1.2">
          Tamanhos
        </FormLabel>
        <SelectMulti
          name="tamanhos"
          textLabelSelectAll="Todos os tamanhos"
          id="tamanhos"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleSubmit();
            }
          }}
          changeBackgroundOption
          optionBackgroundColor="pink.700"
          optionColor="yellow.400"
          placeholder={
            isLargerThan900
              ? 'Digite o tamanho e clique para selecionar'
              : 'Seleciones os tamanhos'
          }
          variant="outline"
          options={tamanhos}
          isMulti
          isSearchable={false}
          closeMenuOnSelect={false}
        />
      </GridItem>
      <GridItem colSpan={12}>
        <FormLabel mb="1px" fontSize="sm" color="black" lineHeight="1.2">
          Categoria
        </FormLabel>
        <SelectCategoria
          name="categorias"
          placeholder={
            isLargerThan900
              ? 'Digite o nome da categoria e clique para selecionar'
              : 'Selecione as categorias'
          }
        />
      </GridItem>
      <GridItem colSpan={12}>
        <FormLabel mb="1px" fontSize="sm" color="black" lineHeight="1.2">
          Marca
        </FormLabel>
        <SelectMulti
          name="marcas"
          textLabelSelectAll="Todas as marcas"
          id="marcas"
          placeholder={
            isLargerThan900
              ? 'Digite o nome da marca e clique para selecionar'
              : 'Selecione as marcas'
          }
          variant="outline"
          options={marcas}
          isMulti
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleSubmit();
            }
          }}
          isSearchable={false}
          closeMenuOnSelect={false}
        />
      </GridItem>
    </DrawerConsultaProdutos>
  );
};
