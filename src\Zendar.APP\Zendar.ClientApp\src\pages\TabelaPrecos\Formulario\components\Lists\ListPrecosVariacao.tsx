import {
  <PERSON>,
  Text,
  Stack,
  Button,
  Flex,
  <PERSON>,
  The<PERSON>,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON>ist,
  MenuItem,
} from '@chakra-ui/react';
import { useState, useCallback, useRef, ChangeEvent } from 'react';
import { useFieldArray } from 'react-hook-form';
import { RiArrowDropDownLine, RiArrowDropUpLine } from 'react-icons/ri';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';
import useIsMountedRef from 'helpers/layout/useIsMountedRef';

import api, { ResponseApi } from 'services/api';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { GridPaginadaRetorno } from 'components/Grid/Paginacao';
import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import { ModalConfirmacaoExcluir } from 'components/Modal/ModalConfirmacaoExcluir';
import { PaginationData } from 'components/update/Pagination';
import { ActionsMenu } from 'components/update/Table/ActionsMenu';
import {
  PagedTable,
  PagedTableForwardRefData,
} from 'components/update/Table/PagedTable';
import { InfoTooltip } from 'components/update/Tooltip/InfoTooltip';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import ConstanteFuncionalidades from 'constants/permissoes';
import {
  CarregarArquivoColetorIcon,
  OpcoesIcon,
  SalvarInserirNovoIcon,
} from 'icons';

import { TabelaPrecoHook } from '../../hooks';
import {
  FieldArrayType,
  ProdutoTabelaPrecoProps,
  TabelaPrecoCadastrarProps,
} from '../../types';
import {
  TabelaPrecoItensProps,
  TabelaPrecoResponseProps,
} from '../../validationForm';
import { ModalAdicionarProduto } from '../ModalAdicionarProduto';
import { ModalEditarProduto } from '../ModalEditarProduto';
import { ModalErrosImportacao } from '../ModalErrosImportacao';

import { useImportacaoCSV } from './hooks/useImportacaoCSV';

export const ListPrecosVariacao = ({
  isReadOnly,
  isAlterar,
  useTabelaPreco,
}: {
  isReadOnly: boolean;
  isAlterar: boolean;
  useTabelaPreco: () => TabelaPrecoHook;
}) => {
  const [totalRegistros, setTotalRegistros] = useState(0);

  const tabelaPrecoHook = useTabelaPreco();
  const { idDoParametroRota, setIsLoading, isLoading } = tabelaPrecoHook;

  const { casasDecimais } = usePadronizacaoContext();
  const { fields, remove, append, update, replace } =
    useFieldArray<FieldArrayType>({
      name: 'tabelaPrecoProdutoCorTamanhos',
    });

  const possuiPermissaoVisualizarPrecoCusto = auth.possuiPermissao(
    ConstanteFuncionalidades.USUARIO_VISUALIZAR_PRECO_CUSTO
  ).permitido;

  const buttonRef = useRef<HTMLButtonElement>(null);
  const pagedTableRef = useRef<PagedTableForwardRefData>(null);
  const pageIsLoaded = useRef(false);
  const isMountedRef = useIsMountedRef();

  const {
    modalErrosOpen,
    resultadoImportacao,
    inputRef,
    handleUploadFile,
    handleUploadCSV,
    handleExportarErros,
    handleCloseModalErros,
    handleImportarProdutosSucesso,
    exportProducts,
  } = useImportacaoCSV({
    tabelaPrecoHook,
    tabelaPrecoSimples: false,
    pagedTableRef,
  });

  const addProduct = useCallback(
    async (
      produtos: TabelaPrecoCadastrarProps[] | TabelaPrecoCadastrarProps
    ) => {
      setIsLoading(true);
      const produtoAdicionado = produtos as TabelaPrecoCadastrarProps;
      const newProduto =
        produtoAdicionado?.id === undefined ? produtos : [produtos];

      const response = await api.post<void, ResponseApi<string>>(
        `${ConstanteEnderecoWebservice.TABELA_PRECO_CADASTRAR_PRODUTO_LISTAGEM_PRODUTOS_COR_TAMANHO}/${idDoParametroRota}`,
        newProduto
      );
      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso: string) => toast.warning(aviso));
          setIsLoading(false);
          pagedTableRef.current?.reload();
          return true;
        }

        if (response.sucesso) {
          pagedTableRef.current?.reload();
          return true;
        }
        setIsLoading(false);
      }
      setIsLoading(false);
      return false;
    },
    [idDoParametroRota, setIsLoading]
  );

  const handleAddProduct = useCallback(async () => {
    const { produto: newProduto, deveReiniciar } = await ModalAdicionarProduto({
      casasDecimaisValor: casasDecimais.casasDecimaisValor,
      fields,
      isAlterar,
      isAddingToVariationList: true,
    });
    if (isAlterar) {
      await addProduct(
        newProduto.map((produtoItem) => ({
          id: produtoItem.produtoCorTamanhoId || '',
          valor: produtoItem.precoVenda || 0,
        }))
      );
    } else {
      newProduto.forEach((produtoItem) => {
        const produtoJaAdicionado = fields.some(
          (fieldsItem) =>
            fieldsItem.produtoCorTamanhoId === produtoItem.produtoCorTamanhoId
        );
        const indexFields = fields.findIndex(
          (fieldsItem) =>
            fieldsItem.produtoCorTamanhoId === produtoItem.produtoCorTamanhoId
        );
        if (produtoJaAdicionado) {
          update(indexFields, produtoItem);
        } else {
          append(produtoItem);
        }
      });
    }
    if (deveReiniciar) {
      if (buttonRef?.current) {
        buttonRef?.current?.click();
      }
    }
  }, [addProduct, append, casasDecimais, fields, isAlterar, update]);

  const editProduct = useCallback(async (data: ProdutoTabelaPrecoProps) => {
    const response = await api.put<void, ResponseApi<string>>(
      ConstanteEnderecoWebservice.TABELA_PRECO_ALTERAR_PRODUTO_LISTAGEM_PRODUTOS_COR_TAMANHO,
      data
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso: string) => toast.warning(aviso));
      }

      if (response.sucesso) {
        toast.success('Produto foi alterado com sucesso');
      }
    }
  }, []);

  const handleEditProduct = async (
    index: number,
    produtoCorTamanhoId: string
  ) => {
    const field = fields[index];

    const { precoVenda, markup } = await ModalEditarProduto({
      casasDecimaisValor: casasDecimais.casasDecimaisValor,
      nomeProduto: field.produtoNome,
      corDescricao: field.corDescricao,
      tamanhoDescricao: field.tamanhoDescricao,
      precoVendaAtual: field.precoVenda,
      precoCusto: field.precoCusto,
    });

    if (isAlterar) {
      await editProduct({
        tabelaPrecoId: idDoParametroRota,
        precoVenda,
        produtoCorTamanhoId,
      });
      pagedTableRef.current?.reload();
      return;
    }

    update(index, { ...field, precoVenda, markup });
  };

  const deleteProduct = useCallback(async (data: ProdutoTabelaPrecoProps) => {
    const response = await api.delete<void, ResponseApi<string>>(
      `${ConstanteEnderecoWebservice.TABELA_PRECO_EXCLUIR_PRODUTO_LISTAGEM_PRODUTOS_COR_TAMANHO}?tabelaPrecoId=${data.tabelaPrecoId}&produtoCorTamanhoId=${data.produtoCorTamanhoId}`
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso: string) => toast.warning(aviso));
      }

      if (response.sucesso) {
        toast.success('Produto deletado com sucesso');
      }
    }
  }, []);

  const handleRemoveProduct = (index: number, produtoCorTamanhoId: string) => {
    ModalConfirmacaoExcluir({
      callback: async (ok: boolean) => {
        if (ok) {
          if (isAlterar) {
            await deleteProduct({
              produtoCorTamanhoId,
              tabelaPrecoId: idDoParametroRota,
            });
            pagedTableRef.current?.reload();
            return;
          }
          remove(index);
        }
      },
    });
  };

  const getListItens = useCallback(
    async (gridPaginadaConsulta: PaginationData) => {
      const isTelaCadastro = !idDoParametroRota;
      if (isTelaCadastro) {
        return;
      }

      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<TabelaPrecoResponseProps>>
      >(
        formatQueryPagegTable(
          ConstanteEnderecoWebservice.TABELA_PRECO_LISTAR_PAGINADO_LISTAGEM_PRODUTOS_COR_TAMANHO,
          {
            ...gridPaginadaConsulta,
            orderColumn: '',
            orderDirection: '',
          }
        ),
        { params: { id: idDoParametroRota } }
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso: string) => toast.warning(aviso));
        }
        if (response.sucesso) {
          const newData = response.dados.registros.map((itemTabelaPreco) => {
            const { precoCusto, precoVenda } = itemTabelaPreco;
            const markup =
              precoCusto && precoVenda
                ? (precoVenda / precoCusto - 1) * 100
                : 0;

            return {
              ...itemTabelaPreco,
              produtoNome: itemTabelaPreco.nome,
              corDescricao: itemTabelaPreco?.cor?.descricao,
              tamanhoDescricao: itemTabelaPreco?.tamanho?.descricao,
              markup,
            } as TabelaPrecoItensProps;
          });
          replace(newData);

          setTotalRegistros(response.dados.total);
          setIsLoading(false);
        }
      }

      if (isMountedRef.current) {
        setIsLoading(false);

        if (!pageIsLoaded.current) {
          pageIsLoaded.current = true;
        }
      }
    },
    [idDoParametroRota, isMountedRef, replace, setIsLoading]
  );

  return (
    <>
      {isLoading && <LoadingPadrao />}
      <Box w="full">
        <HStack spacing="1" mb="1">
          <Text
            as="label"
            lineHeight="none"
            fontSize="sm"
            fontWeight="semibold"
            mb="0"
          >
            Preços especiais por variação
          </Text>
          <InfoTooltip
            bg="black"
            padding="8px"
            pl="12px"
            borderRadius="8px"
            label="O sistema irá verificar se existe um preço diferente adicionado para uma determinada variação e utilizará este valor no momento da venda, desconsiderando as regras acima"
          />
        </HStack>

        <Box
          borderRadius="md"
          bg="gray.50"
          border="1px"
          borderColor="gray.100"
          p={{ base: 4, sm: 6, md: 8 }}
        >
          <Stack
            direction={[
              'column-reverse',
              'column-reverse',
              'column-reverse',
              'row',
            ]}
            justifyContent={isReadOnly ? 'flex-end' : 'space-between'}
            mb="4"
          >
            <Flex direction={['column', 'column', 'column', 'row']}>
              {!isReadOnly && (
                <Button
                  borderRadius="md"
                  ref={buttonRef}
                  colorScheme="secondary"
                  leftIcon={<Icon as={SalvarInserirNovoIcon} fontSize="lg" />}
                  onClick={() => handleAddProduct()}
                >
                  Adicionar produto
                </Button>
              )}

              {isAlterar && (
                <>
                  <Box>
                    <Button
                      variant="outlineDefault"
                      colorScheme="gray"
                      mt={['10px', '10px', '10px', '0']}
                      mb={['10px', '10px', '10px', '0']}
                      mr={['0', '0', '0', '20px']}
                      ml={['0', '0', '0', '20px']}
                      borderRadius="md"
                      border="1px solid #909090"
                      w={['full', 'full', 'full', '200px']}
                      leftIcon={
                        <Icon as={CarregarArquivoColetorIcon} fontSize="lg" />
                      }
                      onClick={handleUploadFile}
                    >
                      Importar produtos
                    </Button>
                  </Box>

                  <Menu>
                    {({ isOpen }) => (
                      <>
                        <MenuButton
                          variant="outlineDefault"
                          colorScheme="gray"
                          isActive={isOpen}
                          color={isOpen ? 'white' : 'gray.700'}
                          as={Button}
                          w={['full', 'full', 'full', '200px']}
                          rightIcon={
                            isOpen ? (
                              <Icon as={RiArrowDropDownLine} h={7} w={7} />
                            ) : (
                              <Icon as={RiArrowDropUpLine} h={7} w={7} />
                            )
                          }
                          leftIcon={<OpcoesIcon />}
                          borderRadius="md"
                          border="1px solid #909090"
                        >
                          <Text paddingLeft="2" textAlign="center">
                            Opções gerais
                          </Text>
                        </MenuButton>
                        <MenuList>
                          <MenuItem onClick={() => exportProducts(true)}>
                            <Text ml="4" fontSize="sm">
                              Exportar todos os produtos
                            </Text>
                          </MenuItem>
                          <MenuItem onClick={() => exportProducts(false)}>
                            <Text ml="4" fontSize="sm">
                              Exportar produtos da tabela de preço
                            </Text>
                          </MenuItem>
                        </MenuList>
                      </>
                    )}
                  </Menu>
                </>
              )}
            </Flex>
            <Flex alignItems="center">
              <Text fontSize="sm" fontWeight="semibold">
                Informe valores especiais para variações específicas dos
                produtos
              </Text>
            </Flex>
          </Stack>

          <input
            ref={inputRef}
            accept=".csv"
            type="file"
            value=""
            style={{ display: 'none' }}
            onChange={(e: ChangeEvent<HTMLInputElement>) => {
              const { files } = e.target;
              if (!files || files.length === 0) {
                return;
              }

              const newFile = files[0];

              if (newFile) {
                handleUploadCSV(newFile);
              }
            }}
          />

          <Box
            bg="white"
            borderRadius="md"
            boxShadow="md"
            position="relative"
            overflow="auto"
            sx={{
              '&::-webkit-scrollbar': {
                w: '0',
                h: '0',
              },
            }}
          >
            {isAlterar ? (
              <PagedTable
                ref={pagedTableRef}
                loadColumnsData={getListItens}
                itemsTotalCount={totalRegistros}
                defaultKeyOrdered="nome"
                tableHeaders={[
                  {
                    key: 'Produto',
                    content: 'Produto',
                    w: 'auto',
                    isOrderable: false,
                  },
                  {
                    key: 'tamanho',
                    content: 'Tamanho',
                    w: '120px',
                    minW: '120px',
                    isOrderable: false,
                  },
                  {
                    key: 'precoVendas',
                    content: 'Preço de venda',
                    isOrderable: false,
                    w: '10%',
                    isNumeric: true,
                  },
                  possuiPermissaoVisualizarPrecoCusto
                    ? {
                        key: 'custo',
                        content: 'Custo',
                        w: '10%',
                        isOrderable: false,
                        isNumeric: true,
                      }
                    : {
                        key: 'custo',
                        content: '',
                        w: '0%',
                        isOrderable: false,
                        isNumeric: true,
                      },
                  {
                    key: 'markup',
                    content: 'Markup',
                    w: '10%',
                    isOrderable: false,
                    isNumeric: true,
                  },
                  {
                    key: 'acoes',
                    content: 'Ações',
                    isOrderable: false,
                    w: '1px',
                  },
                ]}
                renderTableRows={
                  fields?.length > 0 ? (
                    fields?.map((field, index) => (
                      <Tr>
                        <Td w="auto">{`${field.produtoNome} ${field.corDescricao}`}</Td>
                        <Td minW="120px" w="120px">
                          {field.tamanhoDescricao || '-'}
                        </Td>
                        <Td isNumeric w="10%">
                          {field.precoVenda.toLocaleString('pt-BR', {
                            minimumFractionDigits:
                              casasDecimais.casasDecimaisValor,
                            maximumFractionDigits:
                              casasDecimais.casasDecimaisValor,
                          })}
                        </Td>
                        {possuiPermissaoVisualizarPrecoCusto ? (
                          <Td isNumeric w="10%">
                            {field.precoCusto.toLocaleString('pt-BR', {
                              minimumFractionDigits:
                                casasDecimais.casasDecimaisValor,
                              maximumFractionDigits:
                                casasDecimais.casasDecimaisValor,
                            })}
                          </Td>
                        ) : (
                          <Td />
                        )}
                        <Td isNumeric w="10%">
                          {`${field.markup.toLocaleString('pt-BR', {
                            minimumFractionDigits:
                              casasDecimais.casasDecimaisValor,
                            maximumFractionDigits:
                              casasDecimais.casasDecimaisValor,
                          })}%`}
                        </Td>
                        <Td w="1px">
                          <ActionsMenu
                            isDisabled={isReadOnly}
                            items={
                              isReadOnly
                                ? []
                                : [
                                    {
                                      content: 'Editar',
                                      onClick: () =>
                                        handleEditProduct(
                                          index,
                                          field.produtoCorTamanhoId
                                        ),
                                    },
                                    {
                                      content: 'Remover',
                                      onClick: () =>
                                        handleRemoveProduct(
                                          index,
                                          field.produtoCorTamanhoId
                                        ),
                                    },
                                  ]
                            }
                          />
                        </Td>
                      </Tr>
                    ))
                  ) : (
                    <Tr>
                      <Td whiteSpace="nowrap" colSpan={9999}>
                        Nenhum produto adicionado.
                      </Td>
                    </Tr>
                  )
                }
              />
            ) : (
              <Table variant="filled">
                <Thead>
                  <Tr>
                    <Th w="auto" whiteSpace="nowrap" userSelect="none">
                      Produto
                    </Th>
                    <Th
                      minW="120px"
                      w="120px"
                      whiteSpace="nowrap"
                      userSelect="none"
                    >
                      Tamanho
                    </Th>
                    <Th isNumeric w="10%" whiteSpace="nowrap" userSelect="none">
                      Preço de venda
                    </Th>
                    {possuiPermissaoVisualizarPrecoCusto ? (
                      <Th
                        isNumeric
                        w="10%"
                        whiteSpace="nowrap"
                        userSelect="none"
                      >
                        Custo
                      </Th>
                    ) : (
                      <Th w="0%" />
                    )}
                    <Th isNumeric w="10%" whiteSpace="nowrap" userSelect="none">
                      Markup
                    </Th>
                    <Th w="1px" whiteSpace="nowrap" userSelect="none">
                      Ações
                    </Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {(fields || [])?.length > 0 ? (
                    (fields || [])?.map((field, index) => (
                      <Tr>
                        <Td w="auto">{`${field.produtoNome} ${field.corDescricao}`}</Td>
                        <Td minW="120px" w="120px">
                          {field.tamanhoDescricao || '-'}
                        </Td>
                        <Td isNumeric w="10%">
                          {field.precoVenda.toLocaleString('pt-BR', {
                            minimumFractionDigits:
                              casasDecimais.casasDecimaisValor,
                            maximumFractionDigits:
                              casasDecimais.casasDecimaisValor,
                          })}
                        </Td>
                        {possuiPermissaoVisualizarPrecoCusto ? (
                          <Td isNumeric w="10%">
                            {field.precoCusto.toLocaleString('pt-BR', {
                              minimumFractionDigits:
                                casasDecimais.casasDecimaisValor,
                              maximumFractionDigits:
                                casasDecimais.casasDecimaisValor,
                            })}
                          </Td>
                        ) : (
                          <Td />
                        )}
                        <Td isNumeric w="10%">
                          {`${field.markup.toLocaleString('pt-BR', {
                            minimumFractionDigits:
                              casasDecimais.casasDecimaisValor,
                            maximumFractionDigits:
                              casasDecimais.casasDecimaisValor,
                          })}%`}
                        </Td>
                        <Td w="1px">
                          <ActionsMenu
                            isDisabled={isReadOnly}
                            items={
                              isReadOnly
                                ? []
                                : [
                                    {
                                      content: 'Editar',
                                      onClick: () =>
                                        handleEditProduct(
                                          index,
                                          field.produtoCorTamanhoId
                                        ),
                                    },
                                    {
                                      content: 'Remover',
                                      onClick: () =>
                                        handleRemoveProduct(
                                          index,
                                          field.produtoCorTamanhoId
                                        ),
                                    },
                                  ]
                            }
                          />
                        </Td>
                      </Tr>
                    ))
                  ) : (
                    <Tr>
                      <Td whiteSpace="nowrap" colSpan={9999}>
                        Nenhum produto adicionado.
                      </Td>
                    </Tr>
                  )}
                </Tbody>
              </Table>
            )}
          </Box>
        </Box>
      </Box>

      {resultadoImportacao && (
        <ModalErrosImportacao
          isOpen={modalErrosOpen}
          onClose={handleCloseModalErros}
          produtosComErro={resultadoImportacao.produtosComErro}
          totalProcessados={resultadoImportacao.totalProcessados}
          exportarErros={handleExportarErros}
          produtosComSucesso={resultadoImportacao.produtosComSucesso}
          importarProdutosValidos={handleImportarProdutosSucesso}
        />
      )}
    </>
  );
};
