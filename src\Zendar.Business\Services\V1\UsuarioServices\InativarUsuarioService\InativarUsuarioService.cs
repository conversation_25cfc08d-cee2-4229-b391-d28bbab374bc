﻿using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.Business.Interfaces.Services;
using Zendar.Data.Enums;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;

namespace Zendar.Business.Services.V1.UsuarioServices.InativarUsuarioService
{
	public class InativarUsuarioService : BaseService, IInativarUsuarioService
	{
		private readonly IUsuarioRepository _usuarioRepository;
		private readonly UserManager<Usuario> _userManager;
		private readonly ILogAuditoriaService _logAuditoriaService;

		public InativarUsuarioService(
			INotificador notificador,
			IUsuarioRepository usuarioRepository,
			UserManager<Usuario> userManager,
			ILogAuditoriaService logAuditoriaService)
			: base(notificador)
		{
			_usuarioRepository = usuarioRepository;
			_userManager = userManager;
			_logAuditoriaService = logAuditoriaService;
		}

		public void Dispose()
		{
			_usuarioRepository?.Dispose();
			_userManager?.Dispose();
			_logAuditoriaService?.Dispose();
		}

		public async Task InativarMaisRecentes(TipoUsuario tipoUsuario, int quantidade)
		{
			IEnumerable<Guid> usuariosIds = await _usuarioRepository.ObterUsuariosMaisRecentesParaInativar(tipoUsuario, quantidade);

			foreach(var usuarioId in usuariosIds)
			{
				var user = await _userManager.FindByIdAsync(usuarioId.ToString());
				if (user is null) continue;

				user.Ativo = false;
				await _userManager.UpdateAsync(user);

				await _logAuditoriaService.Inserir(new ViewModels.LogAuditoriaInserirViewModel
				{
					Descricao = $"Inativando o usuário {user.Nome} após atualização das licenças",
					Tela = LogAuditoriaTela.USUARIO,
					Operacao = LogAuditoriaOperacao.ALTERAR,
					UsuarioNome = "Ação feita pelo sistema"
				});
			}
		}
	}
}
