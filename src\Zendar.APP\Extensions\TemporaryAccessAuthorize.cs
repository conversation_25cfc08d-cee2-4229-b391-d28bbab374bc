﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Interfaces.Services.MultiEmpresa;
using Zendar.Business.Services.TokenAcessoServices;

namespace Zendar.APP.Extensions
{
    public class TemporaryAccessAuthorizeAttribute : TypeFilterAttribute
    {
        public TemporaryAccessAuthorizeAttribute() : base(typeof(TokenAcessoTemporarioFilter))
        {
        }
    }

    public class TokenAcessoTemporarioFilter : IAsyncAuthorizationFilter
    {
        private readonly ITokenAcessoService _tokenAcessoService;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly ICacheService _cacheService;
        private readonly IClienteMultiEmpresaService _clienteMultiEmpresaService;

        public TokenAcessoTemporarioFilter(ITokenAcessoService tokenAcessoService,
                                           IAspNetUserInfo aspNetUserInfo,
                                           ICacheService cacheService,
                                           IClienteMultiEmpresaService clienteMultiEmpresaService)
        {
            _tokenAcessoService = tokenAcessoService;
            _aspNetUserInfo = aspNetUserInfo;
            _cacheService = cacheService;
            _clienteMultiEmpresaService = clienteMultiEmpresaService;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            var autorizacaoPadrao = new RequisitoClaimFilter(new(), false, _aspNetUserInfo, _cacheService, _clienteMultiEmpresaService);

            await autorizacaoPadrao.OnAuthorizationAsync(context);

            // Caso a solicitação vier do sistea e o usuário estivar autorizado, não se faz nada
            if (context.Result == null)
                return;

            var accessToken = context.HttpContext.Request.Headers.Where(x => x.Key == "temporary-access-token")
                                                                 .Select(x => x.Value)
                                                                 .FirstOrDefault().ToString();

            var recursoId = context.HttpContext.Request.Query.Where(x => x.Key == "id")
                                                                 .Select(x => x.Value)
                                                                 .FirstOrDefault().ToString();

            if (string.IsNullOrEmpty(recursoId) || string.IsNullOrEmpty(accessToken) || !await _tokenAcessoService.ValidarTokenAcessoTemporario(recursoId, accessToken))
            {
                context.Result = new StatusCodeResult(StatusCodes.Status403Forbidden);
                return;
            }

            // Usuario foi autorizado
            context.Result = null;
        }
    }
}
