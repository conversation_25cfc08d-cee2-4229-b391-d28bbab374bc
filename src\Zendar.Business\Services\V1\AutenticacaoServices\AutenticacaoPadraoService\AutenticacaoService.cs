﻿using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Multiempresa.Shared.Enums;
using Multiempresa.Shared.Helpers;
using Multiempresa.Shared.Interfaces.Hangfire;
using Multiempresa.Data.Repositories.FaturamentoRepositories;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Zendar.Business.Emails;
using Zendar.Business.Helpers;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Interfaces.Services.MultiEmpresa;
using Zendar.Business.Services.IntegracaoServices.IntegracaoService;
using Zendar.Business.SignalR;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Autenticacao;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoRepository;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;
using Zendar.Business.Services.V1.IntegracaoServices.SincronizacaoServices.Sincronizacao;

namespace Zendar.Business.Services.AutenticacaoServices.AutenticacaoPadraoService
{
    public class AutenticacaoService : AutenticacaoBaseService, IAutenticacaoService
    {
        private readonly UserManager<Usuario> _userManager;
        private readonly IMapper _mapper;
        private readonly IPerfilRepository _perfilRepository;
        private readonly IPermissaoRepository _permissaoRepository;
        private readonly IUsuarioPermissaoRepository _usuarioPermissaoRepository;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly ILojaRepository _lojaRepository;
        private readonly IHubContext<NotificationHubService> _hubContext;
        private readonly IDatabaseTransaction _databaseTransaction;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogAuditoriaService _logAuditoriaService;
        private readonly IStorageService _storageService;
        private readonly ILojaService _lojaService;
        private readonly IIntegracaoService _integracaoService;
        private readonly IClienteMultiEmpresaService _clienteMultiEmpresaService;
        private readonly IFaturamentoRepository _faturamentoRepository;

        public AutenticacaoService(UserManager<Usuario> userManager,
            SignInManager<Usuario> signInManager,
            IOptions<JwtSettings> jwtSettings,
            INotificador notificador,
            ICacheService cacheService,
            IIntegracaoRepository integracaoRepository,
            IMapper mapper,
            IPerfilRepository perfilRepository,
            IUsuarioRepository usuarioRepository,
            ILojaRepository lojaRepository,
            IAspNetUserInfo aspNetUserInfo,
            IHubContext<NotificationHubService> hubContext,
            IDatabaseTransaction databaseTransaction,
            IPermissaoRepository permissaoRepository,
            IStorageService storageService,
            IMemoryCache memoryCache,
            ILogAuditoriaService logAuditoriaService,
            IUsuarioPermissaoRepository usuarioPermissaoRepository,
            ILojaService lojaService,
            IClienteMultiEmpresaService clienteMultiEmpresaService,
            IIntegracaoService integracaoService,
            IFaturamentoRepository faturamentoRepository,
			ISincronizacaoIntegracaoService sincronizacaoIntegracaoService)
            : base(notificador, aspNetUserInfo, cacheService, integracaoRepository, signInManager, jwtSettings, sincronizacaoIntegracaoService)
        {
            _userManager = userManager;
            _mapper = mapper;
            _perfilRepository = perfilRepository;
            _usuarioRepository = usuarioRepository;
            _lojaRepository = lojaRepository;
            _hubContext = hubContext;
            _databaseTransaction = databaseTransaction;
            _permissaoRepository = permissaoRepository;
            _memoryCache = memoryCache;
            _storageService = storageService;
            _logAuditoriaService = logAuditoriaService;
            _usuarioPermissaoRepository = usuarioPermissaoRepository;
            _lojaService = lojaService;
            _clienteMultiEmpresaService = clienteMultiEmpresaService;
            _integracaoService = integracaoService;
            _faturamentoRepository = faturamentoRepository;
        }

        public override async Task LogOff()
        {
            await base.LogOff();

            // dispara a mensagem no hub
            var user = await _userManager.FindByIdAsync(_aspNetUserInfo.Id);
            if (user != null)
                await _hubContext.Clients.Group(user.SecurityStamp).SendAsync("logoff", ResourceMensagem.AutenticacaoService_UsuarioAlterado, new Guid());
        }

        public async Task ForceUserLogoutAsync(
            string userId,
            string logoutMessage,
            string securityStamp = null)
        {
            if (string.IsNullOrWhiteSpace(securityStamp))
            {
                var user = await _usuarioRepository
                    .GetByIdAsync(
                        Guid.Parse(userId),
                        u => new Usuario { SecurityStamp = u.SecurityStamp });

                if (user != null)
                    securityStamp = user.SecurityStamp;
            }

            if (!string.IsNullOrWhiteSpace(securityStamp))
            {
                await _hubContext.Clients.Group(securityStamp).SendAsync("logoff_obrigatorio", logoutMessage, new Guid());
                await _cacheService.RemoverAsync(userId);
            }
        }

        public async Task<UsuarioAutenticadoViewModel> Login(LoginViewModel loginViewModel)
        {
            if (!LoginValido(loginViewModel)) return new();

            var user = await _userManager.FindByNameAsync(loginViewModel.Usuario);
            if (!UsuarioValido(user)) return new();

            if (!await TryLogin(loginViewModel)) return new();

            if (await _cacheService.Existe(user.Id.ToString()))
            {
                if (loginViewModel.ExecutarLogOffSessaoParelela)
                {
                    await _hubContext.Clients.Group(user.SecurityStamp).SendAsync("logoff", ResourceMensagem.AutenticacaoService_SessaoDuplicada, Guid.NewGuid());
                }
                else
                {
                    return new UsuarioAutenticadoViewModel
                    {
                        PossuiOutraSessaoAtiva = true
                    };
                }
            }

            var usuario = await _usuarioRepository.ObterUsuarioPorUserNameComPermissoes(loginViewModel.Usuario);

            var lojaUsuarioViewModel = await ObterLojaUsuario(usuario);
            if (lojaUsuarioViewModel == null || lojaUsuarioViewModel.Id == Guid.Empty) return new();

            _databaseTransaction.BeginTransaction();

            bool primeiroLogin = user.VerificarPrimeiroAcesso();
            user.UpdateUltimoLogin();
            await _userManager.UpdateSecurityStampAsync(user);

            var usuarioAutenticado = GerarTokenSegurancaPreencherViewModel(usuario, user.SecurityStamp, lojaUsuarioViewModel.Id);
            usuarioAutenticado.Loja = lojaUsuarioViewModel;
            usuarioAutenticado.QtdLojas = await _lojaRepository.ObterQtdLojasUsuario(user.Id);
            usuarioAutenticado.Permissoes = PreencherPermissoesUsuario(usuario);
            usuarioAutenticado.PrimeiroLogin = primeiroLogin;

            await SetCacheUsuarioAutenticado(user.Id.ToString(),
                                             usuarioAutenticado.ToCacheViewModel());

            _databaseTransaction.Commit();

            return usuarioAutenticado;
        }

        /// <summary>
        /// obtem todas as lojas com serviço do tipo plano vinculadas ao usuário ordenando pela loja mais recente
        /// verifica se loginViewModel.LojaPadraoId é <> null e valida por ela
        /// se for null percorre um foreach para validar qual loja esta disponivel
        /// se nenhuma estiver, barra o login
        /// </summary>
        private async Task<LojaFantasiaViewModel> ObterLojaUsuario(Usuario usuario)
        {
            var lojas = usuario.LojaUsuarios.Select(x => x.Loja);

            LojaFantasiaViewModel lojaUsuarioViewModel = null;

            var lojaPadraoUsuario = usuario.LojaUsuarios.FirstOrDefault(l => l.LojaPadrao)?.Loja;
            if (lojaPadraoUsuario != null && !lojaPadraoUsuario.Bloqueada(DateTime.UtcNow))
            {
                lojaUsuarioViewModel = new LojaFantasiaViewModel
                {
                    Id = lojaPadraoUsuario.Id,
                    Fantasia = lojaPadraoUsuario.Fantasia,
                    DataBloqueio = lojaPadraoUsuario.LojaServicos.First(x => x.TipoServico == TipoServicoStargate.PLANO).DataBloqueio,
                    LinkCobranca = string.Empty,
                };
            }

            if (lojaUsuarioViewModel == null)
            {
                foreach (var loja in lojas)
                {
                    if (!loja.Bloqueada(DateTime.UtcNow))
                    {
                        lojaUsuarioViewModel = new LojaFantasiaViewModel
                        {
                            Id = loja.Id,
                            Fantasia = loja.Fantasia,
                            DataBloqueio = loja.LojaServicos.First(x => x.TipoServico == TipoServicoStargate.PLANO).DataBloqueio,
                            LinkCobranca = string.Empty,
                        };

                        break;
                    }
                }
            }

            if (lojaUsuarioViewModel == null) {
                if (lojas.Any())
                {
                    NotificarAviso(ResourceMensagem.LojaService_LojaBloqueada);
                }
                else
                {
                    NotificarAviso(ResourceMensagem.AutenticacaoService_UsuarioSemLoja);
                }
            }

            return lojaUsuarioViewModel;
        }

        public async Task<UsuarioAutenticadoViewModel> RefreshToken(RefreshTokenViewModel refreshTokenViewModel)
        {
            if (!RefreshTokenValido(refreshTokenViewModel)) return new();

            var loja = await _lojaService.ObterLojaComDataBloqueioPlano(refreshTokenViewModel.LojaId);
            if (loja.Bloqueada(DateTime.UtcNow))
            {
                NotificarAviso(ResourceMensagem.LojaService_LojaBloqueada);
                return new();
            }

            var (usuarioId, userName) = ObterInfoUsuarioDoToken(refreshTokenViewModel.Token);

            (var cacheValido, var usuarioAutenticadoCache) = await TryGetCacheUsuarioAutenticado(usuarioId.ToString());
            if (!cacheValido) return new();

            if (!TokenCacheValido(usuarioAutenticadoCache, refreshTokenViewModel)) return new();

            var usuario = await _usuarioRepository.ObterUsuarioPorUserNameSemPermissoes(userName);

            var usuarioAutenticado = GerarTokenSegurancaPreencherViewModel(usuario, usuarioAutenticadoCache.SecurityStamp, refreshTokenViewModel.LojaId);
            usuarioAutenticadoCache.Token = usuarioAutenticado.Token;

            await SetCacheUsuarioAutenticado(usuarioId.ToString(),
                                             usuarioAutenticadoCache);

            return usuarioAutenticado;
        }

        private UsuarioAutenticadoViewModel GerarTokenSegurancaPreencherViewModel(Usuario usuario, string securityStamp, Guid lojaId)
        {
            var identityClaims = ObterClaimsUsuario(usuario, securityStamp, lojaId);

            var usuarioAutenticado = GerarUsuarioAutenticadoViewModel(securityStamp);
            var token = CreateToken(identityClaims, usuarioAutenticado.ValidadeToken);

            usuarioAutenticado.SetToken(token);
            usuarioAutenticado.AbrirPdvAposLogin = usuario.AbrirPdvAposLogin;

            return usuarioAutenticado;
        }

        private ClaimsIdentity ObterClaimsUsuario(Usuario usuario, string securityStamp, Guid lojaId)
        {
            PreencherPermissaoMemoryCache();

            string fotoUrl = string.IsNullOrEmpty(usuario.Foto) ? "" : _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, usuario.Foto, 24);

            var planoContratacao = _lojaService.ObterPlano(lojaId).Result;
            var revendaId = _clienteMultiEmpresaService.ObterRevendaId(_aspNetUserInfo.CodigoContaEmpresa).Result;

            var claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, usuario.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.Email, usuario.Email ?? ""),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Nbf, ToUnixEpochDate(DateTime.UtcNow).ToString()),
                new Claim(JwtRegisteredClaimNames.Iat, ToUnixEpochDate(DateTime.UtcNow).ToString(), ClaimValueTypes.Integer64),

                new Claim(ClaimTypesCustom.UserId, usuario.Id.ToString()),
                new Claim(ClaimTypes.Name, usuario.Nome),
                new Claim(ClaimTypesCustom.CodigoContaEmpresa, _aspNetUserInfo.CodigoContaEmpresa),
                new Claim(ClaimTypesCustom.RevendaId, revendaId.ToString()),
                new Claim(ClaimTypesCustom.SecurityStamp, securityStamp),
                new Claim(ClaimTypesCustom.UserName, usuario.UserName),
                new Claim(ClaimTypesCustom.Plano, planoContratacao.ObterDescricao()),
                new Claim(ClaimTypesCustom.FotoUrl, fotoUrl),
                new Claim(ClaimTypesCustom.Administrador, usuario.Administrador.ToString()),
                new Claim(ClaimTypesCustom.Servicos, PreencherServicosUsuario(usuario))
            };

            var identityClaims = new ClaimsIdentity();
            identityClaims.AddClaims(claims);
            return identityClaims;
        }

        private List<TokenPermissao> PreencherPermissoesUsuario(Usuario usuario)
        {
            var permissoesCache = _memoryCache.Get<List<TokenPermissao>>(MemoryCacheKeys.Permissoes);

            if (permissoesCache == null)
            {
                PreencherPermissaoMemoryCache();
                permissoesCache = _memoryCache.Get<List<TokenPermissao>>(MemoryCacheKeys.Permissoes);
            }

            permissoesCache.ForEach(p => { p.Permissao = usuario.UsuarioPermissoes.Any(e => e.Permissao.Codigo == p.Funcionalidade); });

            return permissoesCache;
        }

        public string PreencherServicosUsuario(Usuario usuario)
        {
            var servicos = usuario.LojaUsuarios.SelectMany(l => l.Loja.LojaServicos.Select(s => new TokenServico
            {
                LojaId = l.Loja.Id,
                ReferenciaServico = s.ReferenciaServico
            })).ToList();

            #region Integracao Zoop
            var integracaoZoop = _integracaoService.ListarIntegracaoConfiguradaPorIdentificador(Data.Enums.Integracao.IdentificacaoIntegracao.STI3PAY).Result;
            foreach (var loja in integracaoZoop)
            {
                servicos.Add(new TokenServico { LojaId = loja, ReferenciaServico = Data.Enums.Stargate.ReferenciaServicoStargate.INTEGRACAO_ZOOP });
            }
            #endregion

            return JsonConvert.SerializeObject(servicos);
        }

        private void PreencherPermissaoMemoryCache()
        {
            var permissoesCache = _memoryCache.Get<List<TokenPermissao>>(MemoryCacheKeys.Permissoes);

            if (permissoesCache == null)
            {
                var permissoes = _permissaoRepository.ListarPermissoesComPlano();

                var listaTokenPermissao = permissoes.Select(permissao => new TokenPermissao
                {
                    Funcionalidade = permissao.Codigo,
                    Planos = permissao.PlanoPermissoes.Select(x => x.ReferenciaPlano).ToList()
                }).ToList();

                _memoryCache.Set(MemoryCacheKeys.Permissoes, listaTokenPermissao);
            }
        }

        public void Dispose()
        {
            _perfilRepository?.Dispose();
            _permissaoRepository?.Dispose();
            _lojaRepository?.Dispose();
            _databaseTransaction?.Dispose();
        }

        public async Task RecuperarSenha(string email)
        {
            var usuario = await _userManager.FindByEmailAsync(email);

            if (usuario == null)
            {
                NotificarAviso(ResourceMensagem.AutenticacaoService_EmailNaoEncontrado);
                return;
            }

            var token = await _userManager.GeneratePasswordResetTokenAsync(usuario);

            var emailEnvio = new EmailEnvio
            {
                Sistema = _aspNetUserInfo.Sistema.ObterDescricao(),
                Emails = new List<string> { email },
                Assunto = "Recuperação de senha",
                Corpo = EmailRecuperarSenha.MontarEmail(usuario.Nome, usuario.UserName,
                Formatter.FormatarUrlRecuperarSenha(_aspNetUserInfo.Url, usuario.UserName, token), _aspNetUserInfo.Sistema.ObterDescricao())
            };

            BackgroundJob.Enqueue<IEmailService>(x => x.Enviar(emailEnvio));
        }

        public async Task AlterarSenha(UsuarioResetSenhaViewModel usuarioResetSenhaViewModel)
        {
            var usuario = await _userManager.FindByNameAsync(usuarioResetSenhaViewModel.Usuario);

            if (usuario == null)
            {
                NotificarAvisoRegistroNaoEncontrado("usuário");
                return;
            }

            var result = await _userManager.ResetPasswordAsync(usuario,
                                                               usuarioResetSenhaViewModel.Token,
                                                               usuarioResetSenhaViewModel.NovaSenha);

            if (!result.Succeeded)
            {
                NotificarAviso(ResourceMensagem.AutenticacaoService_FalhaResetSenha);
            }
        }

        public async Task<string> ValidarPermissaoPorUsuario(UsuarioLiberarPermissaoViewModel usuarioPermissaoViewModel)
        {
            var sigInResult = await _signInManager.PasswordSignInAsync(usuarioPermissaoViewModel.Usuario, usuarioPermissaoViewModel.Senha,
                                                                        isPersistent: false, lockoutOnFailure: false);
            if (sigInResult.Succeeded)
            {
                var usuario = await _userManager.FindByNameAsync(usuarioPermissaoViewModel.Usuario);
                var usuarioPermissao = await _usuarioPermissaoRepository.ObterUsuarioPermissao(usuario.Id, usuarioPermissaoViewModel.Permissoes);
                if (usuarioPermissao != null)
                {
                    await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel
                    {
                        Tela = usuarioPermissaoViewModel.Tela,
                        Operacao = LogAuditoriaOperacao.AUTORIZAR_ACAO,
                        Descricao = $"Ação: {usuarioPermissao.Permissao.PermissaoGrupo.Descricao} - {usuarioPermissao.Permissao.Nome}",
                        UsuarioId = usuario.Id,
                        UsuarioNome = usuario.Nome
                    });

                    var chavePermissaoTemporaria = Guid.NewGuid().ToString();

                    await _cacheService.AdicionarAsync(
                        new CacheModel
                        {
                            Chave = chavePermissaoTemporaria,
                            Valor = usuarioPermissaoViewModel.Permissoes,
                            ExpirarAposInatividade = TimeSpan.FromHours(1)
                        });

                    return chavePermissaoTemporaria;
                }
            }
            else
            {
                NotificarAviso(ResourceMensagem.AutenticacaoService_UsuarioSenhaInvalidos);
            }

            return null;
        }
    }
}
