import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import api, { ResponseApi } from 'services/api';
import {
  buscarPromocaoAtiva,
  getCanalVendasTray,
  obterEtapaAtual,
} from 'services/tray';

import { telasExibicaoPromocao } from 'pages/Promocao/Formulario/constants';

import { obterDadosIntegracaoTray } from 'api/Tray/ObterDadosIntegração';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { enumIdentificacaoIntegracao } from 'constants/enum/enumIdentificacaoIntegracao';
import enumMesesDoAno from 'constants/enum/enumMesesDoAno';
import { enumTabsConfiguracaoTray } from 'constants/enum/enumTabsConfiguracaoTray';
import { IdentificacaoEtapasTray } from 'constants/enum/IdentificacaoEtapasTray';
import enumReferenciaServicoStargate from 'constants/enum/referenciaServicoStargate';
import ConstanteRotas, { ConstanteRotasAlternativas } from 'constants/rotas';

import { Configuracoes } from './types';

type TrayPainelControleProps = {
  nomeCanalVenda: string;
  dadosTray: DadosTrayProps;
  getTray: () => Promise<void>;
  setIsLoading: (value: boolean) => void;
  isLoading: boolean;
  getTotalizador: () => Promise<Totalizadores | null>;
  mesAtual?: {
    label: string;
    value: number;
  };
  isLoadingTotalizadores: boolean;
  avisoProcessoEmAndamento: {
    textoAviso: string;
    exibirAviso: boolean;
    exibirNaTab: number | null;
  };
  promocaoAtiva: PromocaoProps | null;
};

export type DadosTrayProps = {
  id: string;
  dataAtivacao: Date;
  ativo: boolean;
  sincronizacaoHabilitada: boolean;
  configuracoes: string;
};

type TrayPainelControleProviderProps = {
  children: ReactNode;
};

export const TrayPainelControleContext = createContext(
  {} as TrayPainelControleProps
);

type Totalizadores = {
  descricaoProdutoMaisVendido: string;
  faturamento: number;
  ticketMedio: number;
  quantidadeVendas: number;
};

type PromocaoProps = {
  id: string;
  nome: string;
  periodoVigencia: {
    periodoInicio: string;
    periodoFim: string;
  };
};

export const TrayPainelControleProvider = ({
  children,
}: TrayPainelControleProviderProps) => {
  const [nomeCanalVenda, setNomeCanalVenda] = useState('');
  const [dadosTray, setDadosTray] = useState<DadosTrayProps>({
    sincronizacaoHabilitada: true,
  } as DadosTrayProps);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingTotalizadores, setIsLoadingTotalizadores] = useState(false);
  const [avisoProcessoEmAndamento, setAvisoProcessoEmAndamento] = useState<{
    textoAviso: string;
    exibirAviso: boolean;
    exibirNaTab: number | null;
  }>({
    textoAviso: '',
    exibirAviso: false,
    exibirNaTab: null,
  });
  const [promocaoAtiva, setPromocaoAtiva] = useState<PromocaoProps | null>(
    null
  );

  const valueMonthAtual = new Date().getMonth();

  const buscarPromocoesIntegracao = useCallback(async () => {
    const response = await api.get<void, ResponseApi<PromocaoProps[]>>(
      `${ConstanteEnderecoWebservice.LISTAR_SELECT_PROMOCAO}?telaUsoPromocao=${telasExibicaoPromocao.TRAY}`
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((item: string) => toast.warning(item));
      }

      if (response.sucesso && response.dados) {
        const promocoesApi = response.dados.map((promocao) => {
          return {
            nome: promocao.nome,
            id: promocao.id,
            periodoVigencia: {
              periodoInicio: promocao?.periodoVigencia?.periodoInicio,
              periodoFim: promocao?.periodoVigencia?.periodoFim,
            },
          };
        });
        return promocoesApi;
      }
    }
    return [];
  }, []);

  const buscarInformacoesPromocaoAtiva = useCallback(async () => {
    const listaPromocoesComTray = await buscarPromocoesIntegracao();
    const idPromocaoAtivaNaTray = await buscarPromocaoAtiva();

    const promocaoAtiva = listaPromocoesComTray?.find(
      (promocao) => promocao.id === idPromocaoAtivaNaTray
    );

    setPromocaoAtiva(promocaoAtiva?.id ? promocaoAtiva : null);
  }, [buscarPromocoesIntegracao, buscarPromocaoAtiva]);

  const checarAvisosProcessosEmAndamento = useCallback(
    (dados: DadosTrayProps) => {
      if (dados && dados.configuracoes) {
        try {
          const configuracoesValidas: Configuracoes = JSON.parse(
            dados.configuracoes.replace(/\\/g, '')
          );
          const possuiAviso =
            configuracoesValidas.AtualizacaoTabelaPreco ||
            configuracoesValidas.AtualizacaoPromocao;

          const tabAtualizando = configuracoesValidas.AtualizacaoTabelaPreco
            ? enumTabsConfiguracaoTray.TABELA_PRECO
            : enumTabsConfiguracaoTray.PROMOCAO;

          const textoAtualizacao = configuracoesValidas.AtualizacaoTabelaPreco
            ? 'tabela de preços'
            : 'promoção';

          setAvisoProcessoEmAndamento({
            textoAviso: possuiAviso
              ? `Atualizando a ${textoAtualizacao}. Enviaremos uma notificação assim que o processo estiver concluído.`
              : '',
            exibirAviso: possuiAviso,
            exibirNaTab: possuiAviso ? tabAtualizando : null,
          });
          return;
        } catch (error) {
          setAvisoProcessoEmAndamento({
            textoAviso: '',
            exibirAviso: false,
            exibirNaTab: null,
          });
        }
      } else {
        setAvisoProcessoEmAndamento({
          textoAviso: '',
          exibirAviso: false,
          exibirNaTab: null,
        });
      }
    },
    []
  );

  const mesAtual = enumMesesDoAno.properties.find(
    (mes) => mes?.value === Number(valueMonthAtual + 1 || 0)
  );

  const history = useHistory();

  const possuiPermissaoTray = auth.possuiServico(
    enumReferenciaServicoStargate.INTEGRACAO_TRAY
  ).permitido;

  const getCanalVendas = useCallback(async () => {
    const response = await getCanalVendasTray<{ nomeCanalVenda: string }>();

    if (response !== null) {
      setNomeCanalVenda(response.nomeCanalVenda);
    }
  }, []);

  const obterEtapaTray = useCallback(async () => {
    if (possuiPermissaoTray) {
      const response = await obterEtapaAtual();
      const naoFinalizada =
        response !== IdentificacaoEtapasTray.ETAPA_FINALIZADA;

      if (response && naoFinalizada)
        history.push(ConstanteRotasAlternativas.TRAY_ETAPAS);

      return;
    }

    if (!dadosTray) {
      history.push(ConstanteRotas.INTEGRACAO_TRAY_TELA_COMERCIAL);
      return;
    }

    history.push(ConstanteRotas.INTEGRACAO_TRAY_DETALHES);
  }, [dadosTray, history, possuiPermissaoTray]);

  const getTray = useCallback(async () => {
    setIsLoading(true);
    const response = await obterDadosIntegracaoTray();

    if (response?.sucesso) {
      await buscarInformacoesPromocaoAtiva();
      checarAvisosProcessosEmAndamento(response.dados);
      setDadosTray(response.dados);
    }

    setIsLoading(false);
  }, [checarAvisosProcessosEmAndamento]);

  const getTotalizador = useCallback(async () => {
    setIsLoadingTotalizadores(true);
    const response = await api.get<void, ResponseApi<Totalizadores>>(
      ConstanteEnderecoWebservice.INTEGRACAO_TRAY_TOTAL_VENDAS,
      {
        params: {
          identificacaoIntegracao: enumIdentificacaoIntegracao.TRAY,
          mes: mesAtual?.value,
        },
      }
    );

    if (response) {
      if (response.sucesso) {
        setIsLoadingTotalizadores(false);
        return response.dados;
      }
    }
    setIsLoadingTotalizadores(false);
    return null;
  }, [mesAtual?.value, setIsLoadingTotalizadores]);

  useEffect(() => {
    getCanalVendas();
  }, [getCanalVendas]);

  useEffect(() => {
    obterEtapaTray();
  }, [obterEtapaTray]);

  useEffect(() => {
    getTray();
  }, [getTray]);

  return (
    <TrayPainelControleContext.Provider
      value={{
        nomeCanalVenda,
        dadosTray,
        getTray,
        setIsLoading,
        isLoading,
        getTotalizador,
        isLoadingTotalizadores,
        mesAtual,
        avisoProcessoEmAndamento,
        promocaoAtiva,
      }}
    >
      {children}
    </TrayPainelControleContext.Provider>
  );
};

export function useTrayPainelControleContext(): TrayPainelControleProps {
  const context = useContext(TrayPainelControleContext);

  if (!context)
    throw new Error(
      'useTrayPainelControleContext must be used within a TrayPainelControleProvider.'
    );

  return context;
}
