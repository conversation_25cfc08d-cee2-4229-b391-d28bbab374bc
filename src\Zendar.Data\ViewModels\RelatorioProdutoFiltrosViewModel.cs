﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;

#nullable enable
namespace Zendar.Data.ViewModels
{
    public class RelatorioProdutoFiltrosViewModel
    {
        public Guid? IdRelatorioPersonalizado { get; set; }
        public TipoRelatorioPadrao? TipoRelatorio { get; set; }
        [Description("Produto")]
        public Guid? ProdutoId { get; set; }
        [Description("Tipo estoque")]
        public TipoFiltroProdutoEstoque TipoEstoque { get; set; }
        [Description("Status")]
        public StatusConsulta StatusConsulta { get; set; }
        public List<Guid>? Tamanhos { get; set; }
        public List<Guid>? Cores { get; set; }
        public List<Guid>? CategoriasProduto { get; set; }
        public List<Guid>? Marcas { get; set; }
        public List<Guid>? Tags { get; set; }
        public CampoPersonalizadoValorViewModel[]? CamposPersonalizados { get; set; }
        public Guid? TabelaPrecoId { get; set; }
        public Guid[]? ContasFinanceirasId { get; set; }
        public Guid? VendedorId { get; set; }
        [Description("Data emissão início")]
        public DateTime? DataEmissaoInicio { get; set; } // Data da emissão da operação início
        [Description("Data emissão fim")]
        public DateTime? DataEmissaoFim { get; set; } // Data da emissão da operação fim
        [Description("Cliente")]
        public Guid? ClienteFornecedorId { get; set; }
        public int? NumeracaoComandaInicial { get; set; }
        public int? NumeracaoComandaFinal { get; set; }
        public TipoAgrupamentoItensVendidos TipoAgrupamento { get; set; }
        [Description("Local Estoque")]
        public List<Guid> LocalEstoqueIds { get; set; } = new();
        [Description("Tipo Fiscal")]
        public List<TipoFiscal>? TipoFiscal { get; set; } = new();
        [Description("Status da Venda")]
        public StatusVenda? StatusVenda { get; set; }
        [Description("Origem")]
        public IdentificacaoIntegracao? Origem { get; set; }
    }
}
