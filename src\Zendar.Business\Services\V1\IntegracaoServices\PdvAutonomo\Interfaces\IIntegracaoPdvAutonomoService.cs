﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.Business.Services.V1.IntegracaoServices.IntegracaoPausarService;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Fiscal;
using Zendar.Business.ViewModels.Integracao;
using Zendar.Business.ViewModels.Integracao.PdvAutonomo;
using Zendar.Business.ViewModels.V1.Fiscal;
using Zendar.Business.ViewModels.V1.Integracao.PdvAutonomo;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;
using Zendar.Data.Helpers;
using Zendar.Data.ViewModels.Dispositivo;
using Zendar.Data.ViewModels.Integracao.FrenteCaixa;
using Zendar.Integracao.ViewModel;

namespace Zendar.Business.Services.V1.IntegracaoServices.PdvAutonomo.Interfaces
{
    public interface IIntegracaoPdvAutonomoService : IDisposable, IAtivarInativarIntegracaoService
    {
        #region Integracao

        Task<IntegracaoObterViewModel> ObterIntegracao(
            Guid? lojaId = null);

        Task<Guid?> CadastrarIntegracao(
            IntegracaoViewModel integracaoViewModel);

        Task<PdvAutonomoConfiguracaoAlterarViewModel> ObterConfiguracaoParaAlterar();

        Task<Guid?> AlterarConfiguracao(
            PdvAutonomoConfiguracaoAlterarViewModel configuracaoAlterarViewModel);

        Task<PdvAutonomoConfiguracaoTefAlterarViewModel> ObterConfiguracaoTefParaAlterar();

        Task<Guid?> AlterarConfiguracaoTef(
            PdvAutonomoConfiguracaoTefAlterarViewModel configuracaoAlterarTefViewModel);

        Task<PdvAutonomoConfiguracaoTabelaPrecoAlterarViewModel> ObterConfiguracaoTabelaPrecoParaAlterar();

        Task<Guid?> AlterarConfiguracaoTabelaPreco(
            PdvAutonomoConfiguracaoTabelaPrecoAlterarViewModel configuracaoTabelaPrecoAlterarViewModel);

        Task Desistir();

        Task<PdvAutonomoClienteFornecedorRegraViewModel> ObterClienteFornecedorRegra(
            Guid clienteFornecedorId);

        Task<List<PdvAutonomoTefViewModel>> ObterListaOperacaoTef();

        Task<List<PdvAutonomoTefViewModel>> ObterListaParcelamentoTef();

        Task<List<PdvAutonomoTefViewModel>> ObterListaTipoCartaoTef();

        Task<string> GerarPinDispositivo();

        Task<PdvAutonomoQuantidadeAtivaDispositivoViewModel> ObterQuantidadeAtivaDispositivo();

        GridPaginadaRetorno<DispositivoPaginadoViewModel> ObterListaDispositivo(
            GridPaginadaConsulta gridPaginada,
            string nome,
            bool? ativo = null);

        Task InativarDispositivo(
            params Guid[] listaDispositivoId);

        Task AtivarDispositivo(
            params Guid[] listaDispositivoId);

        #endregion

        #region Sincronizacao

        Task<PdvAutonomoDispositivoViewModel> ObterDispositivo(
            string pin,
            string identificador,
            string apelido);

        Task<PdvAutonomoDispositivoViewModel> AtivarLicenca(
            PdvAutonomoAtivarLicencaViewModel ativarLicencaViewModel);

        Task MarcarDispositivoComDataHoraUltimaSincronizacao(
            Guid dispositivoId);

        Task<PdvAutonomoDispositivoViewModel> ObterDadosPin(
            string pin);

        Task<Guid?> EnviarVenda(
            OperacaoIntegracaoViewModel operacaoIntegracaoViewModel);

        Task<Guid?> EnviarNotaFiscal(
            NotaFiscalViewModel notaFiscalViewModel);

        Task<Guid?> EnviarCupomSat(
            CupomSatViewModel cupomSatViewModel);

        Task<bool> EnviarInutilizacao(
            EnviarInutilizacaoViewModel enviarInutilizacaoViewModel);

        Task<PdvAutonomoConfiguracaoViewModel> ObterConfiguracao();

        Task<List<LojaViewModel>> ObterListaLoja(
            DateTime? dataAtualizacao);

        Task<List<MarcaViewModel>> ObterListaMarca(
            DateTime? dataAtualizacao);

        Task<List<CorViewModel>> ObterListaCor(
            DateTime? dataAtualizacao);

        Task<List<TamanhoViewModel>> ObterListaTamanho(
            DateTime? dataAtualizacao);

        Task<List<CategoriaProdutoViewModel>> ObterListaCategoria(
            DateTime? dataAtualizacao);

        Task<List<UnidadeMedidaViewModel>> ObterListaUnidade(
            DateTime? dataAtualizacao);

        Task<List<PdvAutonomoFatorConversaoViewModel>> ObterListaFatorConversao(
            DateTime? dataAtualizacao);

        Task<List<PdvAutonomoAliquotaIcmsViewModel>> ObterListaAliquotaIcms(
            DateTime? dataAtualizacao);

        Task<List<VendedorViewModel>> ObterListaVendedor(
            DateTime? dataAtualizacao);

        Task<List<PdvAutonomoUsuarioViewModel>> ObterListaUsuario(
            DateTime? dataAtualizacao);

        Task<List<PdvAutonomoUsuarioPermissaoViewModel>> ObterListaUsuarioPermissao(
            DateTime? dataAtualizacao);

        Task<List<FormaPagamentoRecebimentoViewModel>> ObterListaFormaPagamentoRecebimento(
            DateTime? dataAtualizacao);

        Task<List<RegraFiscalViewModel>> ObterListaRegraFiscal(
            DateTime? dataAtualizacao);

        Task<List<ClienteFornecedorViewModel>> ObterListaCliente(
            GridPaginadaConsulta gridPaginada,
            DateTime? dataAtualizacao);

        Task<List<ProdutoV2ViewModel>> ObterListaProduto(
            GridPaginadaConsulta gridPaginada,
            DateTime? dataAtualizacao);

        Task<List<CredenciadoraCartaoViewModel>> ObterListaCredenciadoraCartao(
            DateTime? dataAtualizacao);

        Task<List<SincronizarNotificacaoViewModel>> ObterListaNotificacaoPorDispositivo(
            Guid dispositivoId);

        Task MarcarNotificacaoComoSincronizada(
            Guid notificacaoId);

        Task<List<SincronizarNotificacaoExclusaoViewModel>> ObterListaNotificacaoExclusaoPorDispositivo(
            Guid dispositivoId);

        Task MarcarNotificacaoExclusaoComoSincronizada(
            Guid notificacaoId);

        Task<List<PeriodoCaixaViewModel>> ObterListaPeriodoCaixa(
            DateTime? dataAtualizacao);

        #endregion

        #region Remover

        Task NotificarExportacao(
           NotificacaoViewModel notificacaoViewModel);

        Task NotificarScript(
           NotificacaoViewModel notificacaoViewModel);

        Task<string> ObterUrlScript();

        #endregion
    }
}