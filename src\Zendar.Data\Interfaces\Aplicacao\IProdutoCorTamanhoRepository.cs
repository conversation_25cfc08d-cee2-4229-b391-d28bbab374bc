﻿using Multiempresa.Shared.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Zendar.Data.Enums;
using Zendar.Data.Helpers;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Models.PesquisaDeProduto;
using Zendar.Data.ViewModels;
using ZendarPackage.NotaFiscal.Classes.Autorizar;

namespace Zendar.Data.Interfaces.Aplicacao
{
    public interface IProdutoCorTamanhoRepository : IRepository<ProdutoCorTamanho>
    {
        Task<List<ProdutoCorTamanho>> ObterProdutosCorTamanhoAlteracaoEmMassa(bool selecionados, Guid[] produtosIds, Guid[] marcasIds, Guid[] categoriasIds, StatusConsulta status);
        Task<List<ProdutoCorTamanho>> ObterProdutosCorTamanhoAlteracaoEmMassa(bool selecionados, Guid[] produtosIds, decimal estoqueMinimoAtual);
        IQueryable<ProdutoCorTamanho> Where(Expression<Func<ProdutoCorTamanho, bool>> predicate);
        Task<bool> AlterarCodigoGtinEan(Guid produtoCorTamanhoId, string codigo);
        Task<bool> AlterarCodigoExterno(Guid produtoCorTamanhoId, string codigo);

        Task<ProdutoCorTamanho> ConsultarDescricaoProduto(Guid produtoCorTamanhoId);
        public Task<List<ProdutoCorTamanhoSelectKitsViewModel>> ListarSelectTamanhoKits(string nomeSkuCodigoExternoBarrasGtinEan, Guid produtoCorId, Guid lojaId);
        Task<decimal> ObterPrecoVendaProduto(Guid produtoCorTamanhoId, Guid lojaId);
        Task<decimal> ObterPrecoCustoProduto(Guid produtoCorTamanhoId, Guid lojaId);
        List<IGrouping<Guid, ProdutoCorTamanho>> ListaSelectConferenciaEstoque(ProdutoCorTamanhoConferenciaEstoqueListaSelectParameros produtoCorTamanhoConferenciaEstoqueListaSelectParameros);
        List<IGrouping<Guid, ProdutoCorTamanho>> ListarSelectMovimentacaoTransferenciaEstoque(string nomeSkuCodigoExternoBarrasGtinEan);
        Task<List<ProdutoExportarConferenciaViewModel>> ObterProdutosExportarConferencia(Guid localEstoqueId, TipoConferencia tipoConferencia, List<Guid> categorias, List<Guid> marcas);
        Task<bool> VerificarCodExterno(Guid id, string codigoExterno);
        Task<bool> VerificarCodGtinEan(Guid id, string gTIN);
        Task<List<OperacaoItem>> ObterParaRelatorioLucroPorProduto(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        Task<List<OperacaoItem>> ObterParaRelatorioProdutoPorVenda(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        Task<List<OperacaoItem>> ObterParaRelatorioProdutoPorGrupo(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        Task<List<ProdutoCorTamanho>> ObterParaRelatorioProdutoComPreco(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        Task<List<ProdutoCorTamanho>> ObterParaRelatorioPersonalizadoProduto(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        Task<List<ProdutoCorTamanho>> ObterParaImprimirEtiqueta(FiltroGerarEtiquetaViewModel filtroGerarEtiqueta, Guid lojaId);
        Task<List<ProdutoCorTamanho>> ListarSelectIdNome(string nomeSkuCodigoExternoBarrasGtinEan);
        Task<bool> ProdutoFoiVendido(List<Guid> produtoCorTamanhoId);
        Task<ProdutoCorTamanho> ObterParaAtualizarCusto(Guid produtoCorTamanhoId, Guid lojaId);
        Task<ProdutoCorTamanho> ObterInformacoesParaNotaFiscal(Guid id, Guid lojaId);
        Task<List<Guid>> ObterIdItensConferencia(Guid lojaEstoqueId, Guid conferenciaId, ICollection<ConferenciaEstoqueFiltros> filtros = null);
        Task<List<ProdutoCorTamanho>> ObterRelatoriosEstoque(RelatorioProdutoFiltrosViewModel filtrosEstoqueViewModel, Guid lojaId);
        Task<List<ProdutoCorTamanho>> ObterVariacoesParaExportarNaTabelaPreco(Guid lojaId);
        Task<Dictionary<string, Guid>> ObterListaIdPorCodigoBarrasInterno(List<string> codigoBarrasInterno);
        IQueryable<ProdutoCorTamanho> ObterPorData(DateTime dataInicio, DateTime dataFim, TipoData tipoData, Guid lojaId);
        Task<List<PesquisaProduto>> ObterParaPopularTabelaPesquisa(Guid? produtoId = null);
        Task<List<PesquisaProduto>> ObterParaPopularTabelaPesquisa(IEnumerable<Guid> produtosId);
        Task<List<ProdutoCorTamanho>> ObterProdutoCorTamanhoPorProdutoId(Guid produtoId, Guid lojaId);
        GridPaginadaRetorno<ProdutoCorTamanho> ObterItensConferirEstoqueCompleto(GridPaginadaConsulta gridConsulta, Guid lojaEstoqueId, Guid conferenciaId, ICollection<ConferenciaEstoqueFiltros> filtros = null, bool completo = false);
        Task<List<ProdutoCorTamanhoEstoque>> ObterParaRelatorioCatalogo(RelatorioCatalogoProdutosFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        Task<List<ProdutoCorTamanho>> ObterParaRelatorioCatalogoGrade(RelatorioCatalogoProdutosFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        IQueryable<ProdutoCorTamanho> ObterEtiquetaPesquisaAvancada(string nomeSkuCodigoExternoBarrasGtinEan, Guid lojaId);
        Task<List<ItemOrigemCombustivel>> ObterOrigemCombustivel(Guid produtoCorTamanhoId);
        Task<List<OperacaoItem>> ObterRelatorioAgrupadoPorCliente(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        Task<List<OperacaoItem>> ObterRelatorioProdutoPorCompras(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        Task<List<OperacaoItem>> ObterRelatorioProdutoPorComprasDetalhado(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        Task<List<OperacaoItem>> ObterRelatoriosProdutoAgrupadoPorDia(RelatorioProdutoFiltrosViewModel filtrosViewModel, Guid lojaId);
        Task<List<OperacaoItem>> ObterParaRelatorioProdutoPorNumeroConta(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        Task<List<OperacaoItem>> ObterParaRelatorioItensMaisVendidos(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        Task<List<OperacaoItem>> ObterRelatorioCompraPorGrade(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, Guid lojaId);
        Task<List<ProdutoCorTamanho>> ListarProdutosImportadosBalanca(DateTime dataHoraLocal, Guid lojaId);
        Task<IEnumerable<ProdutoComposicaoViewModel>> ListarComposicoesProdutos();

    }
}
