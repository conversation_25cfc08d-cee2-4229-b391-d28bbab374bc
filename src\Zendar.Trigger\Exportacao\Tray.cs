﻿using AutoMapper;
using Multiempresa.Shared.Constants;
using Multiempresa.Shared.Enums;
using Multiempresa.Shared.Helpers;
using Multiempresa.Shared.Helpers.Formatadores;
using Newtonsoft.Json;
using Refit;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Zendar.Business.API.ZendarSync.Clients;
using Zendar.Business.API.ZendarSync.Dtos.Export.Request;
using Zendar.Business.API.ZendarSync.Dtos.Trigger.Request;
using Zendar.Business.API.ZendarSync.Dtos.Update.Request;
using Zendar.Business.API.ZendarSync.Handlers;
using Zendar.Business.API.ZendarSync.Services;
using Zendar.Business.AutoMapper.PromocaoMappers;
using Zendar.Business.Consts;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Interfaces.Services.Fiscal;
using Zendar.Business.Services.IntegracaoServices.IntegracaoPedidoService.Cadastrar;
using Zendar.Business.Services.IntegracaoServices.IntegracaoService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoService;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Integracao;
using Zendar.Business.ViewModels.Integracao.Tray;
using Zendar.Business.ViewModels.Integracao.Trigger;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Models.CampanhaPromocional;
using Zendar.Data.Repository.Aplicacao;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoRepository;
using Zendar.Data.Repository.Aplicacao.PromocaoRepository;

namespace zendar_trigger.Exportacao
{
    public class Tray
    {
        private readonly IDocumentoFiscalRepository _documentoFiscalRepository;
        private readonly IDocumentoFiscalItemRepository _documentoFiscalItemRepository;
        private readonly IDocumentoFiscalXmlRepository _documentoFiscalXmlRepository;
        private readonly IIntegracaoRepository _integracaoRepository;
        private readonly IPromocaoRepository _promocaoRepository;
        private readonly IPromocaoItemRepository _promocaoItemRepository;
        private readonly IMarcaService _marcaService;
        private readonly ICategoriaProdutoService _categoriaProdutoService;
        private readonly ICorService _corService;
        private readonly ITamanhoService _tamanhoService;
        private readonly IProdutoCorTamanhoService _produtoCorTamanhoService;
        private readonly IProdutoCorService _produtoCorService;
        private readonly IProdutoV2Service _produtoV2Service;
        private readonly IDocumentoFiscalXmlService _documentoFiscalXmlService;
        private readonly IIntegracaoPedidoService _integracaoPedidoService;
        private readonly IIntegracaoService _integracaoService;
        private readonly IStorageService _storageService;
        private readonly ILogErroService _logErroService;
        private readonly IMapper _mapper;
        private readonly SyncJwtHandler _syncJwtHandler;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private IZendarSyncEcommerceService<ITrayApi> _zendarSyncTrayApi;

        public Tray(
            IDocumentoFiscalRepository documentoFiscalRepository,
            IDocumentoFiscalItemRepository documentoFiscalItemRepository,
            IDocumentoFiscalXmlRepository documentoFiscalXmlRepository,
            IIntegracaoRepository integracaoRepository,
            IPromocaoRepository promocaoRepository,
            IPromocaoItemRepository promocaoItemRepository,
            IMarcaService marcaService,
            ICategoriaProdutoService categoriaProdutoService,
            ICorService corService,
            ITamanhoService tamanhoService,
            IProdutoCorTamanhoService produtoCorTamanhoService,
            IProdutoCorService produtoCorService,
            IProdutoV2Service produtoV2Service,
            IDocumentoFiscalXmlService documentoFiscalXmlService,
            IIntegracaoPedidoService integracaoPedidoService,
            IIntegracaoService integracaoService,
            IStorageService storageService,
            ILogErroService logErroService,
            IMapper mapper,
            SyncJwtHandler syncJwtHandler,
            IAspNetUserInfo aspNetUserInfo)
        {
            _documentoFiscalRepository = documentoFiscalRepository;
            _documentoFiscalItemRepository = documentoFiscalItemRepository;
            _documentoFiscalXmlRepository = documentoFiscalXmlRepository;
            _integracaoRepository = integracaoRepository;
            _promocaoRepository = promocaoRepository;
            _promocaoItemRepository = promocaoItemRepository;
            _marcaService = marcaService;
            _categoriaProdutoService = categoriaProdutoService;
            _corService = corService;
            _tamanhoService = tamanhoService;
            _produtoCorTamanhoService = produtoCorTamanhoService;
            _produtoCorService = produtoCorService;
            _produtoV2Service = produtoV2Service;
            _documentoFiscalXmlService = documentoFiscalXmlService;
            _integracaoPedidoService = integracaoPedidoService;
            _integracaoService = integracaoService;
            _storageService = storageService;
            _logErroService = logErroService;
            _mapper = mapper;
            _syncJwtHandler = syncJwtHandler;
            _aspNetUserInfo = aspNetUserInfo;

            var trayUrl = Environment.GetEnvironmentVariable(SystemConst.ZENDAR_SYNC_TRAY_URL);

            var httpClient = new HttpClient(_syncJwtHandler) { BaseAddress = new Uri(trayUrl) };

            _zendarSyncTrayApi = RestService.For<IZendarSyncEcommerceService<ITrayApi>>(httpClient);
        }

        public async Task Execute(
            ZendarTriggerViewModel zendarTriggerViewModel)
        {
            try
            {
                var integracao =
                    await _integracaoRepository.FirstOrDefault(c => c.LojaId == Guid.Parse(zendarTriggerViewModel.LojaId) &&
                                                                    c.IdentificacaoIntegracao == IdentificacaoIntegracao.TRAY);

                if (integracao == null)
                    return;

                if (zendarTriggerViewModel.Id == Guid.Empty ||
                    zendarTriggerViewModel.TabelaTrigger == TabelaTrigger.CLIENTE)
                {
                    if (string.IsNullOrEmpty(zendarTriggerViewModel.IdCustom))
                        return;
                }

                if (zendarTriggerViewModel.TabelaTrigger == TabelaTrigger.MARCA)
                {
                    if (zendarTriggerViewModel.OperacaoTrigger == OperacaoTrigger.REMOVER)
                        await _zendarSyncTrayApi.GatilhoRemoverMarca(zendarTriggerViewModel.Id);
                    else
                    {
                        var marca =
                            await _marcaService.ObterComPredicate(c => c.Id == zendarTriggerViewModel.Id);

                        if (marca != null)
                        {
                            GatilhoMarcaRequest gatilhoMarcaRequest = new GatilhoMarcaRequest();
                            gatilhoMarcaRequest.ZendarId = marca.Id;
                            gatilhoMarcaRequest.Nome = marca.Nome;

                            await _zendarSyncTrayApi.GatilhoMarca(gatilhoMarcaRequest);
                        }
                    }
                }
                else if (zendarTriggerViewModel.TabelaTrigger == TabelaTrigger.CATEGORIA)
                {
                    if (zendarTriggerViewModel.OperacaoTrigger == OperacaoTrigger.REMOVER)
                        await _zendarSyncTrayApi.GatilhoRemoverCategoria(zendarTriggerViewModel.Id);
                    else
                    {
                        var listaCategoriaProdutoViewModel =
                            await _categoriaProdutoService.ObterLista(true);

                        if (listaCategoriaProdutoViewModel != null &&
                            listaCategoriaProdutoViewModel.Count > 0)
                        {
                            GatilhoCategoriaCadastroRequest gatilhoCategoriaCadastroRequest = new GatilhoCategoriaCadastroRequest();

                            gatilhoCategoriaCadastroRequest = CategoriaProdutoHierarquia(gatilhoCategoriaCadastroRequest, listaCategoriaProdutoViewModel, null);

                            await _zendarSyncTrayApi.GatilhoCategoria(gatilhoCategoriaCadastroRequest);
                        }
                    }
                }
                else if (zendarTriggerViewModel.TabelaTrigger == TabelaTrigger.COR)
                {
                    //Para cor é necessário enviar a lista completa para tray
                    var listaCor =
                        await _corService.ListarSelect(StatusConsulta.Todos, false);

                    if (listaCor != null &&
                        listaCor.Count > 0)
                    {
                        GatilhoCaracteristicaCadastroRequest gatilhoCaracteristicaCadastroRequest = new GatilhoCaracteristicaCadastroRequest();

                        foreach (var cor in listaCor)
                        {
                            GatilhoCaracteristicaRequest gatilhoCaracteristicaRequest = new GatilhoCaracteristicaRequest();
                            gatilhoCaracteristicaRequest.ZendarId = cor.Id.Value;
                            gatilhoCaracteristicaRequest.Nome = cor.Nome;
                            gatilhoCaracteristicaRequest.NomeEcommerce = cor.NomeEcommerce;
                            gatilhoCaracteristicaRequest.Imagem = cor.Imagem;

                            gatilhoCaracteristicaCadastroRequest.ListaPropriedade.Add(gatilhoCaracteristicaRequest);
                        }

                        await _zendarSyncTrayApi.GatilhoCaracteristica(gatilhoCaracteristicaCadastroRequest);
                    }
                }
                else if (zendarTriggerViewModel.TabelaTrigger == TabelaTrigger.TAMANHO)
                {
                    //Para tamanho é necessário enviar a lista completa para tray
                    var listaTamanho =
                        await _tamanhoService.ListarSelect(StatusConsulta.Todos, false);

                    if (listaTamanho != null &&
                        listaTamanho.Count > 0)
                    {
                        GatilhoCaracteristicaCadastroRequest gatilhoCaracteristicaCadastroRequest = new GatilhoCaracteristicaCadastroRequest();

                        foreach (var tamanho in listaTamanho)
                        {
                            GatilhoCaracteristicaRequest gatilhoCaracteristicaRequest = new GatilhoCaracteristicaRequest();
                            gatilhoCaracteristicaRequest.ZendarId = tamanho.Id.Value;
                            gatilhoCaracteristicaRequest.Nome = tamanho.Nome;
                            gatilhoCaracteristicaRequest.NomeEcommerce = tamanho.NomeEcommerce;

                            gatilhoCaracteristicaCadastroRequest.ListaPropriedade.Add(gatilhoCaracteristicaRequest);
                        }

                        await _zendarSyncTrayApi.GatilhoCaracteristica(gatilhoCaracteristicaCadastroRequest);
                    }
                }
                else if (zendarTriggerViewModel.TabelaTrigger == TabelaTrigger.VARIACAO)
                {
                    if (zendarTriggerViewModel.OperacaoTrigger == OperacaoTrigger.REMOVER)
                        await _zendarSyncTrayApi.GatilhoRemoverVariacao(zendarTriggerViewModel.Id);
                }
                else if (zendarTriggerViewModel.TabelaTrigger == TabelaTrigger.PRODUTO)
                {
                    if (zendarTriggerViewModel.OperacaoTrigger == OperacaoTrigger.REMOVER)
                        await _zendarSyncTrayApi.GatilhoRemoverProduto(zendarTriggerViewModel.Id);
                    else
                    {
                        if (zendarTriggerViewModel.Id != Guid.Empty)
                        {
                            ExportarCadastroProdutoRequest exportarCadastroProdutoRequest = new ExportarCadastroProdutoRequest();
                            exportarCadastroProdutoRequest.HTTP_REFERER_MULTIEMPRESA = _aspNetUserInfo?.HostUrl;
                            exportarCadastroProdutoRequest.LojaId = _aspNetUserInfo?.LojaId?.ToString();
                            exportarCadastroProdutoRequest.IdentificacaoIntegracao = (int)IdentificacaoIntegracao.TRAY;
                            exportarCadastroProdutoRequest.UsuarioId = Guid.Parse(_aspNetUserInfo?.Id?.ToString());
                            exportarCadastroProdutoRequest.Token = _aspNetUserInfo.Token;
                            exportarCadastroProdutoRequest.Notificar = false;

                            exportarCadastroProdutoRequest.ListaProduto.Add(new ExportarProdutoRequest() { ProdutoId = zendarTriggerViewModel.Id });

                            await _zendarSyncTrayApi.ExportarCadastroProduto(exportarCadastroProdutoRequest);
                        }
                    }
                }
                else if (zendarTriggerViewModel.TabelaTrigger == TabelaTrigger.DOCUMENTO_FISCAL)
                {
                    var documentoFiscal = await _documentoFiscalRepository.FirstOrDefaultAsNoTracking(d => d.Id == zendarTriggerViewModel.Id);

                    if (documentoFiscal != null &&
                        documentoFiscal.Status == ZendarPackage.NotaFiscal.Enums.StatusFiscal.AUTORIZADA)
                        await GatilhoNotaFiscal(documentoFiscal);
                }
                else if (zendarTriggerViewModel.TabelaTrigger == TabelaTrigger.PROMOCAO)
                {
                    if (integracao.PromocaoId != zendarTriggerViewModel.Id)
                        return;

                    var promocao =
                        await _promocaoRepository.ObterCompletoPorId(zendarTriggerViewModel.Id);

                    var listaTelaUsoPromocao = promocao.ObterTelasUsoSistema();

                    var telaUsoPromocaoTray =
                        listaTelaUsoPromocao?.Any(c => c == TelaUsoPromocao.TRAY);

                    if (!telaUsoPromocaoTray.Value)
                        return;

                    if (zendarTriggerViewModel.OperacaoTrigger == OperacaoTrigger.CADASTRAR ||
                        zendarTriggerViewModel.OperacaoTrigger == OperacaoTrigger.ALTERAR)
                    {
                        var atualizacaoPromocaoRequest = new AtualizacaoPromocaoRequest();
                        atualizacaoPromocaoRequest.PromocaoId = zendarTriggerViewModel.Id;
                        atualizacaoPromocaoRequest.HTTP_REFERER_MULTIEMPRESA = _aspNetUserInfo?.HostUrl;
                        atualizacaoPromocaoRequest.LojaId = _aspNetUserInfo?.LojaId?.ToString();
                        atualizacaoPromocaoRequest.UsuarioId = Guid.Parse(_aspNetUserInfo?.Id?.ToString());
                        atualizacaoPromocaoRequest.Token = _aspNetUserInfo?.Token;
                        atualizacaoPromocaoRequest.Notificar = true;

                        await _integracaoService.AlterarPromocao(integracao.Id, atualizacaoPromocaoRequest.PromocaoId);

                        await _zendarSyncTrayApi.AtualizacaoPromocao(atualizacaoPromocaoRequest);
                    }
                }
                else if (zendarTriggerViewModel.TabelaTrigger == TabelaTrigger.PROMOCAO_ITEM)
                {
                    var key = zendarTriggerViewModel.IdCustom.Split(';');
                    var promocaoId = new Guid(key[0]);
                    var produtoId = new Guid(key[1]);
                    var produtoCorTamanhoId = new Guid(key[2]);

                    if (integracao.PromocaoId != promocaoId)
                        return;

                    var promocao =
                        await _promocaoRepository.ObterCompletoPorId(promocaoId);

                    var listaTelaUsoPromocao = promocao.ObterTelasUsoSistema();

                    var telaUsoPromocaoTray =
                        listaTelaUsoPromocao?.Any(c => c == TelaUsoPromocao.TRAY);

                    if (!telaUsoPromocaoTray.Value)
                        return;

                    var produto = await _produtoV2Service.Obter(produtoId);

                    if (zendarTriggerViewModel.OperacaoTrigger == OperacaoTrigger.REMOVER)
                    {
                        if (produto.TipoProduto == TipoProduto.PRODUTO_SIMPLES)
                        {
                            var gatilhoProdutoPromocaoRequest = new GatilhoProdutoPromocaoRequest();
                            gatilhoProdutoPromocaoRequest.ZendarId = produtoId;
                            gatilhoProdutoPromocaoRequest.PrecoPromocao = 0;
                            gatilhoProdutoPromocaoRequest.InicioPromocao = string.Empty;
                            gatilhoProdutoPromocaoRequest.FimPromocao = string.Empty;

                            await _zendarSyncTrayApi.GatilhoProdutoPromocao(gatilhoProdutoPromocaoRequest);
                        }
                        else
                        {
                            var gatilhoVariacaoPromocaoRequest = new GatilhoVariacaoPromocaoRequest();
                            gatilhoVariacaoPromocaoRequest.ZendarId = produtoCorTamanhoId;
                            gatilhoVariacaoPromocaoRequest.ProdutoId = produtoId;
                            gatilhoVariacaoPromocaoRequest.PrecoPromocao = 0;
                            gatilhoVariacaoPromocaoRequest.InicioPromocao = string.Empty;
                            gatilhoVariacaoPromocaoRequest.FimPromocao = string.Empty;

                            await _zendarSyncTrayApi.GatilhoVariacaoPromocao(gatilhoVariacaoPromocaoRequest);
                        }
                    }
                    else
                    {
                        var promocaoItem =
                            await _promocaoItemRepository.ObterCompletoPorPromocaoIdEProdutoCorTamanhoId(promocaoId, produtoCorTamanhoId);

                        if (produto.TipoProduto == TipoProduto.PRODUTO_SIMPLES)
                        {
                            var gatilhoProdutoPromocaoRequest = new GatilhoProdutoPromocaoRequest();
                            gatilhoProdutoPromocaoRequest.ZendarId = produtoId;
                            gatilhoProdutoPromocaoRequest.PrecoPromocao = promocaoItem.PrecoVenda;
                            gatilhoProdutoPromocaoRequest.InicioPromocao = promocao.VigenciaInicio.ToString("yyyy-MM-dd");
                            gatilhoProdutoPromocaoRequest.FimPromocao = promocao.VigenciaFim.ToString("yyyy-MM-dd");

                            await _zendarSyncTrayApi.GatilhoProdutoPromocao(gatilhoProdutoPromocaoRequest);
                        }
                        else
                        {
                            var gatilhoVariacaoPromocaoRequest = new GatilhoVariacaoPromocaoRequest();
                            gatilhoVariacaoPromocaoRequest.ZendarId = produtoCorTamanhoId;
                            gatilhoVariacaoPromocaoRequest.ProdutoId = produtoId;
                            gatilhoVariacaoPromocaoRequest.PrecoPromocao = promocaoItem.PrecoVenda;
                            gatilhoVariacaoPromocaoRequest.InicioPromocao = promocao.VigenciaInicio.ToString("yyyy-MM-dd");
                            gatilhoVariacaoPromocaoRequest.FimPromocao = promocao.VigenciaFim.ToString("yyyy-MM-dd");

                            await _zendarSyncTrayApi.GatilhoVariacaoPromocao(gatilhoVariacaoPromocaoRequest);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = $"Erro no disparo trigger: Id:{zendarTriggerViewModel.Id} - TabelaTrigger: {zendarTriggerViewModel.TabelaTrigger}" });
            }
        }

        private async Task GatilhoNotaFiscal(
            DocumentoFiscal documentoFiscal)
        {
            var integracaoPedido =
                await _integracaoPedidoService.ObterPorOperacaoId(documentoFiscal.OperacaoId.Value);

            if (integracaoPedido == null)
                return;

            #region Sincronizar Danfe e Xml
            var documentoFiscalXml =
                _documentoFiscalXmlRepository.ObterParaSincronizarArquivoDanfePorDocumentoFiscalId(documentoFiscal.Id);

            if (documentoFiscalXml == null)
                return;

            #region Danfe
            using var danfe = GerarDanfe(documentoFiscalXml);

            var caminhoDanfe = string.Format(CaminhoArquivosStorage.CaminhoArquivosFiscaisDanfe,
                                               FormatarTexto.ManterSomenteNumeros(documentoFiscalXml.DocumentoFiscal.Loja.CpfCnpj),
                                               documentoFiscalXml.DocumentoFiscal.DataEmissao.ToString("yyyy-MM"),
                                               RandomHash.Generate(32));

            var danfeBase64 = Convert.ToBase64String(danfe.ToArray());

            await _storageService.Upload(
                          StorageContaArmazenamento.Danfes,
                          TipoArquivo.OUTROS,
                          caminhoDanfe,
                          danfeBase64);

            documentoFiscalXml.DocumentoFiscal.DanfeCaminhoArquivo = caminhoDanfe;
            #endregion

            #region Xml
            var caminhoXml = string.Format(CaminhoArquivosStorage.CaminhoArquivosFiscaisXml,
                                            FormatarTexto.ManterSomenteNumeros(documentoFiscalXml.DocumentoFiscal.Loja.CpfCnpj),
                                            documentoFiscalXml.DocumentoFiscal.DataEmissao.ToString("yyyy-MM"),
                                            documentoFiscalXml.ModeloFiscal,
                                            documentoFiscalXml.NomeArquivo);

            await _storageService.Upload(
                          StorageContaArmazenamento.ArquivosFiscais,
                          TipoArquivo.XML,
                          caminhoXml,
                          documentoFiscalXml.Arquivo);

            documentoFiscalXml.ArquivoSincronizado = true;
            documentoFiscalXml.Arquivo = caminhoXml;
            #endregion

            await _documentoFiscalXmlRepository.SaveChanges();
            #endregion

            var gatilhoFaturaRequest = new GatilhoFaturaRequest();
            gatilhoFaturaRequest.ZendarId = integracaoPedido.Id;
            gatilhoFaturaRequest.DataEmissao = documentoFiscal.DataEmissao.ToString("yyyy-MM-dd");
            gatilhoFaturaRequest.Numero = documentoFiscal.Numero;
            gatilhoFaturaRequest.Serie = documentoFiscal.Serie.ToString();
            gatilhoFaturaRequest.Valor = documentoFiscal.ValorTotal.ToString("0.##", CultureInfo.InvariantCulture);
            gatilhoFaturaRequest.Chave = documentoFiscal.ChaveAcesso;
            gatilhoFaturaRequest.UrlDanfe = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Danfes, caminhoDanfe, 43830);
            gatilhoFaturaRequest.Xml = await _documentoFiscalXmlService.ObterXml(documentoFiscal.Id, TipoXml.AUTORIZAR);
            gatilhoFaturaRequest.ListaProdutoCfop = new List<GatilhoFaturaProdutoCfopRequest>();

            var listaDocumentoFiscalItem =
                await _documentoFiscalItemRepository.ObterListaDocumentoFiscalItemPorDocumentoFiscalId(documentoFiscal.Id);

            if (listaDocumentoFiscalItem != null &&
                listaDocumentoFiscalItem.Count > 0)
            {
                foreach (var documentoFiscalItem in listaDocumentoFiscalItem)
                {
                    var gatilhoFaturaProdutoCfopRequest = new GatilhoFaturaProdutoCfopRequest();

                    var produtoCorTamanhoTrigger =
                        await _produtoCorTamanhoService.ObterComPredicate(c => c.Id == documentoFiscalItem.ProdutoCorTamanhoId.Value);

                    if (produtoCorTamanhoTrigger != null)
                    {
                        var produtoCorTrigger =
                            await _produtoCorService.ObterComPredicate(c => c.Id == produtoCorTamanhoTrigger.ProdutoCorId);

                        if (produtoCorTrigger != null)
                            gatilhoFaturaProdutoCfopRequest.ZendarProductId = produtoCorTrigger.ProdutoId;
                    }

                    gatilhoFaturaProdutoCfopRequest.ZendarVariantId = documentoFiscalItem.ProdutoCorTamanhoId.Value;
                    gatilhoFaturaProdutoCfopRequest.Cfop = documentoFiscalItem.Cfop;

                    gatilhoFaturaRequest.ListaProdutoCfop.Add(gatilhoFaturaProdutoCfopRequest);
                }
            }

            await _zendarSyncTrayApi.GatilhoAnexarFatura(gatilhoFaturaRequest);
        }

        private GatilhoCategoriaCadastroRequest CategoriaProdutoHierarquia(
            GatilhoCategoriaCadastroRequest gatilhoCategoriaCadastroRequest,
            List<CategoriaProdutoViewModel> listaCategoriaProdutoViewModel,
            Guid? categoriaProdutoPaiId)
        {
            foreach (var categoriaProdutoViewModel in listaCategoriaProdutoViewModel.Where(c => c.CategoriaProdutoPaiId == categoriaProdutoPaiId))
            {
                GatilhoCategoriaRequest gatilhoCategoriaRequest = new GatilhoCategoriaRequest();
                gatilhoCategoriaRequest.ZendarId = categoriaProdutoViewModel.Id;
                gatilhoCategoriaRequest.ZendarCategoriaPaiId = categoriaProdutoViewModel.CategoriaProdutoPaiId;
                gatilhoCategoriaRequest.Nome = categoriaProdutoViewModel.Nome;
                gatilhoCategoriaRequest.Ativo = categoriaProdutoViewModel.Ativo;

                gatilhoCategoriaCadastroRequest.ListaCategoria.Add(gatilhoCategoriaRequest);

                gatilhoCategoriaCadastroRequest = CategoriaProdutoHierarquia(gatilhoCategoriaCadastroRequest, listaCategoriaProdutoViewModel, categoriaProdutoViewModel.Id);
            }

            return gatilhoCategoriaCadastroRequest;
        }

        private MemoryStream GerarDanfe(
            DocumentoFiscalXml documentoFiscalXml)
        {
            var arquivo = documentoFiscalXml.ArquivoSincronizado
                             ? _storageService.Download(StorageContaArmazenamento.ArquivosFiscais, documentoFiscalXml.Arquivo)
                             : new MemoryStream(Encoding.UTF8.GetBytes(documentoFiscalXml.Arquivo));

            var xml = Encoding.UTF8.GetString(arquivo.ToArray());

            var modelo = ZendarPackage.NotaFiscal.Danfe.Modelo.DanfeViewModelCreator.CriarDeStringXml(xml);

            var danfe = new ZendarPackage.NotaFiscal.Danfe.Danfe(modelo);

            if (!string.IsNullOrEmpty(documentoFiscalXml.DocumentoFiscal.Loja.LojaImpressaoRelatorio.LogoQuadrado))
                danfe.AdicionarLogoImagem(_storageService.Download(StorageContaArmazenamento.Imagens, documentoFiscalXml.DocumentoFiscal.Loja.LojaImpressaoRelatorio.LogoQuadrado));

            danfe.Gerar();

            var arquivoMemoryStream = new MemoryStream();
            danfe.Salvar(arquivoMemoryStream);

            return arquivoMemoryStream;
        }
    }
}