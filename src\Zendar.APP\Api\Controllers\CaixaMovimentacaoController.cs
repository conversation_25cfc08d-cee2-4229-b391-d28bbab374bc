﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.APP.Constants;
using Zendar.APP.Extensions;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.Financeiro.AbrirCaixaServices;
using Zendar.Business.Services.Financeiro.CaixaMovimentacaoServices;
using Zendar.Business.ViewModels;
using Zendar.Data.Interfaces.Aplicacao;

namespace Zendar.APP.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CaixaMovimentacaoController : MainController
    {
        private readonly ICaixaMovimentacaoService _caixaMovimentacaoService;
        private readonly IAbrirCaixaService _abrirCaixaService;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly ICaixaMovimentacaoRepository _caixaMovimentacaoRepository;

		public CaixaMovimentacaoController(
			INotificador notificador,
			ICaixaMovimentacaoService caixaMovimentacaoService,
			IAbrirCaixaService abrirCaixaService,
			IAspNetUserInfo aspNetUserInfo,
			ICaixaMovimentacaoRepository caixaMovimentacaoRepository) : base(notificador)
		{
			_caixaMovimentacaoService = caixaMovimentacaoService;
			_abrirCaixaService = abrirCaixaService;
			_aspNetUserInfo = aspNetUserInfo;
			_caixaMovimentacaoRepository = caixaMovimentacaoRepository;
		}

		[HttpPost(Endpoints.AbrirCaixa)]
        [ClaimsAuthorize(Permissoes.ControleCaixaAbrirFechar)]
        public async Task<ActionResult> AbrirCaixa([FromBody] AbrirCaixaViewModel viewModel)
        {
            try
            {
                return CustomResponse(await _abrirCaixaService.AbrirCaixa(viewModel));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, viewModel);
                return CustomResponse();
            }
        }

        #region [Caixa Movel]
        [HttpPost(Endpoints.AbrirCaixaMovel)]
        //[ClaimsAuthorize()]
        public async Task<ActionResult<Guid?>> AbrirCaixaMovel([FromBody] AbrirFecharCaixaDispositivoViewModel abrirFecharCaixaDispositivoViewModel)
        {
            try
            {
                return CustomResponse(
                    await _abrirCaixaService.AbrirCaixaMovel(
                        abrirFecharCaixaDispositivoViewModel.LojaId,
                        abrirFecharCaixaDispositivoViewModel.Identificador,
                        abrirFecharCaixaDispositivoViewModel.AgrupamentoIntegracaoId.Value,
                        Guid.Parse(abrirFecharCaixaDispositivoViewModel.UsuarioId)));
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ExitePorIdentificadorDispositivo)]
        //[ClaimsAuthorize()]
        public async Task<ActionResult<bool>> ExitePorIdentificadorDispositivo([FromQuery] Guid lojaId, [FromQuery] string identificador)
        {
            try
            {
                return CustomResponse(await _caixaMovimentacaoService.ExisteCaixaAbertoPorIdentificadorDispositivo(lojaId, identificador));
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ObterPorAgrupamento)]
        //[ClaimsAuthorize()]
        public async Task<ActionResult<Guid?>> ObterCaixaPorAgrupamentoIntegracao([FromQuery] Guid lojaId, [FromQuery] Guid agrupamentoIntegracaoId)
        {
            try
            {
                return CustomResponse((await _caixaMovimentacaoService.ObterCaixaMovimentacaoPorAgrupamentoId(lojaId, agrupamentoIntegracaoId))?.Id);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpPatch(Endpoints.VincularAgrupamentoIntegracao)]
        //[ClaimsAuthorize()]
        public async Task<ActionResult> VincularAgrupamentoIntegracaoCaixaMovimentacao([FromBody] VincularAgrupamentoIntegracaoCaixaMovimentacaoViewModel viewModel)
        {
            try
            {
                await _caixaMovimentacaoService.VincularAgrupamentoIntegracaoAoCaixaMovimentacao(viewModel);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }
            return CustomResponse();
        }

        [HttpGet("caixas-abertos")]
        public async Task<ActionResult<List<ListaCaixaAbertoSmartPOSViewModel>>> ObterCaixaAbertoPorDispositivo(
            [FromQuery] Guid lojaId,
            [FromQuery] string identificador)
        {
            try
            {
				var caixaMovimentacoesAbertos =
				    await _caixaMovimentacaoRepository.FindAllSelectAsNoTracking(
					    c => c.ContaFinanceira.Dispositivo.Identificador == identificador &&
						     c.ContaFinanceira.LojaId == lojaId,
					    c => new Data.Models.Aplicacao.CaixaMovimentacao
					    {
						    Id = c.Id,
						    AgrupamentoIntegracaoId = c.AgrupamentoIntegracaoId
					    });

                return CustomResponse(
                    caixaMovimentacoesAbertos
                        .Select(x => new ListaCaixaAbertoSmartPOSViewModel
                        {
						    CaixaMovimentacaoId = x.Id,
						    AgrupamentoIntegracaoId = x.AgrupamentoIntegracaoId.Value
					    })
                        .ToList());
			}
            catch (Exception ex)
            {
                NotificarErro(ex, lojaId, identificador);
            }

            return CustomResponse();
        }
        #endregion

        [HttpGet(Endpoints.ObterInformacoesAberturaCaixa)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<SaldoCaixaAberturaViewModel>> ObterInformacoesAberturaCaixa([FromRoute] Guid contaFinanceiraId)
        {
            try
            {
                return CustomResponse(await _abrirCaixaService.ObterSaldoParaAbertura(contaFinanceiraId));
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpDelete("{id}")]
        [ClaimsAuthorize]
        public async Task<ActionResult> Excluir([FromRoute] Guid id)
        {
            try
            {
                await _caixaMovimentacaoService.Excluir(id, Guid.Parse(_aspNetUserInfo.Id));
            }
            catch (Exception ex)
            {
                NotificarErro(ex, new
                {
                    UsuarioId = _aspNetUserInfo.Id,
                    CaixaMovimentacaoId = id
                });
            }

            return CustomResponse();
        }
    }
}
