import { TipoFiscal } from 'constants/enum/tipoFiscal';

import { FormData } from '../validationForm';

export const adaptarRelatorioProdutosVendidos = (data: FormData) => {
  const listaCamposPersonalizados = data.camposPersonalizados
    .filter((campoItem) => campoItem.valor)
    .map((campoPersonalizado) => ({
      ...campoPersonalizado,
      valor: String(campoPersonalizado.valor),
    }));

  const camposPersonalizados =
    listaCamposPersonalizados.length > 0 ? listaCamposPersonalizados : null;

  const tiposFiscaisSelecionaveis = [
    TipoFiscal.NFCE,
    TipoFiscal.NFE,
    TipoFiscal.SEM_FISCAL,
  ];

  const tiposFiscaisSelecionados =
    (data.tipoFiscal?.length || 0) > 0 ? data.tipoFiscal : [TipoFiscal.TODOS];

  const tipoFiscal = tiposFiscaisSelecionaveis.every((tipoFiscalEnum) =>
    tiposFiscaisSelecionados?.includes(tipoFiscalEnum)
  )
    ? [TipoFiscal.TODOS]
    : tiposFiscaisSelecionados;

  return {
    ...data,
    tipoFiscal,
    camposPersonalizados,
    produtoId: data.produtoId?.value || undefined,
    localEstoqueIds: data.localEstoqueIds || undefined,
    clienteFornecedorId: data.clienteFornecedorId?.value || null,
    tipoAgrupamento: data.tipoAgrupamento || undefined,
  };
};
