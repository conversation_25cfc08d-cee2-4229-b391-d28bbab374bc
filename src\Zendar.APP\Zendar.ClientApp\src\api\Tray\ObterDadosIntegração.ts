import api, { ResponseApi } from 'services/api';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';

export type ObterDadosIntegracaoTrayRetorno = {
  id: string;
  dataAtivacao: Date;
  ativo: boolean;
  sincronizacaoHabilitada: boolean;
  configuracoes: string;
};

export const obterDadosIntegracaoTray = () => {
  return api.get<void, ResponseApi<ObterDadosIntegracaoTrayRetorno>>(
    ConstanteEnderecoWebservice.INTEGRACAO_TRAY_OBTER
  );
};
