import { useState, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import { toast } from 'react-toastify';

import { PagedTableForwardRefData } from 'components/update/Table/PagedTable';

import { TabelaPrecoHook } from '../../../hooks';
import { ResultadoImportacao, ProdutosProps } from '../../../types';

type UseImportacaoCSVProps = {
  tabelaPrecoHook: TabelaPrecoHook;
  tabelaPrecoSimples: boolean;
  pagedTableRef: React.RefObject<PagedTableForwardRefData>;
};

export const useImportacaoCSV = ({
  tabelaPrecoHook,
  tabelaPrecoSimples,
  pagedTableRef,
}: UseImportacaoCSVProps) => {
  const [modalErrosOpen, setModalErrosOpen] = useState(false);
  const [resultadoImportacao, setResultadoImportacao] =
    useState<ResultadoImportacao | null>(null);

  const inputRef = useRef<HTMLInputElement>(null);
  const { handleSubmit } = useFormContext();

  const {
    lerArquivoCSV,
    validarCSV,
    exportarProdutosComErro,
    importarProdutoNaTabelaPreco,
    obterProdutosParaExportar,
    idDoParametroRota,
  } = tabelaPrecoHook;

  const handleUploadFile = handleSubmit(() => {
    if (inputRef.current) {
      inputRef.current.click();
    }
  });

  const handleUploadCSV = async (file: File) => {
    const success = validarCSV(file);
    if (success) {
      const resultado = await lerArquivoCSV(file, tabelaPrecoSimples);
      if (resultado && resultado.produtosComErro.length > 0) {
        setResultadoImportacao(resultado);
        setModalErrosOpen(true);
      } else if (resultado && resultado.produtosComSucesso.length > 0) {
        pagedTableRef.current?.reload();
        toast.success('Produtos importados com sucesso!');
      }
      if (!resultado || resultado?.totalProcessados === 0) {
        toast.warning(
          'Não foi possível importar os produtos. Verifique o arquivo e tente novamente.'
        );
      }
    }
  };

  const handleExportarErros = () => {
    if (resultadoImportacao?.produtosComErro) {
      exportarProdutosComErro(
        resultadoImportacao.produtosComErro,
        tabelaPrecoSimples
      );
    }
  };

  const handleCloseModalErros = () => {
    setModalErrosOpen(false);
    setResultadoImportacao(null);
  };

  const handleImportarProdutosSucesso = async (produtos: ProdutosProps) => {
    await importarProdutoNaTabelaPreco(produtos, tabelaPrecoSimples, false);
    pagedTableRef.current?.reload();
  };

  const exportProducts = (exportAll: boolean) => {
    const tabelaPrecoId = idDoParametroRota || null;
    obterProdutosParaExportar(exportAll, tabelaPrecoId, tabelaPrecoSimples);
  };

  return {
    modalErrosOpen,
    resultadoImportacao,
    inputRef,

    handleUploadFile,
    handleUploadCSV,
    handleExportarErros,
    handleCloseModalErros,
    handleImportarProdutosSucesso,
    exportProducts,
  };
};
