﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;
using Zendar.Business.Helpers;

namespace Zendar.APP.Configurations
{
    public static class RedisConfig
    {
        public static IServiceCollection AddRedisConfig(this IServiceCollection services, IConfiguration configuration,
            IWebHostEnvironment currentEnvironment)
        {
            var appSettingsSection = configuration.GetSection("RedisSettings");
            var appSettings = appSettingsSection.Get<RedisSettings>();

            services.AddStackExchangeRedisCache(options =>
            {
                options.ConfigurationOptions = new ConfigurationOptions
                {
                    EndPoints = { { appSettings.Endpoint, int.Parse(appSettings.Port) } },
                    User = "default",
                    Password = appSettings.Password
                };
            });

            return services;
        }
    }
}
