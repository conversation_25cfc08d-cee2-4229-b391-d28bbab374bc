import api, { ResponseApi } from 'services/api';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import StatusConsultaEnum, {
  StatusConsultaEnum as StatusConsultaEnumType,
} from 'constants/enum/tipoAcaoEstoque';

type CorProps = {
  id: string;
  descricao: string;
  descricaoEcommerce: string | null;
  ativo: boolean;
  dataHoraCadastro: string;
  dataHoraUltimaAlteracao: string;
  padraoSistema: boolean;
  hexadecimal: string | null;
  imagem: string | null;
};

type TamanhoProps = {
  id: string;
  codigoGTINEAN: string | null;
  descricao: string;
  descricaoEcommerce: string | null;
  sequenciaOrdenacao: number | null;
  ativo: boolean;
  dataHoraCadastro: string;
  dataHoraUltimaAlteracao: string;
  padraoSistema: boolean;
};

type ObterProdutoCorTamanhoGtinEanRetorno = {
  cor: CorProps;
  tamanhos?: TamanhoProps[];
};

type ObterProdutoCorTamanhoGtinEanProps = {
  produtoId: string;
  status?: StatusConsultaEnumType;
};

export const obterProdutoCorTamanhoGtinEan = (
  props: ObterProdutoCorTamanhoGtinEanProps
) => {
  const { produtoId, status = StatusConsultaEnum.ATIVOS } = props;

  const query = new URLSearchParams({
    status: status.toString(),
  });

  return api.get<void, ResponseApi<ObterProdutoCorTamanhoGtinEanRetorno[]>>(
    `${ConstanteEnderecoWebservice.OBTER_PRODUTO_COR_TAMANHO_GTIN_EAN.replace(
      '{id}',
      produtoId
    )}?${query.toString()}`
  );
};
