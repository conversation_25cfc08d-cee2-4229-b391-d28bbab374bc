﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.APP.Constants;
using Zendar.APP.Extensions;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.V1.IntegracaoServices.PdvAutonomo.Interfaces;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Fiscal;
using Zendar.Business.ViewModels.Integracao;
using Zendar.Business.ViewModels.Integracao.FrenteCaixa;
using Zendar.Business.ViewModels.Integracao.PdvAutonomo;
using Zendar.Business.ViewModels.V1.Fiscal;
using Zendar.Business.ViewModels.V1.Integracao.PdvAutonomo;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces;
using Zendar.Data.ViewModels.Dispositivo;
using Zendar.Data.ViewModels.Integracao.FrenteCaixa;
using Zendar.Integracao.ViewModel;

namespace Zendar.APP.Api.Controllers.Integracao.PdvAutonomo
{
    [ApiController]
    [Route("api/[controller]")]
    public class PdvAutonomoController : MainController
    {
        private readonly IDatabaseTransaction _databaseTransaction;
        private readonly IIntegracaoPdvAutonomoService _integracaoPdvAutonomoService;
        private readonly IIntegracaoCaixaPdvAutonomoService _integracaoCaixaPdvAutonomoService;

        public PdvAutonomoController(
            INotificador notificador,
            IDatabaseTransaction databaseTransaction,
            IIntegracaoPdvAutonomoService integracaoPdvAutonomoService,
            IIntegracaoCaixaPdvAutonomoService integracaoCaixaPdvAutonomoService) : base(notificador)
        {
            _databaseTransaction = databaseTransaction;
            _integracaoPdvAutonomoService = integracaoPdvAutonomoService;
            _integracaoCaixaPdvAutonomoService = integracaoCaixaPdvAutonomoService;
        }

        #region Integracao

        [HttpGet(Endpoints.PdvAutonomoObterIntegracao)]
        [ClaimsAuthorize]
        public async Task<ActionResult<IntegracaoObterViewModel>> ObterIntegracao()
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterIntegracao();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoCadastrarIntegracao)]
        [ClaimsAuthorize]
        public async Task<ActionResult<Guid?>> CadastrarIntegracao(
            [FromBody] IntegracaoViewModel integracaoViewModel)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.CadastrarIntegracao(integracaoViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterConfiguracaoParaAlterar)]
        [ClaimsAuthorize]
        public async Task<ActionResult<PdvAutonomoConfiguracaoAlterarViewModel>> ObterConfiguracaoParaAlterar()
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterConfiguracaoParaAlterar();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoAlterarConfiguracao)]
        [ClaimsAuthorize]
        public async Task<ActionResult<Guid?>> AlterarConfiguracao(
            [FromBody] PdvAutonomoConfiguracaoAlterarViewModel configuracaoAlterarViewModel)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.AlterarConfiguracao(configuracaoAlterarViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterConfiguracaoTefParaAlterar)]
        [ClaimsAuthorize]
        public async Task<ActionResult<PdvAutonomoConfiguracaoTefAlterarViewModel>> ObterConfiguracaoTefParaAlterar()
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterConfiguracaoTefParaAlterar();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoAlterarConfiguracaoTef)]
        [ClaimsAuthorize]
        public async Task<ActionResult<Guid?>> AlterarConfiguracaoTef(
            [FromBody] PdvAutonomoConfiguracaoTefAlterarViewModel configuracaoTefAlterarViewModel)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.AlterarConfiguracaoTef(configuracaoTefAlterarViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterConfiguracaoTabelaPrecoParaAlterar)]
        [ClaimsAuthorize]
        public async Task<ActionResult<PdvAutonomoConfiguracaoTabelaPrecoAlterarViewModel>> ObterConfiguracaoTabelaPrecoParaAlterar()
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterConfiguracaoTabelaPrecoParaAlterar();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoAlterarConfiguracaoTabelaPreco)]
        [ClaimsAuthorize]
        public async Task<ActionResult<Guid?>> AlterarConfiguracaoTabelaPreco(
            [FromBody] PdvAutonomoConfiguracaoTabelaPrecoAlterarViewModel configuracaoTabelaPrecoAlterarViewModel)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.AlterarConfiguracaoTabelaPreco(configuracaoTabelaPrecoAlterarViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoDesistir)]
        [ClaimsAuthorize]
        public async Task<ActionResult> Desistir()
        {
            try
            {
                await _integracaoPdvAutonomoService.Desistir();
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }

            return CustomResponse();
        }

        [HttpGet(Endpoints.PdvAutonomoClienteFornecedorRegra)]
        public async Task<ActionResult<PdvAutonomoClienteFornecedorRegraViewModel>> ObterClienteFornecedorRegra(
            [FromRoute] Guid clienteFornecedorId)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterClienteFornecedorRegra(clienteFornecedorId);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaOperacaoTef)]
        public async Task<ActionResult<List<PdvAutonomoTefViewModel>>> ObterListaOperacaoTef()
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaOperacaoTef();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaParcelamentoTef)]
        public async Task<ActionResult<List<PdvAutonomoTefViewModel>>> ObterListaParcelamentoTef()
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaParcelamentoTef();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaTipoCartaoTef)]
        public async Task<ActionResult<List<PdvAutonomoTefViewModel>>> ObterListaTipoCartaoTef()
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaTipoCartaoTef();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoGerarPin)]
        [ClaimsAuthorize]
        public async Task<ActionResult<string>> GerarPin()
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.GerarPinDispositivo();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterQuantidadeAtivaDispositivo)]
        [ClaimsAuthorize]
        public async Task<ActionResult<PdvAutonomoQuantidadeAtivaDispositivoViewModel>> ObterQuantidadeAtivaDispositivo()
        {
            try
            {
                return CustomResponse(await _integracaoPdvAutonomoService.ObterQuantidadeAtivaDispositivo());
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaDispositivo)]
        [ClaimsAuthorize]
        public ActionResult<GridPaginadaRetorno<DispositivoPaginadoViewModel>> ObterListaDispositivo(
            [FromQuery] GridPaginadaConsulta gridPaginada,
            string nome,
            bool? ativo = null)
        {
            try
            {
                var result = _integracaoPdvAutonomoService.ObterListaDispositivo(gridPaginada, nome, ativo);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPut(Endpoints.PdvAutonomoInativarDispositivo)]
        [ClaimsAuthorize]
        public async Task<ActionResult> InativarDispositivo(
            [FromQuery] Guid id)
        {
            try
            {
                await _integracaoPdvAutonomoService.InativarDispositivo(id);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, id);
            }

            return CustomResponse();
        }

        [HttpPut(Endpoints.PdvAutonomoAtivarDispositivo)]
        [ClaimsAuthorize]
        public async Task<ActionResult> AtivarDispositivo(
            [FromQuery] Guid id)
        {
            try
            {
                await _integracaoPdvAutonomoService.AtivarDispositivo(id);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, id);
            }

            return CustomResponse();
        }

        #endregion

        #region Sincronizacao

        [HttpGet(Endpoints.PdvAutonomoObterDispositivo)]
        public async Task<ActionResult<PdvAutonomoDispositivoViewModel>> ObterDispositivo(
            [FromQuery] string pin,
            [FromQuery] string identificador,
            [FromQuery] string apelido)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterDispositivo(pin, identificador, apelido);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, pin, identificador, apelido);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoAtivarLicenca)]
        public async Task<ActionResult<PdvAutonomoDispositivoViewModel>> AtivarLicenca(
            [FromBody] PdvAutonomoAtivarLicencaViewModel ativarLicencaViewModel)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.AtivarLicenca(ativarLicencaViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPut(Endpoints.PdvAutonomoMarcarDispositivoComDataHoraUltimaSincronizacao)]
        public async Task<ActionResult<FrenteCaixaDispositivoViewModel>> MarcarDispositivoComDataHoraUltimaSincronizacao(
        [FromQuery] Guid dispositivoId)
        {
            try
            {
                await _integracaoPdvAutonomoService.MarcarDispositivoComDataHoraUltimaSincronizacao(dispositivoId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }

            return CustomResponse();
        }

        [HttpGet(Endpoints.PdvAutonomoObterDadosPin)]
        public async Task<ActionResult<PdvAutonomoDispositivoViewModel>> ObterDadosPin(
            [FromRoute] string pin)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterDadosPin(pin);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoAbrirCaixaDispositivo)]
        public async Task<ActionResult<Guid?>> AbrirPorDispositivo(
            [FromBody] AbrirFecharCaixaDispositivoViewModel abrirFecharCaixaDispositivoViewModel)
        {
            try
            {
                var result =
                    await _integracaoCaixaPdvAutonomoService.AbrirPorDispositivo(abrirFecharCaixaDispositivoViewModel, ReferenciaServicoStargate.DISPOSITIVO_PDV);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoReabrirPorDispositivo)]
        public async Task<ActionResult<Guid?>> ReabrirPorDispositivo(
            [FromBody] AbrirFecharCaixaDispositivoViewModel abrirFecharCaixaDispositivoViewModel)
        {
            try
            {
                var result =
                    await _integracaoCaixaPdvAutonomoService.ReabrirPorDispositivo(abrirFecharCaixaDispositivoViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoFecharCaixaDispositivo)]
        public async Task<ActionResult<Guid?>> FecharPorDispositivo(
            [FromBody] AbrirFecharCaixaDispositivoViewModel abrirFecharCaixaDispositivoViewModel)
        {
            try
            {
                var result =
                    await _integracaoCaixaPdvAutonomoService.FecharPorDispositivo(abrirFecharCaixaDispositivoViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoTransferencia)]
        public async Task<ActionResult<Guid?>> Transferencia(
         [FromBody] TransferenciaViewModel transferenciaViewModel)
        {
            try
            {
                var result =
                    await _integracaoCaixaPdvAutonomoService.Transferencia(transferenciaViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoVincularAgrupamento)]
        public async Task<ActionResult<Guid?>> VincularAgrupamentoIntegracaoCaixaMovimentacao(
            [FromBody] VincularAgrupamentoIntegracaoCaixaMovimentacaoViewModel vincularAgrupamentoIntegracaoCaixaMovimentacaoViewModel)
        {
            try
            {
                var result =
                    await _integracaoCaixaPdvAutonomoService.VincularAgrupamento(vincularAgrupamentoIntegracaoCaixaMovimentacaoViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoEnviarVenda)]
        public async Task<ActionResult<Guid?>> EnviarVenda(
            OperacaoIntegracaoViewModel operacaoIntegracaoViewModel)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.EnviarVenda(operacaoIntegracaoViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, enviarEmail: false, operacaoIntegracaoViewModel);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoEnviarNotaFiscal)]
        public async Task<ActionResult<Guid?>> EnviarNotaFiscal(
            NotaFiscalViewModel notaFiscalViewModel)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.EnviarNotaFiscal(notaFiscalViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, notaFiscalViewModel);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoEnviarInutilizacao)]
        public async Task<ActionResult<bool>> EnviarInutilizacao(
            EnviarInutilizacaoViewModel enviarInutilizacaoViewModel)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.EnviarInutilizacao(enviarInutilizacaoViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, enviarInutilizacaoViewModel);

                return CustomResponse();
            }
        }

        [HttpPost(Endpoints.PdvAutonomoEnviarCupomSat)]
        public async Task<ActionResult<Guid?>> EnviarCupomSat(
            CupomSatViewModel cupomSatViewModel)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.EnviarCupomSat(cupomSatViewModel);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, cupomSatViewModel);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterConfiguracao)]
        public async Task<ActionResult<PdvAutonomoConfiguracaoViewModel>> ObterConfiguracao()
        {
            try
            {
                var result = await _integracaoPdvAutonomoService.ObterConfiguracao();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaLoja)]
        public async Task<ActionResult<List<LojaViewModel>>> ObterListaLoja(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaLoja(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaMarca)]
        public async Task<ActionResult<List<MarcaViewModel>>> ObterListaMarca(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaMarca(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaCor)]
        public async Task<ActionResult<List<CorViewModel>>> ObterListaCor(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaCor(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaTamanho)]
        public async Task<ActionResult<List<TamanhoViewModel>>> ObterListaTamanho(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaTamanho(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaCategoria)]
        public async Task<ActionResult<List<CategoriaProdutoViewModel>>> ObterListaCategoria(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaCategoria(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaUnidade)]
        public async Task<ActionResult<List<UnidadeMedidaViewModel>>> ObterListaUnidade(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaUnidade(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaFatorConversao)]
        public async Task<ActionResult<List<PdvAutonomoFatorConversaoViewModel>>> ObterListaFatorConversao(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaFatorConversao(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaAliquotaIcms)]
        public async Task<ActionResult<List<PdvAutonomoAliquotaIcmsViewModel>>> ObterListaAliquotaIcms(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaAliquotaIcms(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaVendedor)]
        public async Task<ActionResult<List<VendedorViewModel>>> ObterListaVendedor(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaVendedor(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaUsuario)]
        public async Task<ActionResult<List<PdvAutonomoUsuarioViewModel>>> ObterListaUsuario(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaUsuario(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaUsuarioPermissao)]
        public async Task<ActionResult<List<PdvAutonomoUsuarioPermissaoViewModel>>> ObterListaUsuarioPermissao(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaUsuarioPermissao(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaFormaPagamentoRecebimento)]
        public async Task<ActionResult<List<FormaPagamentoRecebimentoViewModel>>> ObterListaFormaPagamentoRecebimento(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaFormaPagamentoRecebimento(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaRegraFiscal)]
        public async Task<ActionResult<List<RegraFiscalViewModel>>> ObterListaRegraFiscal(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaRegraFiscal(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaCliente)]
        public async Task<ActionResult<List<ClienteFornecedorViewModel>>> ObterListaCliente(
            [FromQuery] GridPaginadaConsulta gridPaginada,
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaCliente(gridPaginada, dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaProduto)]
        public async Task<ActionResult<List<ProdutoV2ViewModel>>> ObterListaProduto(
            [FromQuery] GridPaginadaConsulta gridPaginada,
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaProduto(gridPaginada, dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaCredenciadoraCartao)]
        public async Task<ActionResult<List<PdvAutonomoTefViewModel>>> ObterListaCredenciadoraCartao(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaCredenciadoraCartao(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaNotificacaoPorDispositivo)]
        public async Task<ActionResult<List<SincronizarNotificacaoViewModel>>> ObterListaNotificacaoPorDispositivo(
            [FromQuery] Guid dispositivoId)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaNotificacaoPorDispositivo(dispositivoId);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPut(Endpoints.PdvAutonomoMarcarNotificacaoComoSincronizada)]
        public async Task<ActionResult> MarcarNotificacaoComoSincronizada(
            [FromQuery] Guid notificacaoId)
        {
            try
            {
                await _integracaoPdvAutonomoService.MarcarNotificacaoComoSincronizada(notificacaoId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, notificacaoId);
            }

            return CustomResponse();
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaNotificacaoExclusaoPorDispositivo)]
        public async Task<ActionResult<List<SincronizarNotificacaoExclusaoViewModel>>> ObterListaNotificacaoExclusaoPorDispositivo(
            [FromQuery] Guid dispositivoId)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaNotificacaoExclusaoPorDispositivo(dispositivoId);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        [HttpPut(Endpoints.PdvAutonomoMarcarNotificacaoExclusaoComoSincronizada)]
        public async Task<ActionResult> MarcarNotificacaoExclusaoComoSincronizada(
            [FromQuery] Guid notificacaoId)
        {
            try
            {
                await _integracaoPdvAutonomoService.MarcarNotificacaoExclusaoComoSincronizada(notificacaoId);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, notificacaoId);
            }

            return CustomResponse();
        }

        [HttpGet(Endpoints.PdvAutonomoObterListaPeriodoCaixa)]
        public async Task<ActionResult<List<PeriodoCaixaViewModel>>> ObterListaPeriodoCaixa(
            [FromQuery] DateTime? dataAtualizacao)
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterListaPeriodoCaixa(dataAtualizacao);

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        #endregion

        #region Remover

        [HttpPost(Endpoints.PdvAutonomoNotificarExportacao)]
        public async Task<ActionResult> NotificarExportacao(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                await _integracaoPdvAutonomoService.NotificarExportacao(notificacaoViewModel);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, notificacaoViewModel);
            }

            return CustomResponse();
        }

        [HttpPost(Endpoints.PdvAutonomoNotificarScript)]
        public async Task<ActionResult> NotificarScript(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                await _integracaoPdvAutonomoService.NotificarScript(notificacaoViewModel);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, notificacaoViewModel);
            }

            return CustomResponse();
        }

        [HttpGet(Endpoints.PdvAutonomoObterUrlScript)]
        [ClaimsAuthorize]
        public async Task<ActionResult<string>> ObterUrlScript()
        {
            try
            {
                var result =
                    await _integracaoPdvAutonomoService.ObterUrlScript();

                return CustomResponse(result);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);

                return CustomResponse();
            }
        }

        #endregion
    }
}