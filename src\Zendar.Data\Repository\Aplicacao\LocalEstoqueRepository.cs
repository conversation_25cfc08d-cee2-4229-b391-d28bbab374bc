﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Zendar.Data.Contexts;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Enums;
using Zendar.Data.ViewModels;

namespace Zendar.Data.Repository.Aplicacao
{
    public class LocalEstoqueRepository : RepositoryAplicacao<LocalEstoque>, ILocalEstoqueRepository
    {
        public LocalEstoqueRepository(AplicacaoContexto context) : base(context)
        {
        }

        public GridPaginadaRetorno<LocalEstoquePaginadaViewModel> ListarPaginado(GridPaginadaConsulta gridPaginada, string nome, StatusConsulta statusConsulta, Guid? lojaId)
        {
            var gridPaginadaRetorno = new GridPaginadaRetorno<LocalEstoquePaginadaViewModel>();

            var query = DbSet.Where(x => (string.IsNullOrEmpty(nome) ||
                                           EF.Functions.Collate(x.Nome, Collates.RemoverCaracteresEspeciaisAcentos).Contains(nome)) &&
                                         (lojaId.HasValue && x.LojaId.Equals(lojaId)) &&
                                         (statusConsulta == StatusConsulta.Todos || x.Ativo.Equals(StatusConsulta.Ativos.Equals(statusConsulta))))
                             .OrderBy($"{gridPaginada.CampoOrdenacao} {gridPaginada.DirecaoOrdenacao}")
                             .Select(x => new LocalEstoquePaginadaViewModel
                             {
                                 Id = x.Id,
                                 Nome = x.Nome,
                                 Ativo = x.Ativo,
                                 PadraoSistema = x.PadraoSistema
                             });

            gridPaginadaRetorno.CarregarPaginacao(query, gridPaginada);

            return gridPaginadaRetorno;
        }

        public async Task<List<IdNomeViewModel>> ListarSelect(StatusConsulta statusConsulta, Guid lojaId)
        {
            var query = DbSet
              .AsNoTracking()
              .Where(x => (statusConsulta == StatusConsulta.Todos || x.Ativo.Equals(StatusConsulta.Ativos.Equals(statusConsulta))) && x.LojaId.Equals(lojaId));

            return await query
                .Select(x => new IdNomeViewModel
                {
                    Id = x.Id,
                    Nome = x.Nome
                })
                .ToListAsync();
        }

        public async Task<Guid> ObterLocalEstoqueIdPadrao(Guid lojaId)
        {
            return await DbSet
                 .Where(x => x.LojaId == lojaId && x.PadraoSistema)
                 .Select(x => x.Id)
                 .FirstOrDefaultAsync();
        }

        public async Task<LocalEstoque> ObterComFantasia(Guid Id)
        {
            return await DbSet.Where(x => x.Id.Equals(Id)).Select(x => new LocalEstoque { Id = x.Id, Nome = x.Nome, LojaId = x.LojaId, Loja = new Loja { Fantasia = x.Loja.Fantasia } }).FirstOrDefaultAsync();
        }

        public async Task<string> ObterNome(Guid id)
        {
            return await DbSet.Where(l => l.Id.Equals(id)).Select(l => l.Nome).FirstOrDefaultAsync();
        }
        public async Task<List<string>> ObterNomes(IEnumerable<Guid> ids)
        {
            return await DbSet
                .Where(l => ids.Contains(l.Id))
                .Select(l => l.Nome)
                .ToListAsync();
        }

        public async Task<LocalEstoque> ObterComProdutos(Guid id)
        {
            return await DbSet.Where(l => l.Id.Equals(id)).Include(x => x.ProdutoCorTamanhoEstoques).FirstOrDefaultAsync();

        }
    }
}
