﻿using Multiempresa.Shared.Enums;
using Multiempresa.Shared.Helpers.Convertores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.Financeiro.ContaFinanceiraServices.ContaFinanceiraSaldoServices;
using Zendar.Business.Services.OperacaoServices.OperacaoTransferenciasDinheiroServices;
using Zendar.Business.ViewModels;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Interfaces;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services.Financeiro.AbrirCaixaServices
{
    public class AbrirCaixaService : BaseService, IAbrirCaixaService
    {
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IDatabaseTransaction _databaseTransaction;

        private readonly IFormaPagamentoService _formaPagamentoService;
        private readonly IOperacaoTransferenciasDinheiroService _operacaoTransferenciasDinheiroService;
        private readonly IContaFinanceiraSaldoService _contaFinanceiraSaldoService;

        private readonly ICaixaMovimentacaoRepository _caixaMovimentacaoRepository;
        private readonly IContaFinanceiraRepository _contaFinanceiraRepository;
        private readonly ICaixaConferenciaRepository _caixaConferenciaRepository;
        private readonly IDispositivoRepository _dispositivoRepository;

        private readonly ILogAuditoriaService _logAuditoriaService;

        public AbrirCaixaService(INotificador notificador,
                                 IAspNetUserInfo aspNetUserInfo,
                                 IDatabaseTransaction databaseTransaction,
                                 IFormaPagamentoService formaPagamentoService,
                                 IOperacaoTransferenciasDinheiroService operacaoTransferenciasDinheiroService,
                                 ICaixaMovimentacaoRepository caixaMovimentacaoRepository,
                                 IContaFinanceiraRepository contaFinanceiraRepository,
                                 IContaFinanceiraSaldoService contaFinanceiraSaldoService,
                                 ICaixaConferenciaRepository caixaConferenciaRepository,
                                 ILogAuditoriaService logAuditoriaService,
                                 IDispositivoRepository dispositivoRepository) : base(notificador)
        {
            _aspNetUserInfo = aspNetUserInfo;
            _databaseTransaction = databaseTransaction;
            _operacaoTransferenciasDinheiroService = operacaoTransferenciasDinheiroService;
            _caixaMovimentacaoRepository = caixaMovimentacaoRepository;
            _contaFinanceiraRepository = contaFinanceiraRepository;
            _formaPagamentoService = formaPagamentoService;
            _contaFinanceiraSaldoService = contaFinanceiraSaldoService;
            _contaFinanceiraSaldoService = contaFinanceiraSaldoService;
            _caixaConferenciaRepository = caixaConferenciaRepository;
            _logAuditoriaService = logAuditoriaService;
            _dispositivoRepository = dispositivoRepository;
        }

        public async Task<SaldoCaixaAberturaViewModel> ObterSaldoParaAbertura(Guid contaFinanceiraId)
        {
            // Obter ultimo caixa movimentacao da conta com as conferencias
            var caixaMovimentacao = await _caixaMovimentacaoRepository.ObterUltimoCaixaMovimentacaoAberto(contaFinanceiraId, _aspNetUserInfo.LojaId.Value);

            // Recupera forma de recebimento dinheiro e cheque
            var dinheiro = await _formaPagamentoService.ObterFormaRecebimentoDinheiro();

            // Recupero o saldo atual da conta financeira
            var saldo = await _contaFinanceiraRepository.ObterSaldo(contaFinanceiraId);

            // Inicia o objeto de retorno com dados padrões
            var saldoViewModel = new SaldoCaixaAberturaViewModel(saldo, dinheiro);

            // Se tiver caixa movimentação, utiliza os dados dela
            // Quando uma conta caixa for aberta pela primeira vez, não terá um caixa movimentação
            if (caixaMovimentacao != null)
            {
                ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, caixaMovimentacao);

                saldoViewModel.UsuarioFechamento = caixaMovimentacao.UsuarioFechamento.Nome;
                saldoViewModel.DataFechamento = caixaMovimentacao.DataHoraFechamento.Value;
            }

            // Quando o caixa for fechado sem movimentações, não haverá registros de conferências
            var contaFinanceiraSaldos = await _contaFinanceiraSaldoService.ObterPorContaFinanceira(contaFinanceiraId);

            if (contaFinanceiraSaldos != null && contaFinanceiraSaldos.Any())
            {
                saldoViewModel.FormasRecebimento = contaFinanceiraSaldos
                    .Where(x => x.FormaPagamentoRecebimento.Ativo ||
                                x.Saldo != 0)
                    .Select(x => new SaldoAberturaFormaRecebimentoViewModel
                    {
                        FormaRecebimentoId = x.FormaPagamentoRecebimentoId,
                        FormaRecebimentoNome = x.FormaPagamentoRecebimento.Nome,
                        Saldo = x.Saldo
                    })
                    .ToList();
            }

            // Retorna uma view model com os dados da conferencia da ultima caixaMovimentacao
            return saldoViewModel;
        }

        public async Task<Guid?> AbrirCaixa(AbrirCaixaViewModel abrirCaixaViewModel)
        {
            if (!abrirCaixaViewModel.UsuarioId.HasValue)
                abrirCaixaViewModel.UsuarioId = Guid.Parse(_aspNetUserInfo.Id);

            // Validações para a abertura do caixa
            var caixaMovimentacaoId = await ValidarAberturaDeCaixa(abrirCaixaViewModel);

            if (caixaMovimentacaoId.HasValue && caixaMovimentacaoId.Value != Guid.Empty)
                return caixaMovimentacaoId;

            _databaseTransaction.BeginTransaction();

            /// Se houver alteração no saldo, realizar operação de transferência
            if (abrirCaixaViewModel.AlteracaoSaldo)
                await CriarTransferenciaParaAjustarSaldoCaixa(abrirCaixaViewModel.ContaFinanceiraId, abrirCaixaViewModel.FormasRecebimento);

            //Pego o valor do saldo atual da conta financeira
            var contaFinanceiraSaldo = (await _contaFinanceiraRepository.FirstOrDefaultAsNoTracking(x => x.Id == abrirCaixaViewModel.ContaFinanceiraId, x => new ContaFinanceira { Saldo = x.Saldo }))?.Saldo ?? 0;

            var caixaMovimentacao = new CaixaMovimentacao
            {
                ContaFinanceiraId = abrirCaixaViewModel.ContaFinanceiraId,
                UsuarioAberturaId = abrirCaixaViewModel.UsuarioId.Value,
                DataHoraAbertura = DateTime.UtcNow,
                SaldoAbertura = abrirCaixaViewModel.AlteracaoSaldo ? abrirCaixaViewModel.FormasRecebimento.Sum(x => x.NovoSaldo) : contaFinanceiraSaldo
            };

            await _caixaMovimentacaoRepository.Insert(caixaMovimentacao);

            var nomeContaFinanceira = await _contaFinanceiraRepository.FirstOrDefaultAsNoTracking(x => x.Id == abrirCaixaViewModel.ContaFinanceiraId,
                                                                                                  x => new ContaFinanceira { Nome = x.Nome });

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel
            {
                Tela = LogAuditoriaTela.PDV,
                Operacao = LogAuditoriaOperacao.CADASTRAR,
                UsuarioId = abrirCaixaViewModel.UsuarioId,
                Descricao = $"{nomeContaFinanceira.Nome}: Abrir"
            });

            if (!PossuiAvisos() || !PossuiErros())
                _databaseTransaction.Commit();

            return caixaMovimentacao.Id;
        }

        private async Task<Guid?> ValidarAberturaDeCaixa(AbrirCaixaViewModel abrirCaixaViewModel)
        {
            var caixaMovimentacaoAberto = await _caixaMovimentacaoRepository
                                          .FirstOrDefaultAsNoTracking(x => x.UsuarioAberturaId == abrirCaixaViewModel.UsuarioId.Value
                                          && x.ContaFinanceiraId == abrirCaixaViewModel.ContaFinanceiraId
                                          && x.ContaFinanceira.LojaId.Equals(_aspNetUserInfo.LojaId.Value)
                                          && !x.DataHoraFechamento.HasValue,
                                          x => new CaixaMovimentacao { Id = x.Id });

            if (caixaMovimentacaoAberto != null)
            {
                return caixaMovimentacaoAberto.Id;
            }


            // Validação se já existe um caixa aberto:
            // Para conta caixa, o usuário não pode ter outro caixa aberto de uma outra conta caixa
            // Para caixa movel, o usuário pode ter n caixas abertos com contas diferentes
            var caixaMovimentacaoAbertoDiferenteSelecionado = await _caixaMovimentacaoRepository
                                                                        .FirstOrDefaultAsNoTracking(x => x.UsuarioAberturaId == abrirCaixaViewModel.UsuarioId.Value
                                                                                                       && !x.DataHoraFechamento.HasValue
                                                                                                       && x.ContaFinanceira.LojaId.Equals(_aspNetUserInfo.LojaId.Value)
                                                                                                       && ((abrirCaixaViewModel.TipoCaixa == Multiempresa.Shared.Enums.TipoContaFinanceira.CAIXA
                                                                                                            && x.ContaFinanceira.TipoContaFinanceira == abrirCaixaViewModel.TipoCaixa
                                                                                                            && x.ContaFinanceiraId != abrirCaixaViewModel.ContaFinanceiraId
                                                                                                            )
                                                                                                          ||
                                                                                                           (x.ContaFinanceira.Dispositivo != null
                                                                                                            && x.ContaFinanceira.TipoContaFinanceira == abrirCaixaViewModel.TipoCaixa
                                                                                                            && x.ContaFinanceiraId == abrirCaixaViewModel.ContaFinanceiraId)),
                                                                                                     x => new CaixaMovimentacao
                                                                                                     {
                                                                                                         Id = x.Id,
                                                                                                     });

            if (caixaMovimentacaoAbertoDiferenteSelecionado != null)
            {
                NotificarAviso(ResourceMensagem.CaixaMovimentacaoService_OutroCaixaAbertoMesmoUsuario);
                return null;
            }

            if (await _caixaMovimentacaoRepository.Any(x => x.ContaFinanceiraId.Equals(abrirCaixaViewModel.ContaFinanceiraId) && !x.DataHoraFechamento.HasValue && x.UsuarioAberturaId == abrirCaixaViewModel.UsuarioId.Value))
            {
                NotificarAviso(ResourceMensagem.CaixaMovimentacaoService_CaixaAbertoPorOutroUsuario);
                return null;
            }

            return null;
        }

        private async Task CriarTransferenciaParaAjustarSaldoCaixa(Guid contaFinanceiraCaixaId, List<SaldoAberturaFormaRecebimentoViewModel> formasRecebimento)
        {
            // obter o id do ContaCofre
            var contaCofreId = await _contaFinanceiraRepository.ObterContaCofrePorLoja(_aspNetUserInfo.LojaId.Value);

            var observacao = $"Lançamento para alterar o saldo inicial do caixa, conforme solicitado pelo usuário: {_aspNetUserInfo.Nome}";

            foreach (var formaRecebimento in formasRecebimento)
            {
                OperacaoTransferenciaViewModel operacao = null;

                // O saldo foi alterado e o novo valor é maior que o antigo (realizar transferencia do cofre para o caixa)
                if (formaRecebimento.Saldo != formaRecebimento.NovoSaldo && formaRecebimento.NovoSaldo > formaRecebimento.Saldo)
                {
                    operacao = new OperacaoTransferenciaViewModel
                    {
                        ContaFinanceiraIdEntrada = contaFinanceiraCaixaId,
                        ContaFinanceiraIdSaida = contaCofreId,
                        FormaPagamentoRecebimentoId = formaRecebimento.FormaRecebimentoId,
                        Valor = formaRecebimento.NovoSaldo - formaRecebimento.Saldo,
                        DataEmissao = DateTime.UtcNow,
                        Observacao = observacao,
                        LogAuditoriaTela = Data.Enums.LogAuditoriaTela.PDV
                    };

                }
                // O saldo foi alterado e o novo valor é menor que o antigo (realizar transferencia do caixa para o cofre)
                else if (formaRecebimento.Saldo != formaRecebimento.NovoSaldo && formaRecebimento.NovoSaldo < formaRecebimento.Saldo)
                {
                    operacao = new OperacaoTransferenciaViewModel
                    {
                        ContaFinanceiraIdEntrada = contaCofreId,
                        ContaFinanceiraIdSaida = contaFinanceiraCaixaId,
                        FormaPagamentoRecebimentoId = formaRecebimento.FormaRecebimentoId,
                        Valor = formaRecebimento.Saldo - formaRecebimento.NovoSaldo,
                        DataEmissao = DateTime.UtcNow,
                        Observacao = observacao,
                        LogAuditoriaTela = Data.Enums.LogAuditoriaTela.PDV
                    };
                }

                // Se houver uma operação, gera a transferência
                if (operacao != null)
                {
                    await _operacaoTransferenciasDinheiroService.Transferencia(operacao, false);
                }

				await _contaFinanceiraSaldoService.AtualizarSaldo(contaFinanceiraCaixaId, formaRecebimento.FormaRecebimentoId, formaRecebimento.NovoSaldo);
            }
        }

        public async Task ReabrirCaixa(Guid caixaMovimentacaoId)
        {
            // Obter caixa com conferencias
            var caixaMovimentacao = await _caixaMovimentacaoRepository.FirstOrDefault(x => x.Id == caixaMovimentacaoId);

            if (caixaMovimentacao == null)
            {
                NotificarAvisoRegistroNaoEncontrado("caixa movimentação");
                return;
            }

            // Validar Reabertura
            var caixaAberto = await ValidarAberturaDeCaixa(new AbrirCaixaViewModel { ContaFinanceiraId = caixaMovimentacao.ContaFinanceiraId, UsuarioId = Guid.Parse(_aspNetUserInfo.Id) });
            if (PossuiAvisos() || PossuiErros())
                return;

            // Validar se foi o ultimo caixa fechado
            if (!await _caixaMovimentacaoRepository.UltimoCaixaFechado(_aspNetUserInfo.LojaId.Value, caixaMovimentacao.DataHoraFechamento.Value))
            {
                NotificarAviso(ResourceMensagem.CaixaMovimentacaoService_ReabrirSomenteUltimoCaixa);
                return;
            }

            // Corrijo o saldo das forma de recebimento da conta financeira
            // Deve ser realizad para retornar o saldo das formas de recebimento ao que eram antes do fechamento do caixa
            await CorrigirSaldoFormaRecebimentoParaReabrirCaixa(caixaMovimentacao.ContaFinanceiraId, caixaMovimentacao.Id);

            // Removo as conferencias
            await _caixaConferenciaRepository.LimparConferenciaReabrirCaixa(caixaMovimentacaoId);

            // Limpo as informacoes do fechamento do caixa no Caixa Movimentacao
            caixaMovimentacao.DataHoraFechamento = null;
            caixaMovimentacao.UsuarioFechamentoId = null;
            caixaMovimentacao.SaldoFechamento = 0;

            await _caixaMovimentacaoRepository.SaveChanges();

            var nomeContaFinanceira = await _contaFinanceiraRepository.FirstOrDefaultAsNoTracking(x => x.Id == caixaMovimentacao.ContaFinanceiraId,
                                                                                                  x => new ContaFinanceira { Nome = x.Nome });

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel
            {
                Tela = LogAuditoriaTela.CONTROLE_CAIXA,
                Operacao = LogAuditoriaOperacao.ALTERAR,
                UsuarioId = caixaMovimentacao.UsuarioAberturaId,
                Descricao = $"{nomeContaFinanceira.Nome}: Reabrir"
            });
        }

        private async Task CorrigirSaldoFormaRecebimentoParaReabrirCaixa(Guid contaFinanceiraId, Guid caixaMovimentacaoId)
        {
            // Remover a diferença do entre e o saldo e a conferencia de cada forma de recebimento
            var contaFinanceiraSaldo = await _contaFinanceiraSaldoService.ObterPorContaFinanceira(contaFinanceiraId);

            var movimentacoes = await _caixaMovimentacaoRepository.ObterMovimentacoes(caixaMovimentacaoId);

            foreach (var formaRecebimentoSaldo in contaFinanceiraSaldo)
            {
                // Obter o saldo de movimentacoes da forma de recebimento no caixa
                var formaConferencia = movimentacoes.Where(x => x.Id == formaRecebimentoSaldo.FormaPagamentoRecebimentoId).ToList();

                if (formaConferencia == null)
                    continue;

                var novoSaldo = formaRecebimentoSaldo.Saldo - formaConferencia.Sum(x => x.Valor);

                await _contaFinanceiraSaldoService.AtualizarSaldo(contaFinanceiraId, formaRecebimentoSaldo.FormaPagamentoRecebimentoId, novoSaldo);
            }
        }

        public async Task<Guid?> AbrirCaixaMovel(
            Guid lojaId,
            string identificador,
            Guid agrupamentoIntegracaoId,
            Guid usuarioId)
        {
            var contaFinanceira = await _contaFinanceiraRepository.FirstOrDefaultAsNoTracking(
                x => x.LojaId == lojaId
				  && x.Dispositivo.Identificador == identificador
                  && x.Dispositivo.ReferenciaServico == ReferenciaServicoStargate.DISPOSITIVO_SMART_POS,
				x => new ContaFinanceira
				{
					Id = x.Id,
					Saldo = x.Saldo,
					Nome = x.Nome
				});

            if (contaFinanceira is null)
            {
                NotificarAvisoRegistroNaoEncontrada("conta financeira");
				return null;
			}

			var caixaMovimentacaoAgrupamento = await _caixaMovimentacaoRepository.FirstOrDefaultAsNoTracking(
				x => x.ContaFinanceiraId == contaFinanceira.Id
				  && x.AgrupamentoIntegracaoId == agrupamentoIntegracaoId);

            if (caixaMovimentacaoAgrupamento is not null)
            {
                if (caixaMovimentacaoAgrupamento.FoiFechado())
                {
                    NotificarAviso("Esse caixa já foi fechado.");
                    return null;
                }

                if (caixaMovimentacaoAgrupamento.UsuarioAberturaId != usuarioId)
                {
                    NotificarAviso(ResourceMensagem.CaixaMovimentacaoService_CaixaAbertoPorOutroUsuario);
                    return null;
                }

				// Se o caixa já estiver aberto, para o mesmo usuário, retorna o id do caixa movimentação
                return caixaMovimentacaoAgrupamento.Id;
			}

			var caixaMovimentacao = new CaixaMovimentacao
			{
				ContaFinanceiraId = contaFinanceira.Id,
                AgrupamentoIntegracaoId = agrupamentoIntegracaoId,
				UsuarioAberturaId = usuarioId,
				SaldoAbertura = contaFinanceira.Saldo,
				DataHoraAbertura = DateTime.UtcNow,
			};

			await _caixaMovimentacaoRepository.Insert(caixaMovimentacao);

			await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel
			{
				Tela = LogAuditoriaTela.SMART_POS,
				Operacao = LogAuditoriaOperacao.CADASTRAR,
				UsuarioId = usuarioId,
				Descricao = $"{contaFinanceira.Nome}: Abrir caixa"
			});

            return caixaMovimentacao.Id;
		}

        public void Dispose()
        {
            _formaPagamentoService.Dispose();
            _operacaoTransferenciasDinheiroService?.Dispose();
            _dispositivoRepository?.Dispose();
            _logAuditoriaService?.Dispose();
            _contaFinanceiraSaldoService?.Dispose();
            _caixaMovimentacaoRepository?.Dispose();
            _contaFinanceiraRepository?.Dispose();
            _caixaConferenciaRepository?.Dispose();
        }
    }
}
