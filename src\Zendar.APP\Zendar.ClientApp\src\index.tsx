import React, { Suspense } from 'react';
import ReactDOM from 'react-dom';

import App from './App';
import './assets/css/lite-purple.css';
import './i18n';

import Loading from './components/Layout/Loading/LoadingPadrao';

ReactDOM.render(
  <React.StrictMode>
    <Suspense
      fallback={
        <div style={{ minHeight: '100vh', position: 'relative' }}>
          <Loading />
        </div>
      }
    >
      <App />
    </Suspense>
  </React.StrictMode>,
  document.getElementById('root')
);
