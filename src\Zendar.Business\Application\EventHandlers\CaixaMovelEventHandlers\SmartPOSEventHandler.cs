﻿using MediatR;
using System.Threading;
using System.Threading.Tasks;
using Zendar.Business.Application.Events.SmartPOSEvents;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.ViewModels;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Mediator;

namespace Zendar.Business.Application.EventHandlers.CaixaMovelEventHandlers
{
    public class SmartPOSEventHandler : Mediator.EventHandler,
                    INotificationHandler<ContaFinanceiraSmartPOSInativadaEvent>
    {
        private readonly ICaixaMovimentacaoRepository _caixaMovimentacaoRepository;
		private readonly IFechamentoCaixaService _fechamentoCaixaService;

		public SmartPOSEventHandler(
            INotificador notificador,
            IMediatorHandler mediator,
            ICaixaMovimentacaoRepository caixaMovimentacaoRepository,
            IFechamentoCaixaService fechamentoCaixaService)
            : base(notificador, mediator)
		{
			_caixaMovimentacaoRepository = caixaMovimentacaoRepository;
			_fechamentoCaixaService = fechamentoCaixaService;
		}

		public async Task Handle(ContaFinanceiraSmartPOSInativadaEvent notification, CancellationToken cancellationToken)
        {
            var caixaMovimentacoesAbertos =
                await _caixaMovimentacaoRepository.FindAllSelectAsNoTracking(
                    c => c.ContaFinanceira.Dispositivo.Identificador == notification.Identificador &&
                         c.ContaFinanceira.LojaId == notification.LojaId,
                    c => new Data.Models.Aplicacao.CaixaMovimentacao
                    {
                        Id = c.Id,
                        AgrupamentoIntegracaoId = c.AgrupamentoIntegracaoId
					});

            foreach (var caixaAberto in caixaMovimentacoesAbertos)
			    await _fechamentoCaixaService.FecharCaixaPDVExterno(new AbrirFecharCaixaDispositivoViewModel
                {
                    Identificador = notification.Identificador,
                    LojaId = notification.LojaId,
                    UsuarioId = notification.UsuarioId,
					CaixaMovimentacaoId = caixaAberto.Id,
					AgrupamentoIntegracaoId = caixaAberto.AgrupamentoIntegracaoId,
				});
        }
    }
}
