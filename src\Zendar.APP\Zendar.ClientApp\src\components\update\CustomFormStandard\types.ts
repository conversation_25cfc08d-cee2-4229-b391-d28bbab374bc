import OptionType from 'types/optionType';

export type ListarSelectProps = {
  nome: string;
  id: string;
};

export type Options = {
  label: string;
  value: string | number;
};

export type CamposPersonalizadosProps = {
  campoPersonalizadoId: string;
  valor: string;
};

export type ListaRelatorioPersonalizadoProps = {
  type:
    | 'ProdutosComPreco'
    | 'ProdutosVendidos'
    | 'RelatorioCompras'
    | 'Personalizados'
    | 'Estoque';
  isLoading: boolean;
  optionsRelatorio?: Options[];
  obterOpcoesLocaisDeEstoque?: (
    pesquisa?: string,
    forcarPesquisa?: boolean
  ) => Promise<OptionType[]>;
};

export type ClienteProps = {
  id: string;
  nome: string;
  endereco: string;
  codigo: number;
  cpfCnpj: string;
};

export type ProdutoProps = {
  ativo: boolean;
  id: string;
  nome: string;
  nomeEcommerce: string | null;
  padraoSistema: boolean;
};
