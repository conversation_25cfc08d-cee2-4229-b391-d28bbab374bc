﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using Zendar.Data.Contexts;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.ViewModels.Dispositivo;

namespace Zendar.Data.Repository.Aplicacao
{
    public class DispositivoRepository : RepositoryAplicacao<Dispositivo>, IDispositivoRepository
    {
        public DispositivoRepository(AplicacaoContexto db) : base(db)
        {
        }

        public GridPaginadaRetorno<DispositivoPaginadoViewModel> ListarPaginado(
            GridPaginadaConsulta gridPaginada,
            string nome,
            Guid lojaId,
            ReferenciaServicoStargate referenciaServico,
            bool? ativo = null)
        {
            var gridPaginadaRetorno = new GridPaginadaRetorno<DispositivoPaginadoViewModel>();

            var query = DbSet
                 .Where(x => (string.IsNullOrEmpty(nome) || EF.Functions.Collate(x.Apelido, Collates.RemoverCaracteresEspeciaisAcentos).Contains(nome)) &&
                            (ativo == null || x.Ativo == ativo) &&
                            x.LojaId == lojaId &&
                            x.ReferenciaServico == referenciaServico)
                   .OrderBy(x => x.Apelido)
                   .Select(x => new DispositivoPaginadoViewModel
                   {
                       Id = x.Id,
                       Apelido = x.ContaFinanceira.Nome,
                       Identificador = x.Identificador,
                       Ativo = x.Ativo
                   });

            gridPaginadaRetorno.CarregarPaginacao(query, gridPaginada);

            return gridPaginadaRetorno;
        }

        public async Task<Guid> ObterContaFinanceiraPeloIdentificador(
            Guid lojaId,
            string identificador,
            ReferenciaServicoStargate referenciaServico)
        {
            return await DbSet.Where(d => d.Identificador == identificador &&
                                          d.LojaId == lojaId &&
                                          d.ReferenciaServico == referenciaServico)
                              .Select(d => d.ContaFinanceiraId.Value)
                              .FirstOrDefaultAsync();
        }

        public async Task<int> ObterDispositivosAtivos(
            Guid lojaId,
            ReferenciaServicoStargate referenciaServico)
        {
            return await DbSet.CountAsync(d => d.LojaId == lojaId &&
                                               d.Ativo &&
                                               d.ReferenciaServico == referenciaServico);
        }

        public async Task<IList<Dispositivo>> ObterComContaFinanceiraParaAlteracao(
            params Guid[] dispositivosId)
        {
            return await DbSet.Where(x => dispositivosId.Contains(x.Id))
                              .Include(x => x.ContaFinanceira)
                              .ToListAsync();
        }

        public async Task<int> ObterUltimoCodigoCadastrado(
            Guid lojaId)
        {
            return await DbSet.MaxAsync(x => (int?)x.Codigo) ?? 0;
        }

        public async Task<bool> ValidarDispositivoLoja(
            Guid lojaId,
            string identificador,
            ReferenciaServicoStargate referenciaServico)
        {
            return await DbSet.AnyAsync(x => x.Identificador == identificador &&
                                             x.Ativo &&
                                             x.LojaId == lojaId &&
                                             x.ReferenciaServico == referenciaServico);
        }

        public async Task<Guid> ObterIdDispositivo(
            Guid lojaId,
            string identificador,
            ReferenciaServicoStargate referenciaServico)
            => await DbSet.Where(x => x.LojaId == lojaId &&
                                      x.Identificador == identificador &&
                                      x.ReferenciaServico == referenciaServico)
                          .Select(x => x.Id)
                          .FirstOrDefaultAsync();

        public async Task<List<Guid>> ObterListaDispositivoPorLojaIdEReferenciaServicoStargate(
            Guid? lojaId,
            ReferenciaServicoStargate referenciaServico)
        {
            return await DbSet.Where(d => d.LojaId == lojaId &&
                                          d.ReferenciaServico == referenciaServico)
                              .Select(d => d.Id)
                              .ToListAsync();
        }

        public async Task<Dispositivo> ObterDispositivo(
            Guid lojaId,
            string identificador,
            string apelido,
            ReferenciaServicoStargate referenciaServico)
        {
            return await DbSet
                .Where(d =>
                        (d.Identificador == identificador || d.Apelido == apelido) &&
                        d.LojaId == lojaId &&
                        d.ReferenciaServico == referenciaServico)
                .Select(d => new Dispositivo
                {
                    Id = d.Id,
                    Identificador = d.Identificador,
                    Apelido = d.Apelido,
                    Origem = d.Origem,
                    Codigo = d.Codigo,
                    Ativo = d.Ativo,
                    DataHoraUltimaSincronizacao = d.DataHoraUltimaSincronizacao,
                    ContaFinanceira = d.ContaFinanceira == null ? null : new ContaFinanceira
                    {
                        SerialPOS = d.ContaFinanceira.SerialPOS
                    }
                })
                .FirstOrDefaultAsync();
        }
    }
}