﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Multiempresa.Shared.Extension;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Text.RegularExpressions;
using Zendar.Business.Extensions;
using Zendar.Business.Helpers;
using Zendar.Business.Helpers.Extensions;
using Zendar.Business.Interfaces;
using Zendar.Data.Enums.Stargate;

namespace Zendar.Business.Helpers
{
    public class AspNetUserInfo : IAspNetUserInfo
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IHostingEnvironment _environment;

        private string CodigoContaEmpresaMultiEmpresa { get; set; } = null;

        private Guid? LojaIdAuxiliar { get; set; } = null;

        private string UsuarioIdAuxiliar { get; set; } = null;

        public AspNetUserInfo(IHttpContextAccessor httpContextAccessor, IHostingEnvironment environment)
        {
            _httpContextAccessor = httpContextAccessor;
            _environment = environment;
        }

        public string Token
        {
            get
            {
                string token = _httpContextAccessor.HttpContext.Request.Headers["Authorization"];

                // Remover o prefixo "Bearer "
                if (!string.IsNullOrEmpty(token) && token.StartsWith("Bearer "))
                {
                    token = token.Substring("Bearer ".Length).Trim();
                }

                return token;
            }
        }

        public string Nome => _httpContextAccessor.HttpContext.User.Identity.Name;

        public string NomeHeaderDesktop
        {
            get
            {
                var headers = _httpContextAccessor.HttpContext?.Request?.Headers;
                if (headers != null && headers.TryGetValue("X-User-Name", out var nomeHeader))
                {
                    return nomeHeader.ToString();
                }

                return string.Empty;
            }
        }

        public string Email => _httpContextAccessor.HttpContext.User.ObterEmailUsuario();

        public string Id => UsuarioIdAuxiliar ?? _httpContextAccessor.HttpContext.User.ObterIdUsuario();

        public string CodigoContaEmpresa => CodigoContaEmpresaMultiEmpresa ?? _httpContextAccessor.HttpContext?.User?.ObterCodigoContaEmpresa();

        public string HostUrl => ObterHostUrl(); // _environment.IsTesting() ? "localhost" : _httpContextAccessor.HttpContext?.Request?.GetTypedHeaders()?.Referer?.Host?.ToLower().Split(".")[0];

        public string Url => ObterUrl();

        public bool Autenticado => _httpContextAccessor.HttpContext.User.Identity.IsAuthenticated;

        public bool UsuarioAdministrador => _httpContextAccessor.HttpContext.User.ObterFlagAdministrador();

        public bool Trigger { get; set; } = true;

        public string SecurityStamp => _httpContextAccessor.HttpContext?.User?.ObterSecurityStamp();

        public Guid? LojaId => LojaIdAuxiliar ?? _httpContextAccessor.HttpContext?.Request.Headers
                .Where(x => x.Key == "lojaid")
                .Select(x => new Guid(x.Value))
                .FirstOrDefault();

        public TipoSistema Sistema => ObterSistema();

        public Guid? ChavePermissaoTemporaria => _httpContextAccessor.HttpContext?.Request.Headers
                .Where(x => x.Key == "chave-permissao-temporaria")
                .Select(x => new Guid(x.Value))
                .FirstOrDefault();

        public int? TimezoneOffset => _httpContextAccessor.HttpContext?.Request.Headers
         .Where(x => x.Key == "timezone-offset")
         .Select(x => int.Parse(x.Value))
         .FirstOrDefault();

        public string Identificador =>
            _httpContextAccessor.HttpContext.User.ObterIdentificador();

        public string DispositivoId
            => _httpContextAccessor.HttpContext.User.ObterIdDispositivo();

        public string RefreshToken
            => _httpContextAccessor.HttpContext.User.ObterRefreshToken();

        public ReferenciaServicoStargate? Servico => ObterServico();

        public List<TokenServico> Servicos => _httpContextAccessor.HttpContext.User.ObterServicosUsuario(LojaId.Value);

        public void PreencherCodigoContaEmpresa(string codigoContaEmpresa)
        {
            CodigoContaEmpresaMultiEmpresa = codigoContaEmpresa;
        }

        public string ObterLoginCacheKey(string userId, ReferenciaServicoStargate? referenciaServico)
        {
            if (string.IsNullOrEmpty(userId))
                return default;

            var cacheKey = new StringBuilder(userId);
            if (referenciaServico.HasValue)
                cacheKey.Append($"_{referenciaServico.Value}");

            return cacheKey.ToString();
        }

        private string ObterUrl()
        {
            if (_environment.IsTesting() || _environment.IsDevelopment())
                return "http://localhost:3000";
            else
            {
                var referer = _httpContextAccessor.HttpContext.Request.GetTypedHeaders().Referer;
                if (referer != null)
                    return Regex.Replace(referer?.AbsoluteUri, $"{referer?.AbsolutePath}$", "");
                return "https://localhost.com.br";
            }
        }

        private string ObterHostUrl()
        {
            //if (_environment.IsTesting() || _environment.IsDevelopment())
            //{
            //    return "localhost:3000";
            //}

            if (!string.IsNullOrEmpty(_httpContextAccessor.HttpContext?.Request.Headers["HTTP_REFERER_MULTIEMPRESA"]))
            {
                return _httpContextAccessor.HttpContext?.Request.Headers["HTTP_REFERER_MULTIEMPRESA"].ToString().ToLower();
            }

            if (_httpContextAccessor.HttpContext?.Request?.GetTypedHeaders()?.Referer?.Host != null)
            {
                return _httpContextAccessor.HttpContext?.Request?.GetTypedHeaders()?.Referer?.Host?.ToLower().Split("/")[0];
            }

            return string.Empty;
        }

        private ReferenciaServicoStargate? ObterServico()
        {
            var referenciaServico = _httpContextAccessor?.HttpContext?.Request?.Headers?
                         .Where(x => x.Key == "referencia-servico")?
                         .Select(x => (x.Value))?
                         .FirstOrDefault()
                         .ToString();

            if (string.IsNullOrEmpty(referenciaServico))
                return null;

            try
            {
                return (ReferenciaServicoStargate)Enum.Parse(typeof(ReferenciaServicoStargate), referenciaServico, true);
            }
            catch (Exception)
            {
                return null;
            }
        }

        private TipoSistema ObterSistema()
        {
            var sistema = _httpContextAccessor.HttpContext?.Request.Headers
                          .Where(x => x.Key == "sistema")
                          .Select(x => (x.Value))
                          .FirstOrDefault()
                          .ToString();

            if (!string.IsNullOrEmpty(sistema))
            {
                try
                {
                    return (TipoSistema)Enum.Parse(typeof(TipoSistema), sistema, true);
                }
                catch
                {
                    return TipoSistema.ZENDAR;
                }

            }
            else
            {
                var sistemaUrl = ObterSistemaUrlPeloDominio();
                return (TipoSistema)Enum.Parse(typeof(TipoSistema), sistemaUrl, true);
            }
        }

        private string ObterSistemaUrlPeloDominio()
        {
            var hostUrl = ObterHostUrl();

            if (hostUrl.Contains(TipoSistema.ZENDAR.ObterDescricao(), StringComparison.OrdinalIgnoreCase))
                return TipoSistema.ZENDAR.ObterDescricao();

            if (hostUrl.Contains(TipoSistema.POWERSTOCK.ObterDescricao(), StringComparison.OrdinalIgnoreCase))
                return TipoSistema.POWERSTOCK.ObterDescricao();

            if (hostUrl.Contains(TipoSistema.FOMER.ObterDescricao(), StringComparison.OrdinalIgnoreCase))
                return TipoSistema.FOMER.ObterDescricao();

            if (hostUrl.Contains(TipoSistema.POWERCHEF.ObterDescricao(), StringComparison.OrdinalIgnoreCase))
                return TipoSistema.POWERCHEF.ObterDescricao();

            return TipoSistema.ZENDAR.ObterDescricao();
        }

        public void PreencherLojaId(Guid? lojaId)
        {
            LojaIdAuxiliar = lojaId;
        }

        public void PreencherUsuarioId(string id)
        {
            UsuarioIdAuxiliar = id;
        }

        public void PreencherToken(string token)
        {
            throw new NotImplementedException();
        }

        public void PreencherHostUrl(string hostUrl)
        {
            throw new NotImplementedException();
        }

        public void PreencherTimezoneOffset(int timezoneOffset)
        {
            throw new NotImplementedException();
        }

        public void HabilitarTrigger(bool trigger)
        {
            Trigger = trigger;
        }

        public bool PossuiServico(ReferenciaServicoStargate servico)
        {
            if (Servicos == null) return false;

            bool possuiServico = Servicos.Any(s => s.ReferenciaServico == servico && s.LojaId == LojaId.Value);

            return possuiServico;
        }
    }
}

public static class ClaimsPrincipalExtensions
{
    public static string ObterRefreshToken(this ClaimsPrincipal principal)
    {
        if (principal == null)
        {
            throw new ArgumentException(nameof(principal));
        }

        var claim = principal.FindFirst(ClaimTypesCustom.RefreshToken);

        return claim?.Value;
    }

    public static string ObterIdentificador(this ClaimsPrincipal principal)
    {
        if (principal == null)
        {
            throw new ArgumentException(nameof(principal));
        }

        var claim = principal.FindFirst(ClaimTypesCustom.Identificador);

        return claim?.Value;
    }

    public static string ObterIdDispositivo(this ClaimsPrincipal principal)
    {
        if (principal == null)
        {
            throw new ArgumentException(nameof(principal));
        }

        var claim = principal.FindFirst(ClaimTypesCustom.DispositivoId);

        return claim?.Value;
    }

    public static string ObterIdUsuario(this ClaimsPrincipal principal)
    {
        if (principal == null)
        {
            throw new ArgumentException(nameof(principal));
        }

        var claim = principal.FindFirst(ClaimTypesCustom.UserId);

        return claim?.Value;
    }

    public static string ObterEmailUsuario(this ClaimsPrincipal principal)
    {
        if (principal == null)
        {
            throw new ArgumentException(nameof(principal));
        }

        var claim = principal.FindFirst(ClaimTypes.Email);

        return claim?.Value;
    }

    public static string ObterCodigoContaEmpresa(this ClaimsPrincipal principal)
    {
        if (principal == null)
        {
            throw new ArgumentException(nameof(principal));
        }

        var claim = principal.FindFirst(ClaimTypesCustom.CodigoContaEmpresa);

        return claim?.Value;
    }

    public static string ObterSecurityStamp(this ClaimsPrincipal principal)
    {
        if (principal == null)
        {
            throw new ArgumentException(nameof(principal));
        }

        var claim = principal.FindFirst(ClaimTypesCustom.SecurityStamp);

        return claim?.Value;
    }

    public static bool ObterFlagAdministrador(this ClaimsPrincipal principal)
    {
        if (principal == null)
        {
            throw new ArgumentException(nameof(principal));
        }

        var claim = principal.FindFirst(ClaimTypesCustom.Administrador);
        return Convert.ToBoolean(claim?.Value);
    }

    public static List<TokenServico> ObterServicosUsuario(this ClaimsPrincipal principal, Guid lojaId)
    {
        if (principal == null)
        {
            throw new ArgumentException(nameof(principal));
        }

        var claim = principal.FindFirst(ClaimTypesCustom.Servicos);
        var servicosUsuario = JsonConvert.DeserializeObject<List<TokenServico>>(claim?.Value);

        if (servicosUsuario == null) return servicosUsuario;

        var result = servicosUsuario.Where(s => s.LojaId == lojaId).ToList();
        return result;
    }
}
