﻿using Multiempresa.Shared.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Zendar.Business.Helpers.Extensions;
using Zendar.Business.Interfaces;
using Zendar.Data.Enums.Stargate;


namespace Zendar.Business.Helpers
{
    public class AspNetUserInfoAuxiliar : IAspNetUserInfo
    {
        public AspNetUserInfoAuxiliar()
        {
        }

        private string CodigoContaEmpresaMultiEmpresa { get; set; } = null;

        public string Token { get; set; }

        public string Nome { get; set; }

        public string NomeHeaderDesktop { get; set; }

        public string Email { get; set; }

        public string Id { get; set; }

        public string CodigoContaEmpresa { get; set; }

        public string HostUrl { get; set; }

        public string Url { get; set; }

        public bool Autenticado { get; set; }

        public string SecurityStamp { get; set; }

        public Guid? LojaId { get; set; }

        public TipoSistema Sistema => ObterSistema();

        public Guid? ChavePermissaoTemporaria { get; set; }

        public int? TimezoneOffset { get; set; }

        public string Identificador { get; set; }

        public string DispositivoId { get; set; }

        public string RefreshToken { get; set; }

        public bool UsuarioAdministrador { get; set; }

        public ReferenciaServicoStargate? Servico { get; set; }

        public bool Trigger { get; set; }

        public List<TokenServico> Servicos { get; set; }

        public void PreencherCodigoContaEmpresa(string codigoContaEmpresa)
        {
            CodigoContaEmpresaMultiEmpresa = codigoContaEmpresa;
            CodigoContaEmpresa = codigoContaEmpresa;
        }

        public void PreencherToken(string token)
        {
            Token = token;
        }

        public void PreencherLojaId(Guid? lojaId)
        {
            LojaId = lojaId;
        }

        public void PreencherUsuarioId(string id)
        {
            Id = id;
        }

        public void PreencherHostUrl(string hostUrl)
        {
            HostUrl = hostUrl;
        }

        public void PreencherTimezoneOffset(int timezoneOffset)
        {
            TimezoneOffset = timezoneOffset;
        }

        public void HabilitarTrigger(bool trigger)
        {
            Trigger = trigger;
        }

        private TipoSistema ObterSistema()
        {
            var sistemaUrl = ObterSistemaUrlPeloDominio();

            return (TipoSistema)Enum.Parse(typeof(TipoSistema), sistemaUrl, true);
        }

        private string ObterSistemaUrlPeloDominio()
        {
            if (HostUrl != null)
            {
                if (HostUrl.Contains(TipoSistema.ZENDAR.ObterDescricao(), StringComparison.OrdinalIgnoreCase))
                    return TipoSistema.ZENDAR.ObterDescricao();

                if (HostUrl.Contains(TipoSistema.POWERSTOCK.ObterDescricao(), StringComparison.OrdinalIgnoreCase))
                    return TipoSistema.POWERSTOCK.ObterDescricao();

                if (HostUrl.Contains(TipoSistema.FOMER.ObterDescricao(), StringComparison.OrdinalIgnoreCase))
                    return TipoSistema.FOMER.ObterDescricao();

                if (HostUrl.Contains(TipoSistema.POWERCHEF.ObterDescricao(), StringComparison.OrdinalIgnoreCase))
                    return TipoSistema.POWERCHEF.ObterDescricao();
            }

            return TipoSistema.ZENDAR.ObterDescricao();
        }

        public string ObterLoginCacheKey(string userId, ReferenciaServicoStargate? referenciaServico)
        {
            if (string.IsNullOrEmpty(userId))
                return default;

            var cacheKey = new StringBuilder(userId);
            if (referenciaServico.HasValue)
                cacheKey.Append($"_{referenciaServico.Value}");

            return cacheKey.ToString();
        }

        public bool PossuiServico(ReferenciaServicoStargate servico)
        {
            if (Servicos == null) return false;

            bool possuiServico = Servicos.Any(s => s.ReferenciaServico == servico && s.LojaId == LojaId.Value);

            return possuiServico;
        }
    }
}
