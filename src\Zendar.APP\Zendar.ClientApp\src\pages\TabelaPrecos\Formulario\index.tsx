import { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import { SimpleCard } from 'components/update/Form/SimpleCard';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';

import { NameAndStatus } from './components/Forms/NameAndStatus';
import { Regras } from './components/Forms/Regras';
import { ListPrecosFixos } from './components/Lists/ListPrecosFixos';
import { ListPrecosVariacao } from './components/Lists/ListPrecosVariacao';
import { SelecionarLojas } from './components/SelecionarLojas';
import { useTabelaPreco } from './hooks';
import { FormularioProps } from './types';

const Formulario = ({ isReadonly = false, isAlterar }: FormularioProps) => {
  const { watch, setValue } = useFormContext();

  const { isLoading } = useTabelaPreco();

  const padronizarPreco = watch('padronizarPreco');
  const padraoSistema = watch('padraoSistema');

  useEffect(() => {
    if (!padronizarPreco) {
      setValue('arredondamentoAcima', 0);
      setValue('arredondamentoAbaixo', 0);
    }
  }, [padronizarPreco, setValue]);

  return (
    <SimpleCard>
      {isLoading && <LoadingPadrao />}
      <SimpleGridForm columns={1}>
        <NameAndStatus isReadOnly={isReadonly || padraoSistema} />
        <Regras isReadOnly={isReadonly || padraoSistema} />
        <ListPrecosFixos
          isReadOnly={isReadonly}
          isAlterar={!!isAlterar}
          padraoSistema={padraoSistema}
          useTabelaPreco={useTabelaPreco}
        />
        <ListPrecosVariacao
          isReadOnly={isReadonly}
          isAlterar={!!isAlterar}
          useTabelaPreco={useTabelaPreco}
        />
        <SelecionarLojas
          label="Lojas habilitadas para esta tabela de preços"
          modalSubtitle="Marque apenas as lojas em que esta tabela de preços será aplicada"
          isReadonly={isReadonly || padraoSistema}
        />
      </SimpleGridForm>
    </SimpleCard>
  );
};

export default Formulario;
