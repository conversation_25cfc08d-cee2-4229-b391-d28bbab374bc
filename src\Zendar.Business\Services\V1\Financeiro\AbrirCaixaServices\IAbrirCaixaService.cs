﻿using System;
using System.Threading.Tasks;
using Zendar.Business.ViewModels;

namespace Zendar.Business.Services.Financeiro.AbrirCaixaServices
{
    public interface IAbrirCaixaService : IDisposable
    {
        Task<Guid?> AbrirCaixa(AbrirCaixaViewModel abrirCaixaViewModel);
        Task<SaldoCaixaAberturaViewModel> ObterSaldoParaAbertura(Guid contaFinanceiraId);
        Task ReabrirCaixa(Guid caixaMovimentacaoId);
        Task<Guid?> AbrirCaixaMovel(
            Guid lojaId,
			string identificador,
			Guid agrupamentoIntegracaoId,
			Guid usuarioId);
    }
}
