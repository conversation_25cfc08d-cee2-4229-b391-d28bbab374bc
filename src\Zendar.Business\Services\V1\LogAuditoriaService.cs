﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.ViewModels;
using Zendar.Data.Enums;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services
{
    public class LogAuditoriaService : BaseService, ILogAuditoriaService
    {
        private readonly ILogAuditoriaRepository _logAuditoriaRepository;
        private readonly IMapper _mapper;
        private readonly IAspNetUserInfo _aspNetUserInfo;

        public LogAuditoriaService(INotificador notificador,
                                   ILogAuditoriaRepository logAuditoriaRepository,
                                   IMapper mapper,
                                   IAspNetUserInfo aspNetUserInfo) : base(notificador)
        {
            _logAuditoriaRepository = logAuditoriaRepository;
            _mapper = mapper;
            _aspNetUserInfo = aspNetUserInfo;
        }

        public async Task Inserir(LogAuditoriaInserirViewModel logAuditoriaInserirViewModel)
        {
            var logAuditoria = _mapper.Map<LogAuditoria>(logAuditoriaInserirViewModel);

            logAuditoria.UsuarioId = logAuditoriaInserirViewModel.UsuarioId
                                     ?? (_aspNetUserInfo.Autenticado ? new Guid(_aspNetUserInfo.Id) : null);

            logAuditoria.UsuarioNome = !string.IsNullOrEmpty(logAuditoriaInserirViewModel.UsuarioNome)
                ? logAuditoriaInserirViewModel.UsuarioNome
                : _aspNetUserInfo.Autenticado && !string.IsNullOrEmpty(_aspNetUserInfo.Nome)
                    ? _aspNetUserInfo.Nome
                    : _aspNetUserInfo.NomeHeaderDesktop ?? string.Empty;

            logAuditoria.LojaId = logAuditoriaInserirViewModel.LojaId ?? _aspNetUserInfo.LojaId;

            await _logAuditoriaRepository.Insert(logAuditoria);
        }

        public GridPaginadaRetorno<LogAuditoriaPaginadoViewModel> ListarPaginado(GridPaginadaConsulta gridPaginada, LogAuditoriaFiltrosViewModel logAuditoriaFiltrosViewModel)
        {
            logAuditoriaFiltrosViewModel.LojaId = _aspNetUserInfo.LojaId?.ToString();
            return _logAuditoriaRepository.ListarPaginado(gridPaginada, logAuditoriaFiltrosViewModel);
        }

        public async Task<List<IdNomeViewModel>> ListarUsuarios()
        {
            return await _logAuditoriaRepository.ListarUsuarios();
        }

        public List<ListaCodigoNomeViewModel> ListarDescricaoTelas()
        {
            List<ListaCodigoNomeViewModel> resultado = new List<ListaCodigoNomeViewModel>();

            foreach (LogAuditoriaTela value in Enum.GetValues(typeof(LogAuditoriaTela)))
            {
                resultado.Add(new ListaCodigoNomeViewModel { Codigo = (int)value, Nome = EnumExtension.ObterDescricao(value) });
            }

            return resultado.OrderBy(x => x.Nome).ToList();
        }

        public void Dispose()
        {
            _logAuditoriaRepository?.Dispose();
        }
    }
}
