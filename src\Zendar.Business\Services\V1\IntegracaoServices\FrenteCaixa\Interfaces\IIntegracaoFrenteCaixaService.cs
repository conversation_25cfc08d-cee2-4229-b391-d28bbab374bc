﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.Business.Services.V1.IntegracaoServices.IntegracaoPausarService;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Fiscal;
using Zendar.Business.ViewModels.Integracao;
using Zendar.Business.ViewModels.Integracao.FrenteCaixa;
using Zendar.Business.ViewModels.V1.Fiscal;
using Zendar.Business.ViewModels.V1.Integracao.AutoAtendimento;
using Zendar.Business.ViewModels.V1.Integracao.FrenteCaixa;
using Zendar.Business.ViewModels.V2.CategoriaProdutoViewModels;
using Zendar.Business.ViewModels.V2.CategoriaProdutoViewModels.CategoriaProdutoComplementoViewModels;
using Zendar.Business.ViewModels.V2.Departamentos;
using Zendar.Business.ViewModels.V2.Entregadores;
using Zendar.Business.ViewModels.V2.FichaTecnica;
using Zendar.Business.ViewModels.V2.GerenciadorDeImpressao;
using Zendar.Business.ViewModels.V2.ProdutoPorEtapa;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;
using Zendar.Business.ViewModels.V2.Promocao;
using Zendar.Business.ViewModels.V2.SetorEntrega;
using Zendar.Data.Helpers;
using Zendar.Data.ViewModels.Dispositivo;
using Zendar.Data.ViewModels.Integracao.FrenteCaixa;
using Zendar.Integracao.ViewModel;

namespace Zendar.Business.Services.V1.IntegracaoServices.FrenteCaixa.Interfaces
{
    public interface IIntegracaoFrenteCaixaService : IDisposable, IAtivarInativarIntegracaoService
    {
        #region Integracao

        Task<IntegracaoObterViewModel> ObterIntegracao(
            Guid? lojaId = null);

        Task<Guid?> CadastrarIntegracao(
            IntegracaoViewModel integracaoViewModel);

        Task<FrenteCaixaConfiguracaoAlterarViewModel> ObterConfiguracaoParaAlterar();

        Task<Guid?> AlterarConfiguracao(
            FrenteCaixaConfiguracaoAlterarViewModel configuracaoAlterarViewModel);

        Task<FrenteCaixaConfiguracaoTefAlterarViewModel> ObterConfiguracaoTefParaAlterar();

        Task<Guid?> AlterarConfiguracaoTef(
            FrenteCaixaConfiguracaoTefAlterarViewModel configuracaoAlterarTefViewModel);

        Task<FrenteCaixaConfiguracaoTabelaPrecoAlterarViewModel> ObterConfiguracaoTabelaPrecoParaAlterar();

        Task<Guid?> AlterarConfiguracaoTabelaPreco(
            FrenteCaixaConfiguracaoTabelaPrecoAlterarViewModel configuracaoTabelaPrecoAlterarViewModel);

        Task Desistir();

        Task<FrenteCaixaClienteFornecedorRegraViewModel> ObterClienteFornecedorRegra(
            Guid clienteFornecedorId);

        Task<List<FrenteCaixaTefViewModel>> ObterListaOperacaoTef();

        Task<List<FrenteCaixaTefViewModel>> ObterListaParcelamentoTef();

        Task<List<FrenteCaixaTefViewModel>> ObterListaTipoCartaoTef();

        Task<string> GerarPinDispositivo();

        Task<FrenteCaixaQuantidadeAtivaDispositivoViewModel> ObterQuantidadeAtivaDispositivo();

        GridPaginadaRetorno<DispositivoPaginadoViewModel> ObterListaDispositivo(
            GridPaginadaConsulta gridPaginada,
            string nome,
            bool? ativo = null);

        Task InativarDispositivo(
            params Guid[] listaDispositivoId);

        Task AtivarDispositivo(
            params Guid[] listaDispositivoId);

        #endregion

        #region Sincronizacao

        Task<FrenteCaixaDispositivoViewModel> ObterDispositivo(
            string pin,
            string identificador,
            string apelido);

        Task<FrenteCaixaDispositivoViewModel> AtivarLicenca(
            FrenteCaixaAtivarLicencaViewModel ativarLicencaViewModel);

        Task MarcarDispositivoComDataHoraUltimaSincronizacao(
            Guid dispositivoId);

        Task<FrenteCaixaDispositivoViewModel> ObterDadosPin(
            string pin);

        Task<Guid?> EnviarVenda(
            OperacaoIntegracaoViewModel operacaoIntegracaoViewModel);

        Task<Guid?> EnviarNotaFiscal(
            NotaFiscalViewModel notaFiscalViewModel);

        Task<Guid?> EnviarCupomSat(
            CupomSatViewModel cupomSatViewModel);

        Task<bool> EnviarInutilizacao(
            EnviarInutilizacaoViewModel enviarInutilizacaoViewModel);

        Task<FrenteCaixaConfiguracaoViewModel> ObterConfiguracao();

        Task<List<LojaViewModel>> ObterListaLoja(
            DateTime? dataAtualizacao);

        Task<List<MarcaViewModel>> ObterListaMarca(
            DateTime? dataAtualizacao);

        Task<List<CorViewModel>> ObterListaCor(
            DateTime? dataAtualizacao);

        Task<List<TamanhoViewModel>> ObterListaTamanho(
            DateTime? dataAtualizacao);

        Task<List<CategoriaProdutoViewModel>> ObterListaCategoria(
            DateTime? dataAtualizacao);

        Task<List<ProdutoObservacaoV2ViewModel>> ObterListaObservacao(
            DateTime? dataAtualizacao);

        Task<List<CategoriaProdutoObservacaoViewModel>> ObterListaCategoriaObservacao(
            DateTime? dataAtualizacao);

        Task<List<CategoriaProdutoComplementoViewModel>> ObterListaCategoriaComplemento(
            DateTime? dataAtualizacao);

        Task<List<UnidadeMedidaViewModel>> ObterListaUnidade(
            DateTime? dataAtualizacao);

        Task<List<FrenteCaixaFatorConversaoViewModel>> ObterListaFatorConversao(
            DateTime? dataAtualizacao);

        Task<List<FrenteCaixaAliquotaIcmsViewModel>> ObterListaAliquotaIcms(
            DateTime? dataAtualizacao);

        Task<List<VendedorViewModel>> ObterListaVendedor(
            DateTime? dataAtualizacao);

        Task<List<FrenteCaixaUsuarioViewModel>> ObterListaUsuario(
            DateTime? dataAtualizacao);

        Task<List<FrenteCaixaUsuarioPermissaoViewModel>> ObterListaUsuarioPermissao(
            DateTime? dataAtualizacao);

        Task<List<FormaPagamentoRecebimentoViewModel>> ObterListaFormaPagamentoRecebimento(
            DateTime? dataAtualizacao);

        Task<List<RegraFiscalViewModel>> ObterListaRegraFiscal(
            Guid? regraFiscalId,
            DateTime? dataAtualizacao);

        Task<List<ClienteFornecedorViewModel>> ObterListaCliente(
            GridPaginadaConsulta gridPaginada,
            DateTime? dataAtualizacao);

        Task<List<ProdutoV2ViewModel>> ObterListaProduto(
            GridPaginadaConsulta gridPaginada,
            DateTime? dataAtualizacao);

        Task<List<SetorEntregaViewModel>> ObterListaSetorEntrega(
            DateTime? dataAtualizacao);

        Task<List<PromocaoViewModel>> ObterListaPromocao(
            DateTime? dataAtualizacao);

        Task<List<DepartamentoViewModel>> ObterListaDepartamento(
            DateTime? dataAtualizacao);

        Task<List<EntregadorObterViewModel>> ObterListaEntregador(
            DateTime? dataAtualizacao);

        Task<List<ProdutoFichaTecnicaViewModel>> ObterListaProdutoFichaTecnica(
            DateTime? dataAtualizacao);

        Task<List<EtapaProdutoObterViewModel>> ObterListaEtapaProduto(
            DateTime? dataAtualizacao);

        Task<List<LojaServicoViewModel>> ObterListaLojaServico(
            DateTime? dataAtualizacao);

        Task<List<ConfiguracaoGerenciadorImpressaoObterViewModel>> ObterListaConfiguracaoGerenciadorImpressao(
            DateTime? dataAtualizacao);

        Task<List<GerenciadorImpressaoObterViewModel>> ObterListaGerenciadorImpressao(
            DateTime? dataAtualizacao);

        Task<List<DepartamentoImpressoraObterViewModel>> ObterListaDepartamentoImpressora(
            DateTime? dataAtualizacao);

        Task<List<ImpressoraObterViewModel>> ObterListaImpressora(
            DateTime? dataAtualizacao);

        Task<List<MarketplaceViewModel>> ObterListaMarketplace(
            DateTime? dataAtualizacao);

        Task<List<CredenciadoraCartaoViewModel>> ObterListaCredenciadoraCartao(
            DateTime? dataAtualizacao);

        Task<List<PeriodoCaixaViewModel>> ObterListaPeriodoCaixa(
            DateTime? dataAtualizacao);

        Task<List<RelatorioPersonalizadoViewModel>> ObterListaRelatorioPersonalizado(
            DateTime? dataAtualizacao);

        Task<List<AutoAtendimentoArquivoViewModel>> ObterListaAutoAtendimento();

        Task<List<SincronizarNotificacaoViewModel>> ObterListaNotificacaoPorDispositivo(
            Guid dispositivoId);

        Task MarcarNotificacaoComoSincronizada(
            Guid notificacaoId);

        Task<List<SincronizarNotificacaoExclusaoViewModel>> ObterListaNotificacaoExclusaoPorDispositivo(
            Guid dispositivoId);

        Task MarcarNotificacaoExclusaoComoSincronizada(
            Guid notificacaoId);

        Task<Guid?> CadastrarItemRemovido(
            FrenteCaixaOperacaoItemRemovido item);

        #endregion

        #region Remover

        Task NotificarExportacao(
           NotificacaoViewModel notificacaoViewModel);

        Task NotificarScript(
           NotificacaoViewModel notificacaoViewModel);

        Task<string> ObterUrlScript();

        #endregion
    }
}