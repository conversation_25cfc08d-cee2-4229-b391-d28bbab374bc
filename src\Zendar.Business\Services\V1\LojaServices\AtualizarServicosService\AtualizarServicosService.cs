﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.V1.IntegracaoServices.IntegracaoPausarService;
using Zendar.Business.Services.V1.IntegracaoServices.SincronizacaoServices.Trigger;
using Zendar.Business.Services.V1.UsuarioServices.InativarUsuarioService;
using Zendar.Business.ViewModels.Loja;
using Zendar.Data.DomainServices.LojaServicosServices;
using Zendar.Data.DomainServices.UsuarioServices;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Interfaces;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoRepository;

namespace Zendar.Business.Services.V1.LojaServices.AtualizarServicosService
{
    public class AtualizarServicosService : BaseService, IAtualizarServicosService
    {
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IZendarTriggerService _zendarTriggerService;
        private readonly IDatabaseTransaction _databaseTransaction;
        private readonly ILojaRepository _lojaRepository;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IIntegracaoRepository _integracaoRepository;

        private readonly IInativarUsuarioService _inativarUsuarioService;
        private readonly IIntegracaoAtivarInativarService _integracaoAtivarInativarService;

        public AtualizarServicosService(
            INotificador notificador,
            IAspNetUserInfo aspNetUserInfo,
            IZendarTriggerService zendarTriggerService,
            IDatabaseTransaction databaseTransaction,
            ILojaRepository lojaRepository,
            IUsuarioRepository usuarioRepository,
            IIntegracaoRepository integracaoRepository,
            IInativarUsuarioService inativarUsuarioService,
            IIntegracaoAtivarInativarService integracaoAtivarInativarService)
            : base(notificador)
        {
            _aspNetUserInfo = aspNetUserInfo;
            _zendarTriggerService = zendarTriggerService;
            _databaseTransaction = databaseTransaction;
            _lojaRepository = lojaRepository;
            _usuarioRepository = usuarioRepository;
            _integracaoRepository = integracaoRepository;
            _inativarUsuarioService = inativarUsuarioService;
            _integracaoAtivarInativarService = integracaoAtivarInativarService;
        }

        public void Dispose()
        {
            _lojaRepository?.Dispose();
            _usuarioRepository?.Dispose();
            _integracaoRepository?.Dispose();
            _inativarUsuarioService?.Dispose();
            _integracaoAtivarInativarService?.Dispose();
        }

        public async Task Atualizar(AtualizarServicosViewModel atualizarServicos)
        {
            var loja = await _lojaRepository.ObterComServicos(l => l.AssinaturaId == atualizarServicos.AssinaturaId);
            if (loja is null)
            {
                NotificarAvisoRegistroNaoEncontrada("loja");
                return;
            }

            _databaseTransaction.BeginTransaction();

            try
            {
                loja.AtualizarServicos(servicosAtualizados: atualizarServicos.ObterServicos());
                await _lojaRepository.SaveChanges();

                await InativarUsuariosMaisRecentes();

                await AtivarInativarIntegracoes(loja);
            }
            catch
            {
                _databaseTransaction.Rollback();
                throw;
            }

            _databaseTransaction.Commit();

            #region Trigger

            _aspNetUserInfo.PreencherLojaId(loja.Id);

            var usuario = await _usuarioRepository.BuscarAdministrador();

            if (usuario != null)
                _aspNetUserInfo.PreencherUsuarioId(usuario.Id.ToString());

            await _zendarTriggerService.ExecuteGuid(loja.Id,
                                                TabelaTrigger.LOJA_SERVICO,
                                                OperacaoTrigger.ALTERAR);

            #endregion
        }

        private async Task InativarUsuariosMaisRecentes()
        {
            List<Loja> lojas = await ObterLojasComServicos();
            int quantidadeAtiva = await _usuarioRepository.ObterQuantidadeUsuariosAtivos();

            int tipoUsuarioQuantidadeExcedente =
                LicencaUsuarioService.CalcularQuantidadeExcedentePorTipoUsuario(lojas,
                                                                                quantidadeAtiva);

            if (tipoUsuarioQuantidadeExcedente <= 0)
                return;

            await _inativarUsuarioService.InativarMaisRecentes(TipoUsuario.SISTEMA, tipoUsuarioQuantidadeExcedente);
        }

        private async Task<List<Loja>> ObterLojasComServicos()
        {
            var lojas = await _lojaRepository.FindAllSelectAsNoTracking(x => new Loja
            {
                LojaServicos = x.LojaServicos
                .Select(l => new LojaServicos
                {
                    ReferenciaServico = l.ReferenciaServico,
                    TipoServico = l.TipoServico,
                    Quantidade = l.Quantidade,
                    DataBloqueio = l.DataBloqueio
                })
                .ToList()
            });

            return lojas.ToList();
        }

        private async Task AtivarInativarIntegracoes(
            Loja loja)
        {
            var listaIntegracao =
                await _integracaoRepository.ObterListaIntegracaoPorLojaId(loja.Id);

            foreach (var integracaoReferencia in loja.ObterListaIntegracaoParaAtivar(listaIntegracao))
                await _integracaoAtivarInativarService.AtivarInativarIntegracao(loja.Id, integracaoReferencia, true);

            foreach (var integracaoReferencia in loja.ObterListaIntegracaoAtivaParaInativar(listaIntegracao))
                await _integracaoAtivarInativarService.AtivarInativarIntegracao(loja.Id, integracaoReferencia, false);
        }
    }
}
