﻿using AutoMapper;
using Multiempresa.Shared.Enums;
using Multiempresa.Shared.Helpers.Convertores;
using Multiempresa.Shared.Helpers.Formatadores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zendar.Business.Consts;
using Zendar.Business.Helpers;
using Zendar.Business.Helpers.ImpressaoRelatorioPdf;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.V2.TabelaPrecoV2Services.TabelaPrecoV2Service;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Relatorios;
using Zendar.Business.ViewModels.V1.Relatorios;
using Zendar.Data.DomainServices.PrecoServices;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Extensions;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Models.DTO.Preco;
using Zendar.Data.Models.DTO.TabelaPreco;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services
{
    public class ProdutoRelatorioService : BaseService, IProdutoRelatorioService
    {
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IImpressaoPersonalizadaService _impressaoPersonalizadaService;
        private readonly ILojaRepository _lojaRepository;
        private readonly IProdutoRepository _produtoRepository;
        private readonly ITabelaPrecoService _tabelaPrecoService;
        private readonly IProdutoCorTamanhoService _produtoCorTamanhoService;
        private readonly IProdutoCorTamanhoRepository _produtoCorTamanhoRepository;
        private readonly ITamanhoRepository _tamanhoRepository;
        private readonly ICorRepository _corRepository;
        private readonly ICategoriaProdutoRepository _categoriaProdutoRepository;
        private readonly IMarcaRepository _marcaRepository;
        private readonly ICampoPersonalizadoRepository _campoPersonalizadoRepository;
        private readonly ITagRepository _tagRepository;
        private readonly IStorageService _storageService;
        private readonly ILogAuditoriaService _logAuditoriaService;
        private readonly IMapper _mapper;
        private readonly ITabelaPrecoRepository _tabelaPrecoRepository;
        private readonly IPadronizacaoService _padronizacaoService;
        private readonly IVendedorRepository _vendedorRepository;
        private readonly ILogErroService _logErroService;
        private readonly IClienteFornecedorRepository _clienteFornecedorRepository;
        private readonly ICategoriaProdutoService _categoriaProdutoService;
        private readonly ITabelaPrecoV2Service _tabelaPrecoV2Service;
        private readonly ILocalEstoqueRepository _localEstoqueRepository;

        public ProdutoRelatorioService(INotificador notificador,
                                       IAspNetUserInfo aspNetUserInfo,
                                       IImpressaoPersonalizadaService impressaoPersonalizadaService,
                                       ILojaRepository lojaRepository,
                                       IProdutoRepository produtoRepository,
                                       ITabelaPrecoService tabelaPrecoServcice,
                                       IProdutoCorTamanhoService produtoCorTamanhoService,
                                       IProdutoCorTamanhoRepository produtoCorTamanhoRepository,
                                       ITamanhoRepository tamanhoRepository,
                                       ICorRepository corRepository,
                                       ICategoriaProdutoRepository categoriaProdutoRepository,
                                       IMarcaRepository marcaRepository,
                                       ICampoPersonalizadoRepository campoPersonalizadoRepository,
                                       ITagRepository tagRepository,
                                       IStorageService storageService,
                                       ILogAuditoriaService logAuditoriaService,
                                       IMapper mapper,
                                       ITabelaPrecoRepository tabelaPrecoRepository,
                                       IPadronizacaoService padronizacaoService,
                                       IVendedorRepository vendedorRepository,
                                       ILogErroService logErroService, IClienteFornecedorRepository clienteFornecedorRepository, ICategoriaProdutoService categoriaProdutoService, ITabelaPrecoV2Service tabelaPrecoV2Service, ILocalEstoqueRepository localEstoqueRepository) : base(notificador)
        {
            _aspNetUserInfo = aspNetUserInfo;
            _impressaoPersonalizadaService = impressaoPersonalizadaService;
            _lojaRepository = lojaRepository;
            _produtoRepository = produtoRepository;
            _tabelaPrecoService = tabelaPrecoServcice;
            _produtoCorTamanhoService = produtoCorTamanhoService;
            _produtoCorTamanhoRepository = produtoCorTamanhoRepository;
            _tamanhoRepository = tamanhoRepository;
            _corRepository = corRepository;
            _categoriaProdutoRepository = categoriaProdutoRepository;
            _marcaRepository = marcaRepository;
            _campoPersonalizadoRepository = campoPersonalizadoRepository;
            _tagRepository = tagRepository;
            _storageService = storageService;
            _logAuditoriaService = logAuditoriaService;
            _mapper = mapper;
            _tabelaPrecoRepository = tabelaPrecoRepository;
            _padronizacaoService = padronizacaoService;
            _vendedorRepository = vendedorRepository;
            _logErroService = logErroService;
            _clienteFornecedorRepository = clienteFornecedorRepository;
            _categoriaProdutoService = categoriaProdutoService;
            _tabelaPrecoV2Service = tabelaPrecoV2Service;
            _localEstoqueRepository = localEstoqueRepository;
        }

        public async Task Cadastrar(ImpressaoPersonalizadaCadastrarAlterarProdutoViewModel impressaoViewModel)
        {
            var cadastrarVm = _mapper.Map<ImpressaoPersonalizadaCadastrarViewModel>(impressaoViewModel);

            await _impressaoPersonalizadaService.Cadastrar(cadastrarVm, TipoImpressao.PRODUTO);
        }

        public async Task Alterar(Guid idImpressao, ImpressaoPersonalizadaCadastrarAlterarProdutoViewModel impressaoViewModel)
        {
            var cadastrarVm = _mapper.Map<ImpressaoPersonalizadaViewModel>(impressaoViewModel);
            cadastrarVm.Id = idImpressao;

            await _impressaoPersonalizadaService.Alterar(cadastrarVm);
        }

        private List<Guid> TratarFiltroCategoria(List<Guid> categoriaId)
        {
            var listaIdCategorias = new List<Guid>();

            foreach (var filtro in categoriaId)
            {
                listaIdCategorias.AddRange(_categoriaProdutoService.ObterCategoriasVinculadas(filtro).Result);
            }

            return listaIdCategorias;
        }

        public async Task<byte[]> GerarRelatorio(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel)
        {
            produtoFiltrosViewModel.DataEmissaoInicio = produtoFiltrosViewModel.DataEmissaoInicio.HasValue ? produtoFiltrosViewModel.DataEmissaoInicio : DateTime.MinValue;
            produtoFiltrosViewModel.DataEmissaoFim = produtoFiltrosViewModel.DataEmissaoFim.HasValue ? produtoFiltrosViewModel.DataEmissaoFim : DateTime.MaxValue;

            if (produtoFiltrosViewModel.CategoriasProduto != null)
                produtoFiltrosViewModel.CategoriasProduto = TratarFiltroCategoria(produtoFiltrosViewModel.CategoriasProduto);

            // Recuperar nome da loja
            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(x => x.Id == _aspNetUserInfo.LojaId.Value, x => new Loja
            {
                Fantasia = x.Fantasia
            });

            // Checar o tipo de relatório que será gerado
            if (produtoFiltrosViewModel.TipoRelatorio.HasValue)
            {
                byte[] relatorio = null;
                switch (produtoFiltrosViewModel.TipoRelatorio)
                {
                    case TipoRelatorioPadrao.LUCRO_POR_PRODUTO:
                        var lucroPorProdutoVm = new RelatorioLucroPorProdutoViewModel
                        {
                            TipoSistema = _aspNetUserInfo.Sistema,
                            LojaFantasia = loja.Fantasia,
                            Emissao = DateTime.UtcNow,
                            Produtos = new()
                        };
                        relatorio = await GerarRelatorioLucroPorProduto(produtoFiltrosViewModel, lucroPorProdutoVm);
                        break;
                    case TipoRelatorioPadrao.PRODUTO_POR_VENDA:
                        var relatorioProdutoPorVendaVm = new RelatorioProdutoPorVendaViewModel
                        {
                            TipoSistema = _aspNetUserInfo.Sistema,
                            LojaFantasia = loja.Fantasia,
                            Emissao = DateTime.UtcNow,
                            Produtos = new()
                        };
                        relatorio = await GerarRelatorioProdutoPorVenda(produtoFiltrosViewModel, relatorioProdutoPorVendaVm);
                        break;
                    case TipoRelatorioPadrao.PRODUTO_COM_PRECO:
                        var relatorioVm = new RelatorioProdutoComPrecoViewModel
                        {
                            TipoSistema = _aspNetUserInfo.Sistema,
                            LojaFantasia = loja.Fantasia,
                            Emissao = DateTime.UtcNow,
                        };
                        relatorio = await GerarRelatorioProdutoComPreco(produtoFiltrosViewModel, relatorioVm);
                        break;
                    case TipoRelatorioPadrao.CATEGORIA_AGRUPADO:
                        var relatorioProdutoPorGrupoVm = new RelatorioProdutoPorGrupoViewModel
                        {
                            TipoSistema = _aspNetUserInfo.Sistema,
                            LojaFantasia = loja.Fantasia,
                            Emissao = DateTime.UtcNow,
                        };
                        relatorio = await GerarRelatorioProdutoPorGrupo(produtoFiltrosViewModel, relatorioProdutoPorGrupoVm);
                        break;
                    case TipoRelatorioPadrao.CLIENTE_AGRUPADO:
                        var relatorioVendaAgrupadoCliente = new RelatorioVendaAgrupadoPorClienteViewModel
                        {
                            TipoSistema = _aspNetUserInfo.Sistema,
                            LojaFantasia = loja.Fantasia,
                            Emissao = DateTime.UtcNow,
                            ClientesProdutos = new(),
                        };
                        relatorio = await GerarRelatorioClienteAgrupado(produtoFiltrosViewModel, relatorioVendaAgrupadoCliente);
                        break;
                    case TipoRelatorioPadrao.VENDA_POR_CONTA:
                        var relatorioProdutoPorNumeroConta = new RelatorioProdutoPorNumeroContaViewModel
                        {
                            TipoSistema = _aspNetUserInfo.Sistema,
                            LojaFantasia = loja.Fantasia,
                            Emissao = DateTime.UtcNow,
                            Contas = new()
                        };
                        relatorio = await GerarRelatorioProdutoPorNumeroConta(produtoFiltrosViewModel, relatorioProdutoPorNumeroConta);
                        break;
                    case TipoRelatorioPadrao.VENDA_PRODUTOS_AGRUPADOR_POR_DIA:
                        var vendaProdutosAgrupadosDia = new RelatorioProdutoAgrupadoPorDia
                        {
                            TipoSistema = _aspNetUserInfo.Sistema,
                            LojaFantasia = loja.Fantasia,
                            Emissao = DateTime.UtcNow,
                            CasasDecimais = await _padronizacaoService.ObterCasasDecimais()
                        };
                        relatorio = await GerarRelatorioProdutoAgrupadoPorDia(produtoFiltrosViewModel, vendaProdutosAgrupadosDia);
                        break;
                    case TipoRelatorioPadrao.ITENS_MAIS_VENDIDOS:
                        var relatorioItensVendidos = new RelatorioProdutoItensMaisVendidosViewModel
                        {
                            TipoSistema = _aspNetUserInfo.Sistema,
                            LojaFantasia = loja.Fantasia,
                            Emissao = DateTime.UtcNow,
                            Produtos = new(),
                        };
                        relatorio = await GerarRelatorioProdutoItensMaisVendidos(produtoFiltrosViewModel, relatorioItensVendidos);
                        break;
                }

                await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.IMPRESSAO_PERSONALIZADA, LogAuditoriaOperacao.GERAR,
                $"Relatorio: {produtoFiltrosViewModel.TipoRelatorio.ObterDescricao()}"));

                return relatorio;
            }
            else if (produtoFiltrosViewModel.IdRelatorioPersonalizado.HasValue)
            {
                // Montar view model relatorio personalizado
                var relatorioVm = new RelatorioPersonalizadoViewModel
                {
                    TipoSistema = _aspNetUserInfo.Sistema,
                    LojaFantasia = loja.Fantasia,
                    Emissao = DateTime.UtcNow.AddHours(_aspNetUserInfo.TimezoneOffset.Value),
                    Titulo = "Relatório de Produtos",
                    Dados = new()
                };

                return await GerarRelatorioPersonalizado(produtoFiltrosViewModel, relatorioVm);
            }

            NotificarAviso(ResourceMensagem.ProdutoRelatorioService_NaoSelecionada);
            return null;
        }

        private static List<ProdutoCorTamanho> OrdenarListaProduto(List<ProdutoCorTamanho> produtos, List<OrdenacaoViewModel<string>> propriedades)
        {
            IOrderedEnumerable<ProdutoCorTamanho> produtosOrdenados = null;

            foreach (var prop in propriedades)
            {
                var campoOrdenacao = (CampoRelatorioPersonalizadoProduto)Enum.Parse(typeof(CampoRelatorioPersonalizadoProduto), prop.CampoOrdenacao);

                Func<ProdutoCorTamanho, object> ordenacaoSelector = ObterExpressaoOrdenacao(campoOrdenacao);

                if (ordenacaoSelector == null)
                    continue;

                if (produtosOrdenados == null)
                    produtosOrdenados = produtos.OrderBy(ordenacaoSelector);
                else
                    produtosOrdenados = produtosOrdenados.ThenBy(ordenacaoSelector);
            }

            return produtosOrdenados?.ToList();
        }

        private static Func<ProdutoCorTamanho, object> ObterExpressaoOrdenacao(CampoRelatorioPersonalizadoProduto campoOrdenacao)
            => campoOrdenacao switch
            {
                CampoRelatorioPersonalizadoProduto.Descricao => x => x.FormatarDescricaoCompleta(),
                CampoRelatorioPersonalizadoProduto.DescricaoAbreviada => x => x.ProdutoCor.Produto.NomeAbreviado,
                CampoRelatorioPersonalizadoProduto.GtinEan => x => x.CodigoGTINEAN,
                CampoRelatorioPersonalizadoProduto.Referencia => x => x.ProdutoCor.Produto.Referencia,
                CampoRelatorioPersonalizadoProduto.Tipo => x => x.ProdutoCor.Produto.TipoProduto.ObterDescricao(),
                CampoRelatorioPersonalizadoProduto.Categoria => x => x.ProdutoCor.Produto.CategoriaProduto.Nome,
                CampoRelatorioPersonalizadoProduto.Marca => x => x.ProdutoCor.Produto.Marca.Nome,
                CampoRelatorioPersonalizadoProduto.EstoqueMinimo => x => x.EstoqueMinimo,
                CampoRelatorioPersonalizadoProduto.PrecoCompra => x => x.ProdutoCor.Produto.ProdutoPrecoLojas.FirstOrDefault()?.PrecoCompra,
                CampoRelatorioPersonalizadoProduto.PrecoCusto => x => x.ProdutoCor.Produto.ProdutoPrecoLojas.FirstOrDefault()?.PrecoCusto,
                CampoRelatorioPersonalizadoProduto.Markup => x => x.ProdutoCor.Produto.ProdutoPrecoLojas.FirstOrDefault()?.Markup,
                CampoRelatorioPersonalizadoProduto.PrecoVenda => x => x.ProdutoCor.Produto.ProdutoPrecoLojas.FirstOrDefault()?.PrecoVenda,
                CampoRelatorioPersonalizadoProduto.Ncm => x => x.ProdutoCor.Produto.CodigoNcm,
                CampoRelatorioPersonalizadoProduto.Cest => x => x.ProdutoCor.Produto.CodigoCest,
                CampoRelatorioPersonalizadoProduto.UnidadeMedida => x => x.ProdutoCor.Produto.UnidadeMedida.Descricao,
                CampoRelatorioPersonalizadoProduto.Tags => x => string.Join(", ", x.ProdutoCor.Produto.TagProdutos.Select(x => x.Tag.Nome)),
                CampoRelatorioPersonalizadoProduto.SaldoEstoque => x => x.ProdutoCorTamanhoEstoques.Sum(e => e.EstoqueAtual),
                CampoRelatorioPersonalizadoProduto.SKU => x => x.SKU,
                CampoRelatorioPersonalizadoProduto.RegraFiscal => x => x.ProdutoCor.Produto.RegraFiscal.Nome,
                CampoRelatorioPersonalizadoProduto.CodigoBarrasInterno => x => x.CodigoBarrasInterno,
                _ => x => x.FormatarDescricaoCompleta()
            };

        public async Task<byte[]> GerarRelatorioPersonalizado(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, RelatorioPersonalizadoViewModel relatorioVm)
        {
            // Realizar consulta das configuracoes
            var relatorioConfiguracoes = await _impressaoPersonalizadaService.Obter(produtoFiltrosViewModel.IdRelatorioPersonalizado.Value);
            if (relatorioConfiguracoes == null)
            {
                NotificarAviso(ResourceMensagem.ImpressaoPersonalizadaService_NaoEncontrada);
                return null;
            }

            var props = RelatorioHelpers.ConverterCamposExibicaoEmLista<CampoRelatorioPersonalizadoProduto>(relatorioConfiguracoes.Configuracoes);

            // Recuperar e filtrar os produtos
            var produtos = await _produtoCorTamanhoRepository.ObterParaRelatorioPersonalizadoProduto(produtoFiltrosViewModel, _aspNetUserInfo.LojaId.Value);
            if (produtos.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            var produtosOrdenados = new List<ProdutoCorTamanho>();

            if (props.CamposOrdenacao.Count > 0)
                produtosOrdenados = OrdenarListaProduto(produtos, props.CamposOrdenacao);
            else
                produtosOrdenados = produtos.OrderBy(c => c.FormatarDescricaoCompleta()).ToList();

            produtosOrdenados.ForEach(p =>
            {
                var linha = new Linha { Itens = new() };

                var produto = GerarViewModelRelatorioPersonalizadoProdutos(p);
                props.Campos.ForEach(prop =>
                {
                    // Recupero o valor relativo do enum a partir do seu nome
                    var en = (CampoRelatorioPersonalizadoProduto)Enum.Parse(typeof(CampoRelatorioPersonalizadoProduto), prop);

                    var item = new ItemRelatorio
                    {
                        Chave = en.ObterDescricao(),
                        Valor = produto.GetType().GetProperty(prop)?.GetValue(produto) as string ?? string.Empty,
                        QtdColunasItem = en.ObterTamanho(),
                        Tipo = en.ObterTipoCampo()
                    };

                    if (item.Tipo == TipoItemRelatorio.IMAGEM)
                    {
                        try
                        {
                            if (!string.IsNullOrEmpty(item.Valor))
                                item.Imagem = _storageService.Download(StorageContaArmazenamento.Imagens, item.Valor);
                            else
                                item.Valor = "---";
                        }
                        catch (Exception ex)
                        {
                            item.Valor = "---";
                            _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = item.Valor }).Wait();
                        }
                    }

                    linha.Itens.Add(item);
                });

                relatorioVm.Dados.Add(linha);
            });

            // Gerando a string de filtro
            relatorioVm.Filtro = await GerarTextoExibicaoFiltros(produtoFiltrosViewModel);

            relatorioVm.Titulo = relatorioConfiguracoes.Nome;

            var relatorio = new ImpressaoRelatorioPersonalizadoProduto(relatorioVm).ToArray();

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.IMPRESSAO_PERSONALIZADA, LogAuditoriaOperacao.GERAR,
                    $"Relatorio: {relatorioVm.Titulo}"));

            return relatorio;
        }

        public async Task<byte[]> GerarRelatorioLucroPorProduto(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, RelatorioLucroPorProdutoViewModel relatorioVm)
        {
            // Consultar e Filtrar os dados
            var operacaoItens = await _produtoCorTamanhoRepository.ObterParaRelatorioLucroPorProduto(produtoFiltrosViewModel, _aspNetUserInfo.LojaId.Value);
            if (operacaoItens.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVm);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, operacaoItens);

            var casasDecimais = await _padronizacaoService.ObterCasasDecimais();

            // Totalizadores
            decimal totalQtd = 0;
            decimal totalCusto = 0;
            decimal totalValor = 0;
            decimal totalLucroReal = 0;

            operacaoItens.ForEach(p =>
            {
                var desconto = p.DescontoDistribuido + p.ValorDescontoItem;
                var acrescimo = p.AcrescimoDistribuido + p.FreteDistribuido + p.OutrasDespesasDistribuido;

                if (p.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO)
                {
                    p.ValorUnitario *= -1;
                    p.PrecoCusto *= -1;
                    desconto *= -1;
                    acrescimo *= -1;
                }

                decimal custo = p.PrecoCusto * p.Quantidade;
                decimal valor = (p.ValorUnitario * p.Quantidade) + acrescimo - desconto;
                decimal lucro = (valor - custo);
                var lucroPorcentagem = lucro != 0 && valor != 0 ? lucro / valor * 100 : 0;

                if (p.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO)
                {
                    p.Quantidade *= -1;
                    lucroPorcentagem *= -1;
                }

                var produto = new LucroPorProdutoViewModel
                {
                    NumeroOperacao = p.Operacao.NumeroOperacao.ToString().PadLeft(8, '0'),
                    Produto = _produtoCorTamanhoService.FormatarDescricaoCompletaProduto(p.ProdutoCorTamanhoId).Result,
                    Quantidade = FormatarValor.FormatarQuantidade(p.Quantidade, casasDecimais.CasasDecimaisQuantidade),
                    Custo = FormatarValor.FormatarValorComPontuacao(custo),
                    Valor = FormatarValor.FormatarValorComPontuacao(valor, casasDecimais.CasasDecimaisValor),
                    LucroReal = FormatarValor.FormatarValorComPontuacao(lucro),
                    LucroPorcentagem = FormatarValor.FormatarValorComPontuacao(lucroPorcentagem)
                };


                totalQtd += p.Quantidade;
                totalCusto += custo;
                totalValor += valor;
                totalLucroReal += lucro;

                relatorioVm.Produtos.Add(produto);
            });

            // Preenchendo os totalizadores
            relatorioVm.TotalQuantidade = FormatarValor.FormatarQuantidade(totalQtd, casasDecimais.CasasDecimaisQuantidade);
            relatorioVm.TotalCusto = FormatarValor.FormatarValorComPontuacao(totalCusto);
            relatorioVm.TotalValor = FormatarValor.FormatarValorComPontuacao(totalValor);
            relatorioVm.TotalLucroReal = FormatarValor.FormatarValorComPontuacao(totalLucroReal);
            decimal totalLucroPorcentagem = 0;
            if (totalValor != 0)
            {
                totalLucroPorcentagem = totalLucroReal / totalValor * 100;
                if (totalLucroReal < 0 && totalValor < 0)
                {
                    totalLucroPorcentagem *= -1;
                }
            }

            relatorioVm.TotalLucroPorcentagem = FormatarValor.FormatarValorComPontuacao(totalLucroPorcentagem);

            // Ordenando pelo número da operação
            relatorioVm.Produtos = relatorioVm.Produtos.OrderBy(p => p.NumeroOperacao).ToList();

            // Gerando a string de filtro -> Método deve ser chamado por último, para não ocasionar problemas com datas
            relatorioVm.Filtro = await GerarTextoExibicaoFiltros(produtoFiltrosViewModel);

            // Retornar byte[]
            return new ImpressaoRelatorioLucroPorProduto(relatorioVm).ToArray();
        }
        public async Task<byte[]> GerarRelatorioClienteAgrupado(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, RelatorioVendaAgrupadoPorClienteViewModel relatorioClienteAgrupadoVm)
        {
            List<OperacaoItem> operacaoItens = await _produtoCorTamanhoRepository.ObterRelatorioAgrupadoPorCliente(produtoFiltrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacaoItens.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            //Totalizadores rodapé
            decimal qtdTotal = 0, totalItens = 0, valorTotalDesconto = 0, valorTotalAcrescimos = 0;
            var casasDecimais = await _padronizacaoService.ObterCasasDecimais();

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioClienteAgrupadoVm);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, operacaoItens);

            operacaoItens.ForEach(p =>
            {
                if (p.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO)
                {
                    p.ValorUnitario *= -1;
                    p.ValorItemSemDesconto *= -1;
                    p.Quantidade *= -1;
                    p.DescontoDistribuido *= -1;
                    p.ValorDescontoItem *= -1;
                    p.AcrescimoDistribuido *= -1;
                    p.OutrasDespesasDistribuido *= -1;
                    p.FreteDistribuido *= -1;
                }

                var clienteVenda = new ClientesVendas(p.Operacao.ClienteFornecedor.Codigo.ToString(),
                                                      p.Operacao.ClienteFornecedor.Nome,
                                                      p.Operacao.ClienteFornecedor.Celular,
                                                      p.Operacao.ClienteFornecedor.Telefone);

                var produtoVenda = new ProdutoClienteAgrupadoViewModel(p.Operacao.DataEmissao,
                                                                     p.Operacao.NumeroOperacao,
                                                                     p.ProdutoCorTamanho.FormatarDescricaoCompleta(),
                                                                     p.ProdutoCorTamanho.ProdutoCor.Produto.Referencia,
                                                                     p.Quantidade,
                                                                     p.ValorUnitario,
                                                                     p.ValorItemSemDesconto,
                                                                     casasDecimais);
                qtdTotal += p.Quantidade;
                totalItens += p.ValorItemSemDesconto;
                valorTotalDesconto += (p.DescontoDistribuido + p.ValorDescontoItem);
                valorTotalAcrescimos += (p.AcrescimoDistribuido + p.FreteDistribuido + p.OutrasDespesasDistribuido);

                relatorioClienteAgrupadoVm.AdicionaProdutoVendidoAoCliente(clienteVenda, produtoVenda);
            });

            relatorioClienteAgrupadoVm.OrdenarVendasSequencialmentePorNumeroOperacao();
            relatorioClienteAgrupadoVm.ProdutosTotalizados.PreencherTotalizadores(qtdTotal, totalItens, valorTotalDesconto, valorTotalAcrescimos);

            // Gerando a string de filtro -> Método deve ser chamado por último, para não ocasionar problemas com datas
            relatorioClienteAgrupadoVm.Filtro = await GerarTextoExibicaoFiltros(produtoFiltrosViewModel);

            return new ImpressaoRelatorioProdutoFiltrosViewModel(relatorioClienteAgrupadoVm).ToArray();
        }

        private async Task<byte[]> GerarRelatorioProdutoAgrupadoPorDia(RelatorioProdutoFiltrosViewModel filtrosViewModel, RelatorioProdutoAgrupadoPorDia produtosAgrupadosDia)
        {
            var operacaoItens = await _produtoCorTamanhoRepository.ObterRelatoriosProdutoAgrupadoPorDia(filtrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacaoItens.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, produtosAgrupadosDia);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, operacaoItens);

            IEnumerable<IGrouping<DateTime, OperacaoItem>> operacoesAgrupadasPorDia = operacaoItens.GroupBy(o => o.Operacao.DataEmissao.Date);

            foreach (var operacoesPorDia in operacoesAgrupadasPorDia)
            {
                var periodoVenda = new PeriodoVenda();
                periodoVenda.Data = operacoesPorDia.Key;
                IEnumerable<IGrouping<string, OperacaoItem>> operacoesItensAgrupado = operacoesPorDia.GroupBy(i => i.ProdutoCorTamanho.ProdutoCor.Produto.CategoriaProduto.Nome);

                foreach (var item in operacoesItensAgrupado)
                {
                    var produto = new ProdutoCategoria
                    {
                        CategoriaNome = item.Key,
                        Produtos = item.Select(i => new Produtos(
                            i.ProdutoCorTamanho.ProdutoCor.Produto.SkuIdentificador.ToString(),
                            i.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                            i.Operacao.TipoOperacao.IdentificacaoTipoOperacao,
                            i.Quantidade,
                            i.ValorUnitario,
                            i.AcrescimoDistribuido,
                            i.FreteDistribuido,
                            i.OutrasDespesasDistribuido,
                            i.ValorDescontoItem,
                            i.DescontoDistribuido,
                            produtosAgrupadosDia.CasasDecimais
                        )).ToList()
                    };
                    periodoVenda.ProdutoCategoria.Add(produto);
                }
                produtosAgrupadosDia.Periodo.Add(periodoVenda);
            }

            var produtos = produtosAgrupadosDia.Periodo.SelectMany(p => p.ProdutoCategoria)
                                                             .SelectMany(p => p.Produtos)
                                                             .ToList();

            var totalOperacoes = operacaoItens.Select(oi => oi.Operacao.Id).Distinct().Count();

            var totalizador = new TotalizadorProdutoPeriodoVenda(produtos.Sum(p => p.ValorTotal),
                                                                 produtos.Sum(p => p.AcrescimoDistribuido),
                                                                 produtos.Sum(p => p.FreteDistribuido),
                                                                 produtos.Sum(p => p.OutrasDespesasDistribuido),
                                                                 produtos.Sum(p => p.ValorDescontoItem),
                                                                 produtos.Sum(p => p.DescontoDistribuido),
                                                                 totalOperacoes);

            produtosAgrupadosDia.TotalizadorProdutoPeriodoVenda = totalizador;

            produtosAgrupadosDia.Filtro = await GerarTextoExibicaoFiltros(filtrosViewModel);

            return new ImpressaoRelatorioProdutoAgrupadoPorDia(produtosAgrupadosDia).ToArray();
        }

        public async Task<byte[]> GerarRelatorioProdutoPorVenda(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, RelatorioProdutoPorVendaViewModel relatorioVm)
        {
            // Consultar e  Filtrar os dados
            var operacaoItens = await _produtoCorTamanhoRepository.ObterParaRelatorioProdutoPorVenda(produtoFiltrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacaoItens.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            // Totalizadores
            decimal qtdTotal = 0;
            decimal totalItens = 0;
            decimal valorTotalDesconto = 0, valorTotalAcrescimos = 0;

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVm);
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, operacaoItens);

            var casasDecimais = await _padronizacaoService.ObterCasasDecimais();

            // Preencher view model
            operacaoItens.ForEach(p =>
            {
                if (p.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO)
                {
                    p.ValorUnitario *= -1;
                    p.ValorItemSemDesconto *= -1;
                    p.Quantidade *= -1;
                    p.DescontoDistribuido *= -1;
                    p.ValorDescontoItem *= -1;
                    p.AcrescimoDistribuido *= -1;
                    p.OutrasDespesasDistribuido *= -1;
                    p.FreteDistribuido *= -1;
                }

                var produto = new ProdutoPorVendaViewModel
                {
                    DataEmissao = p.Operacao.DataEmissao.ToString("dd/MM/yyyy HH:mm"),
                    NumeroOperacao = p.Operacao.NumeroOperacao.ToString().PadLeft(8, '0'),
                    Nome = _produtoCorTamanhoService.FormatarDescricaoCompletaProduto(p.ProdutoCorTamanhoId).Result,
                    Vendedor = p.Operacao.Vendedor.Nome,
                    Valor = FormatarValor.FormatarValorComPontuacao(p.ValorUnitario, casasDecimais.CasasDecimaisValor),
                    Quantidade = FormatarValor.FormatarQuantidade(p.Quantidade, casasDecimais.CasasDecimaisQuantidade),
                    Total = FormatarValor.FormatarValorComPontuacao(p.ValorItemSemDesconto),
                    Referencia = p.ProdutoCorTamanho.ProdutoCor.Produto.Referencia
                };

                qtdTotal += p.Quantidade;
                totalItens += p.ValorItemSemDesconto;
                valorTotalDesconto += (p.DescontoDistribuido + p.ValorDescontoItem);
                valorTotalAcrescimos += (p.AcrescimoDistribuido + p.FreteDistribuido + p.OutrasDespesasDistribuido);

                relatorioVm.Produtos.Add(produto);
            });


            // Preenchendo os totalizadores
            relatorioVm.TotalQtdProdutos = FormatarValor.FormatarValorComPontuacao(qtdTotal);
            relatorioVm.TotalValorItens = FormatarValor.FormatarValorComPontuacao(totalItens);
            relatorioVm.TotalDescontos = FormatarValor.FormatarValorComPontuacao(valorTotalDesconto);
            relatorioVm.TotalAcrescimos = FormatarValor.FormatarValorComPontuacao(valorTotalAcrescimos);
            relatorioVm.TotalValorTotal = FormatarValor.FormatarValorComPontuacao(totalItens + valorTotalAcrescimos - valorTotalDesconto);

            // Ordenando pelo número da operação
            relatorioVm.Produtos = relatorioVm.Produtos.OrderBy(p => p.NumeroOperacao).ToList();

            // Gerando a string de filtro -> Método deve ser chamado por último, para não ocasionar problemas com datas
            relatorioVm.Filtro = await GerarTextoExibicaoFiltros(produtoFiltrosViewModel);

            // Retornar byte[]
            return new ImpressaoRelatorioProdutoPorVenda(relatorioVm).ToArray();
        }
        public async Task<byte[]> GerarRelatorioProdutoPorGrupo(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, RelatorioProdutoPorGrupoViewModel relatorioVm)
        {
            // Consultar e  Filtrar os dados
            var operacaoItens = await _produtoCorTamanhoRepository.ObterParaRelatorioProdutoPorGrupo(produtoFiltrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacaoItens.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            // Totalizadores
            relatorioVm.TotalAcrescimos = operacaoItens.TotalAcrescimos();
            relatorioVm.TotalDescontos = operacaoItens.TotalDescontos();
            relatorioVm.TotalVendas = operacaoItens.GroupBy(s => s.OperacaoId).Count();

            var produtosAgrupadosPorGrupos = operacaoItens.Where(w => w.ProdutoCorTamanho.ProdutoCor.Produto.CategoriaProduto != null)
                                                           .GroupBy(x => x.ProdutoCorTamanho.ProdutoCor.Produto.CategoriaProduto.Nome);

            foreach (var grupo in produtosAgrupadosPorGrupos)
            {

                var grupoProduto = new GrupoProduto
                {
                    Grupo = grupo.Key,
                    Produtos = grupo.Select(s => new ProdutoPorGrupoViewModel(s.ProdutoCorTamanho.ProdutoCor.Produto.SkuIdentificador,
                                                                             s.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                                                                             s.Quantidade,
                                                                             s.ValorUnitario))
                                                                            .ToList()
                };

                relatorioVm.Grupos.Add(grupoProduto);
            }

            // Gerando a string de filtro -> Método deve ser chamado por último, para não ocasionar problemas com datas
            relatorioVm.Filtro = await GerarTextoExibicaoFiltros(produtoFiltrosViewModel);


            // Retornar byte[]
            return new ImpressaoRelatorioProdutoPorGrupo(relatorioVm).ToArray();
        }
        public async Task<byte[]> GerarRelatorioProdutoPorNumeroConta(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, RelatorioProdutoPorNumeroContaViewModel relatorioVm)
        {
            // Consultar e  Filtrar os dados
            var operacaoItens = await _produtoCorTamanhoRepository.ObterParaRelatorioProdutoPorNumeroConta(produtoFiltrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacaoItens.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            relatorioVm.TotalAcrescimos = operacaoItens.TotalAcrescimos();
            relatorioVm.TotalDescontos = operacaoItens.TotalDescontos();

            var produtosAgrupadosPorContas = operacaoItens.GroupBy(x => x.NumeroConta);

            foreach (var conta in produtosAgrupadosPorContas)
            {
                var contaProduto = new ContaProduto
                {
                    NumeroConta = conta.Key ?? 0,
                    Produtos = conta.Select(s => new ProdutoPorNumeroContaViewModel(
                                                                             s.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                                                                             s.Quantidade,
                                                                             s.ValorItemSemDesconto))
                                                                            .ToList()
                };

                relatorioVm.Contas.Add(contaProduto);
            }

            // Gerando a string de filtro -> Método deve ser chamado por último, para não ocasionar problemas com datas
            relatorioVm.Filtro = await GerarTextoExibicaoFiltros(produtoFiltrosViewModel);
            // Totalizadores
            relatorioVm.TotalProdutos = relatorioVm.Contas.SelectMany(c => c.Produtos).Sum(p => p.Total);
            relatorioVm.TotalOperacoes = operacaoItens.GroupBy(x => x.OperacaoId).Count();
            relatorioVm.TotalGeral = relatorioVm.TotalProdutos + relatorioVm.TotalAcrescimos - relatorioVm.TotalDescontos;

            // Retornar byte[]
            return new ImpressaoRelatorioProdutoPorNumeroConta(relatorioVm).ToArray();
        }
        public async Task<byte[]> GerarRelatorioProdutoItensMaisVendidos(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, RelatorioProdutoItensMaisVendidosViewModel relatorioVm)
        {
            // Consultar e  Filtrar os dados
            var operacaoItens = await _produtoCorTamanhoRepository.ObterParaRelatorioItensMaisVendidos(produtoFiltrosViewModel, _aspNetUserInfo.LojaId.Value);

            if (operacaoItens.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            // Totalizadores
            relatorioVm.TotalAcrescimos = operacaoItens.TotalAcrescimos();
            relatorioVm.TotalDescontos = operacaoItens.TotalDescontos();

            var produtosAgrupados = relatorioVm.AgruparItensVendidosPorTipo(operacaoItens, produtoFiltrosViewModel.TipoAgrupamento);

            relatorioVm.Produtos.AddRange(produtosAgrupados);

            // Gerando a string de filtro -> Método deve ser chamado por último, para não ocasionar problemas com datas
            relatorioVm.Filtro = await GerarTextoExibicaoFiltros(produtoFiltrosViewModel);
            relatorioVm.ValorTotal = relatorioVm.Produtos.Sum(s => s.Total);
            relatorioVm.TotalGeral = relatorioVm.ValorTotal + relatorioVm.TotalAcrescimos - relatorioVm.TotalDescontos;

            // Retornar byte[]
            return new ImpressaoRelatorioProdutoItensMaisVendidos(relatorioVm).ToArray();
        }
        public async Task<byte[]> GerarRelatorioProdutoComPreco(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel, RelatorioProdutoComPrecoViewModel relatorioVm)
        {
            if (!produtoFiltrosViewModel.TabelaPrecoId.HasValue) return null;

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, relatorioVm);

            TabelaPrecoDTO tabelaPreco = await _tabelaPrecoV2Service.Obter(produtoFiltrosViewModel.TabelaPrecoId.Value);

            if (tabelaPreco is null)
            {
                NotificarAvisoRegistroNaoEncontrada("tabela de preço");
                return null;
            }

            relatorioVm.AdicionarTabelaPreco(tabelaPreco.Nome);

            var produtoCoresTamanhos = await _produtoCorTamanhoRepository.ObterParaRelatorioProdutoComPreco(produtoFiltrosViewModel,
                                                                                                            _aspNetUserInfo.LojaId.Value);
            if (produtoCoresTamanhos?.Any() != true)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            var casasDecimais = await _padronizacaoService.ObterCasasDecimais();

            var produtoCoresTamanhosOrdenado = produtoCoresTamanhos.OrderBy(p => p.ProdutoCor.Produto.Nome.Trim())
                                                                    .ThenBy(p => p.ProdutoCor.Cor.Descricao)
                                                                    .ThenBy(p => p.Tamanho.SequenciaOrdenacao);

            foreach (var produtoCorTamanho in produtoCoresTamanhosOrdenado)
            {
                var produtoVariacaoPreco =
                    ProdutoVariacaoPrecoDTO.CreateFromProdutoCorTamanho(produtoCorTamanho,
                                                                        _aspNetUserInfo.LojaId.Value);

                var produtoComPreco = new ProdutoComPrecoViewModel
                {
                    Nome = produtoCorTamanho.FormatarDescricaoCompleta(),
                    PrecoVenda = produtoCorTamanho.ProdutoCor
                                                  .Produto
                                                  .ProdutoPrecoLojas
                                                  .FirstOrDefault(l => l.LojaId == _aspNetUserInfo.LojaId.Value)
                                                  ?.PrecoVenda ?? 0m,
                    PrecoVendaTabelaPreco = PrecoProdutoCalculador.Calcular(tabelaPreco,
                                                                            produtoVariacaoPreco,
                                                                            casasDecimais)
                };

                relatorioVm.AdcionarProduto(produtoComPreco);
            }

            relatorioVm.Filtro = await GerarTextoExibicaoFiltros(produtoFiltrosViewModel);

            return new ImpressaoRelatorioProdutoComPreco(relatorioVm).ToArray();
        }

        private RelatorioPersonalizadoProdutoViewModel GerarViewModelRelatorioPersonalizadoProdutos(ProdutoCorTamanho produto)
        {
            var produtoVm = new RelatorioPersonalizadoProdutoViewModel
            {
                Foto = produto.ProdutoCor.Produto.Foto,
                Descricao = produto.FormatarDescricaoCompleta(),
                DescricaoAbreviada = produto.ProdutoCor.Produto.NomeAbreviado,
                GtinEan = produto.CodigoGTINEAN,
                Referencia = produto.ProdutoCor.Produto.Referencia,
                Tipo = produto.ProdutoCor.Produto.TipoProduto.ObterDescricao(),
                Categoria = produto.ProdutoCor.Produto.CategoriaProduto.Nome,
                Marca = produto.ProdutoCor.Produto.Marca.Nome,
                EstoqueMinimo = produto.EstoqueMinimo.ToString(),
                PrecoCompra = $"{FormatarValor.FormatarValorComPontuacao(produto.ProdutoCor.Produto.ProdutoPrecoLojas.First().PrecoCompra)}",
                PrecoCusto = $"{FormatarValor.FormatarValorComPontuacao(produto.ProdutoCor.Produto.ProdutoPrecoLojas.First().PrecoCusto)}",
                Markup = $"{produto.ProdutoCor.Produto.ProdutoPrecoLojas.First().Markup}%",
                PrecoVenda = $"{FormatarValor.FormatarValorComPontuacao(produto.ProdutoCor.Produto.ProdutoPrecoLojas.First().PrecoVenda)}",
                Ncm = produto.ProdutoCor.Produto.CodigoNcm == SystemConst.DEFAULT_NCM
                    ? string.Empty
                    : produto.ProdutoCor.Produto.CodigoNcm,
                Cest = produto.ProdutoCor.Produto.CodigoCest,
                UnidadeMedida = produto.ProdutoCor.Produto.UnidadeMedida.Descricao,
                Tags = string.Join(", ", produto.ProdutoCor.Produto.TagProdutos.Select(x => x.Tag.Nome)),
                SKU = produto.SKU,
                SaldoEstoque = FormatarValor.FormatarValorComPontuacao(produto.ProdutoCorTamanhoEstoques.Sum(e => e.EstoqueAtual)),
                RegraFiscal = produto.ProdutoCor.Produto.RegraFiscal.Nome,
                CodigoBarrasInterno = produto.CodigoBarrasInterno
            };

            return produtoVm;
        }

        private async Task<string> GerarTextoExibicaoFiltros(RelatorioProdutoFiltrosViewModel produtoFiltrosViewModel)
        {
            var filtro = new StringBuilder();

            if (produtoFiltrosViewModel.DataEmissaoInicio.HasValue &&
                produtoFiltrosViewModel.DataEmissaoInicio.Value != DateTime.MinValue &&
                produtoFiltrosViewModel.DataEmissaoFim.HasValue &&
                produtoFiltrosViewModel.DataEmissaoFim.Value != DateTime.MaxValue)
            {
                ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, produtoFiltrosViewModel);

                var relatoriosFiltradosPorHora = new TipoRelatorioPadrao?[]
                {
                    TipoRelatorioPadrao.PRODUTO_POR_VENDA,
                };

                var formatoData = relatoriosFiltradosPorHora.Contains(produtoFiltrosViewModel.TipoRelatorio)
                                ? "dd/MM/yyyy HH:mm\\h"
                                : "dd/MM/yyyy";

                filtro.AppendFormat("Período: {0} até {1}",
                    produtoFiltrosViewModel.DataEmissaoInicio.Value.ToString(formatoData),
                    produtoFiltrosViewModel.DataEmissaoFim.Value.ToString(formatoData));
            }

            if (produtoFiltrosViewModel.TipoEstoque != TipoFiltroProdutoEstoque.TODOS)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Estoque: {produtoFiltrosViewModel.TipoEstoque.ObterDescricao()}");
            }

            if (produtoFiltrosViewModel.StatusConsulta != StatusConsulta.Todos)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Status: {produtoFiltrosViewModel.StatusConsulta.ObterDescricao()}");
            }

            if (produtoFiltrosViewModel.TabelaPrecoId.HasValue)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Tabela de preço: {await _tabelaPrecoRepository.ObterDescricao(produtoFiltrosViewModel.TabelaPrecoId.Value)}");
            }

            if (produtoFiltrosViewModel.VendedorId.HasValue)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Vendedor: {await _vendedorRepository.ObterNome(produtoFiltrosViewModel.VendedorId.Value)}");
            }

            if (produtoFiltrosViewModel.ClienteFornecedorId.HasValue)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Cliente: {await _clienteFornecedorRepository.ObterNome(produtoFiltrosViewModel.ClienteFornecedorId.Value)}");
            }

            if (produtoFiltrosViewModel.LocalEstoqueIds.Any())
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Local Estoque: ");

                var locaisEstoque = await _localEstoqueRepository.ObterNomes(produtoFiltrosViewModel.LocalEstoqueIds);
                filtro.Append(string.Join(", ", locaisEstoque));
            }

            if (produtoFiltrosViewModel.Tamanhos != null && produtoFiltrosViewModel.Tamanhos.Count > 0)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var tamanhos = await _tamanhoRepository.ObterDescricao(produtoFiltrosViewModel.Tamanhos);
                if (tamanhos.Any())
                {
                    filtro.Append($"Tamanho: {string.Join(", ", tamanhos)}");
                }
            }

            if (produtoFiltrosViewModel.Cores != null && produtoFiltrosViewModel.Cores.Count > 0)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var cores = await _corRepository.ObterDescricao(produtoFiltrosViewModel.Cores);
                if (cores.Any())
                {
                    filtro.Append($"Cor: {string.Join(", ", cores)}");
                }
            }

            if (produtoFiltrosViewModel.CategoriasProduto != null && produtoFiltrosViewModel.CategoriasProduto.Count > 0)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var categorias = await _categoriaProdutoRepository.ObterDescricao(produtoFiltrosViewModel.CategoriasProduto);
                if (categorias.Any())
                {
                    filtro.Append($"Categoria: {string.Join(", ", categorias.Distinct())}");
                }
            }

            if (produtoFiltrosViewModel.Marcas != null && produtoFiltrosViewModel.Marcas.Count > 0)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var marcas = await _marcaRepository.ObterDescricao(produtoFiltrosViewModel.Marcas);
                if (marcas.Any())
                {
                    filtro.Append($"Marca: {string.Join(", ", marcas)}");
                }
            }

            if (produtoFiltrosViewModel.TipoFiscal.Any() && !produtoFiltrosViewModel.TipoFiscal.Contains(TipoFiscal.TODOS))
            {
                if (filtro.Length > 0) filtro.Append(" | ");

                var tipoFiscal = produtoFiltrosViewModel.TipoFiscal
                    .Select(tipo => tipo.ObterDescricao());

                filtro.Append($"Tipo Fiscal: {string.Join(", ", tipoFiscal)}");
            }

            if (produtoFiltrosViewModel.Origem.HasValue && produtoFiltrosViewModel.Origem != IdentificacaoIntegracao.TODAS)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Origem: {produtoFiltrosViewModel.Origem.Value.ObterDescricao()}");
            }

            if (produtoFiltrosViewModel.StatusVenda.HasValue && produtoFiltrosViewModel.StatusVenda != StatusVenda.TODAS)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var statusOperacao = produtoFiltrosViewModel.StatusVenda.Value;
                filtro.Append($"Status das Vendas: {statusOperacao}");
            }

            if (produtoFiltrosViewModel.Tags != null && produtoFiltrosViewModel.Tags.Count > 0)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var tags = await _tagRepository.ObterDescricao(produtoFiltrosViewModel.Tags);
                if (tags.Any())
                {
                    filtro.Append($"Tag: {string.Join(", ", tags)}");
                }
            }

            if (produtoFiltrosViewModel.CamposPersonalizados != null && produtoFiltrosViewModel.CamposPersonalizados.Length > 0)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var campoPersonalizado = await _campoPersonalizadoRepository.ObterDescricao(produtoFiltrosViewModel.CamposPersonalizados.Select(c => c.CampoPersonalizadoId).ToList());
                if (campoPersonalizado.Any())
                {
                    filtro.Append($"Campo personalizado: {string.Join(", ", campoPersonalizado)}");
                }
            }

            return filtro.ToString();
        }

        public void Dispose()
        {
            _lojaRepository?.Dispose();
            _produtoRepository?.Dispose();
            _produtoCorTamanhoRepository?.Dispose();
            _tamanhoRepository?.Dispose();
            _corRepository?.Dispose();
            _categoriaProdutoRepository?.Dispose();
            _marcaRepository?.Dispose();
            _campoPersonalizadoRepository?.Dispose();
            _tagRepository?.Dispose();
            _vendedorRepository?.Dispose();
            _logErroService?.Dispose();
            _tabelaPrecoV2Service?.Dispose();
        }
    }
}
