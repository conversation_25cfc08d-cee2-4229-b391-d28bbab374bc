import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  ModalCloseButton,
  Button,
  Text,
  VStack,
  HStack,
  Box,
  Badge,
} from '@chakra-ui/react';
import React, { useState } from 'react';
import { FiDownload, FiCheck } from 'react-icons/fi';
import { toast } from 'react-toastify';
import { AutoSizer, List, ListRowProps } from 'react-virtualized';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';

import { ProdutoComErro, ProdutosProps } from '../../types';

interface ModalErrosImportacaoProps {
  isOpen: boolean;
  onClose: () => void;
  produtosComErro: ProdutoComErro[];
  totalProcessados: number;
  exportarErros: () => void;
  produtosComSucesso?: ProdutosProps;
  importarProdutosValidos?: (produtos: ProdutosProps) => Promise<void>;
}

export const ModalErrosImportacao: React.FC<ModalErrosImportacaoProps> = ({
  isOpen,
  onClose,
  produtosComErro,
  totalProcessados,
  exportarErros,
  produtosComSucesso,
  importarProdutosValidos,
}) => {
  const [produtosValidosJaImportados, setProdutosValidosJaImportados] =
    useState(false);
  const [estaImportando, setEstaImportando] = useState(false);

  const { casasDecimais } = usePadronizacaoContext();

  const normalizarValorMonetario = (valor: string | number): number | null => {
    if (!valor || valor === '') return null;

    const valorStr = valor.toString().trim();
    if (valorStr === '') return null;
    if (valorStr.match(/[a-zA-Z]/)) return null;
    let valorLimpo = valorStr.replace(/[^\d,.]/g, '');

    if (valorLimpo.includes('.') && valorLimpo.includes(',')) {
      valorLimpo = valorLimpo.replace(/\./g, '').replace(',', '.');
    } else if (valorLimpo.includes(',') && !valorLimpo.includes('.')) {
      valorLimpo = valorLimpo.replace(',', '.');
    }

    const numeroConvertido = parseFloat(valorLimpo);
    return isNaN(numeroConvertido) ? null : numeroConvertido;
  };

  const quantidadeProdutosComSucesso =
    totalProcessados - produtosComErro?.length;

  const ROW_HEIGHT = 40;
  const MAX_HEIGHT = 400;
  const shouldVirtualize = produtosComErro && produtosComErro.length > 10;

  const renderTableRow = ({ index, key, style }: ListRowProps) => {
    const produto = produtosComErro[index];

    return (
      <div
        key={key}
        style={{
          ...style,
          display: 'flex',
          alignItems: 'center',
          borderBottom: '1px solid #E2E8F0',
          padding: '4px 0',
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = '#EDF2F7';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor =
            index % 2 === 0 ? '#F7FAFC' : 'white';
        }}
      >
        <Box width="80px" px={3} flexShrink={0}>
          <Badge colorScheme="gray" variant="solid">
            {produto.linha}
          </Badge>
        </Box>

        <Box width="350px" px={3} flexShrink={0}>
          <Text
            fontSize="sm"
            noOfLines={1}
            overflow="hidden"
            textOverflow="ellipsis"
            whiteSpace="nowrap"
            title={produto.nome || '-'}
          >
            {produto.nome || '-'}
          </Text>
        </Box>

        <Box width="120px" px={3} flexShrink={0}>
          <Text fontSize="sm">
            {(() => {
              const valor = normalizarValorMonetario(produto.precoVendaAtual);
              return valor !== null
                ? `${valor.toLocaleString('pt-BR', {
                    minimumFractionDigits: casasDecimais.casasDecimaisValor,
                    maximumFractionDigits: casasDecimais.casasDecimaisValor,
                  })}`
                : '-';
            })()}
          </Text>
        </Box>

        <Box width="120px" px={3} flexShrink={0}>
          <Text fontSize="sm">
            {(() => {
              const valor = normalizarValorMonetario(produto.precoVendaNovo);
              return valor !== null
                ? `${valor.toLocaleString('pt-BR', {
                    minimumFractionDigits: casasDecimais.casasDecimaisValor,
                    maximumFractionDigits: casasDecimais.casasDecimaisValor,
                  })}`
                : '-';
            })()}
          </Text>
        </Box>

        <Box flex={1} px={3}>
          <Text fontSize="sm" color="red.500" fontWeight="semibold">
            {produto.descricaoErro}
          </Text>
        </Box>
      </div>
    );
  };

  const handleImportacaoProdutosValidos = async () => {
    if (
      produtosComSucesso &&
      importarProdutosValidos &&
      !produtosValidosJaImportados
    ) {
      setEstaImportando(true);
      try {
        await importarProdutosValidos(produtosComSucesso);
        setProdutosValidosJaImportados(true);
        toast.success(
          `${quantidadeProdutosComSucesso} produto(s) importado(s) com sucesso!`
        );
      } finally {
        setEstaImportando(false);
      }
    }
  };

  const manipularExportacaoErros = () => {
    exportarErros();
  };

  const handleFecharModal = () => {
    setProdutosValidosJaImportados(false);
    setEstaImportando(false);
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleFecharModal} size="6xl" isCentered>
      <ModalOverlay />
      <ModalContent bg="white" borderColor="gray.200" maxH="90vh">
        {estaImportando && <LoadingPadrao />}
        <ModalHeader borderTopRadius="md">
          <HStack spacing={3}>
            <VStack align="start" spacing={1}>
              <Text fontSize="18px" fontWeight="semibold" color="purple.500">
                Importação de produtos
              </Text>
              <Text fontSize="sm" color="gray.600" fontWeight="normal">
                Alguns produtos tiveram erros na importação. Você pode continuar
                a importação com os produtos válidos ou exportar uma planilha
                com os que falharam.
              </Text>
            </VStack>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody p="4px 24px 24px 24px">
          <VStack spacing={6} align="stretch">
            <HStack gap="12px" align="center">
              <Box
                bg="secondary.300"
                p={4}
                w="full"
                borderRadius="md"
                border="1px"
                borderColor="secondary.500"
              >
                <HStack>
                  <Text fontSize="sm" color="black" fontWeight="semibold">
                    {quantidadeProdutosComSucesso === 1
                      ? '1 produto válido'
                      : `${quantidadeProdutosComSucesso} produtos válidos`}{' '}
                  </Text>
                </HStack>
              </Box>
              <Box
                bg="red.300"
                p={4}
                w="full"
                borderRadius="md"
                border="1px"
                borderColor="red.500"
              >
                <HStack>
                  <Text fontSize="sm" color="white" fontWeight="semibold">
                    {produtosComErro.length === 1
                      ? '1 produto'
                      : `${produtosComErro.length} produtos`}{' '}
                    com erro
                  </Text>
                </HStack>
              </Box>
            </HStack>

            <Box minH="360px">
              <Box
                border="1px"
                borderColor="gray.200"
                borderRadius="md"
                bg="white"
              >
                <Box
                  bg="gray.600"
                  position="sticky"
                  top={0}
                  zIndex={10}
                  boxShadow="0 2px 4px rgba(0,0,0,0.1)"
                  display="flex"
                  alignItems="center"
                  py={3}
                  color="white"
                  fontWeight="semibold"
                  fontSize="sm"
                >
                  <Box width="80px" px={3} flexShrink={0}>
                    Linha
                  </Box>
                  <Box width="350px" px={3} flexShrink={0}>
                    Nome
                  </Box>
                  <Box width="120px" px={3} flexShrink={0}>
                    Preço Atual
                  </Box>
                  <Box width="120px" px={3} flexShrink={0}>
                    Preço Novo
                  </Box>
                  <Box flex={1} px={3}>
                    Motivo
                  </Box>
                </Box>

                {shouldVirtualize ? (
                  <Box
                    height={`${Math.min(
                      produtosComErro.length * ROW_HEIGHT,
                      MAX_HEIGHT
                    )}px`}
                  >
                    <AutoSizer>
                      {({ width, height }) => (
                        <List
                          width={width}
                          height={height}
                          rowCount={produtosComErro.length}
                          rowHeight={ROW_HEIGHT}
                          rowRenderer={renderTableRow}
                          style={{ outline: 'none' }}
                        />
                      )}
                    </AutoSizer>
                  </Box>
                ) : (
                  <Box>
                    {produtosComErro.map((produto, index) => (
                      <Box
                        key={index}
                        display="flex"
                        alignItems="center"
                        backgroundColor={index % 2 === 0 ? '#F7FAFC' : 'white'}
                        height="40px"
                        borderBottom="1px solid #E2E8F0"
                        padding="4px 0"
                        _hover={{ backgroundColor: '#EDF2F7' }}
                        cursor="pointer"
                      >
                        <Box width="80px" px={3} flexShrink={0}>
                          <Badge colorScheme="gray" variant="solid">
                            {produto.linha}
                          </Badge>
                        </Box>

                        <Box width="350px" px={3} flexShrink={0}>
                          <Text
                            fontSize="sm"
                            noOfLines={1}
                            overflow="hidden"
                            textOverflow="ellipsis"
                            whiteSpace="nowrap"
                            title={produto.nome || '-'}
                          >
                            {produto.nome || '-'}
                          </Text>
                        </Box>

                        <Box width="120px" px={3} flexShrink={0}>
                          <Text fontSize="sm">
                            {(() => {
                              const valor = normalizarValorMonetario(
                                produto.precoVendaAtual
                              );
                              return valor !== null
                                ? `${valor.toLocaleString('pt-BR', {
                                    minimumFractionDigits:
                                      casasDecimais.casasDecimaisValor,
                                    maximumFractionDigits:
                                      casasDecimais.casasDecimaisValor,
                                  })}`
                                : '-';
                            })()}
                          </Text>
                        </Box>

                        <Box width="120px" px={3} flexShrink={0}>
                          <Text fontSize="sm">
                            {(() => {
                              const valor = normalizarValorMonetario(
                                produto.precoVendaNovo
                              );
                              return valor !== null
                                ? `${valor.toLocaleString('pt-BR', {
                                    minimumFractionDigits:
                                      casasDecimais.casasDecimaisValor,
                                    maximumFractionDigits:
                                      casasDecimais.casasDecimaisValor,
                                  })}`
                                : '-';
                            })()}
                          </Text>
                        </Box>

                        <Box flex={1} px={3}>
                          <Text
                            fontSize="sm"
                            color="red.500"
                            fontWeight="semibold"
                          >
                            {produto.descricaoErro}
                          </Text>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                )}
              </Box>
            </Box>
          </VStack>
        </ModalBody>

        <ModalFooter justifyContent="center">
          <Button
            colorScheme="gray"
            variant="outlineDefault"
            mr={3}
            onClick={handleFecharModal}
            borderRadius="full"
            w="120px"
          >
            Fechar
          </Button>

          {produtosComErro && produtosComErro.length > 0 && (
            <Button
              leftIcon={<FiDownload />}
              colorScheme="gray"
              variant="outlineDefault"
              mr={3}
              borderRadius="full"
              onClick={manipularExportacaoErros}
            >
              Exportar produtos com erro
            </Button>
          )}

          {produtosComSucesso &&
            produtosComSucesso.length > 0 &&
            importarProdutosValidos && (
              <Button
                leftIcon={<FiCheck />}
                colorScheme={produtosValidosJaImportados ? 'gray' : 'secondary'}
                variant={
                  produtosValidosJaImportados ? 'outlineDefault' : 'solid'
                }
                borderRadius="full"
                onClick={handleImportacaoProdutosValidos}
                isLoading={estaImportando}
                isDisabled={produtosValidosJaImportados}
              >
                {produtosValidosJaImportados
                  ? `${
                      quantidadeProdutosComSucesso === 1
                        ? 'Produto importado'
                        : 'Produtos importados'
                    }`
                  : `Importar ${
                      quantidadeProdutosComSucesso === 1
                        ? 'produto'
                        : 'produtos'
                    }`}
              </Button>
            )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
