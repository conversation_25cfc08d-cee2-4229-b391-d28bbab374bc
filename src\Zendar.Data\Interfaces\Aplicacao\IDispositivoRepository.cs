﻿using Multiempresa.Shared.Helpers;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Helpers;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.ViewModels.Dispositivo;

namespace Zendar.Data.Interfaces.Aplicacao
{
    public interface IDispositivoRepository : IRepository<Dispositivo>
    {
        GridPaginadaRetorno<DispositivoPaginadoViewModel> ListarPaginado(
            GridPaginadaConsulta gridPaginada,
            string nome,
            Guid lojaId,
            ReferenciaServicoStargate referenciaServico,
            bool? ativo);

        Task<Guid> ObterContaFinanceiraPeloIdentificador(
            Guid lojaId,
            string identificador,
            ReferenciaServicoStargate referenciaServico);

        Task<int> ObterDispositivosAtivos(
            Guid lojaId,
            ReferenciaServicoStargate referenciaServico);

        Task<IList<Dispositivo>> ObterComContaFinanceiraParaAlteracao(
            params Guid[] dispositivosId);

        Task<int> ObterUltimoCodigoCadastrado(
            Guid lojaId);

        Task<bool> ValidarDispositivoLoja(
            Guid lojaId,
            string identificador,
            ReferenciaServicoStargate referenciaServico);

        Task<Guid> ObterIdDispositivo(
            Guid lojaId,
            string identificador,
            ReferenciaServicoStargate referenciaServico);

        Task<List<Guid>> ObterListaDispositivoPorLojaIdEReferenciaServicoStargate(
            Guid? lojaId,
            ReferenciaServicoStargate referenciaServico);

        Task<Dispositivo> ObterDispositivo(
            Guid lojaId,
            string identificador,
            string apelido,
            ReferenciaServicoStargate referenciaServico);
    }
}
